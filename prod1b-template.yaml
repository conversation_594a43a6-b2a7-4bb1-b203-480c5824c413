AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: Block Scholes Volatility Smile Calc

Resources:
  volSmileCalcFunction:
    Type: AWS::Serverless::Function # More info about Function Resource: https://github.com/awslabs/serverless-application-model/blob/master/versions/2016-10-31.md#awsserverlessfunction
    Properties:
      PackageType: Image
      CodeUri: ./api
      Timeout: 60
      MemorySize: 1769
      Policies:
      - arn:aws:iam::685767522279:policy/LambdaFunctionsDynamoDbPolicy-2
      - arn:aws:iam::685767522279:policy/LambdaFunctionsS3Policy-2
      Environment:
        Variables:
          LOG_LEVEL: INFO
    Metadata:
      Dockerfile: Dockerfile
      DockerContext: ./api
      DockerTag: latest
      DockerBuildArgs:
        DATAGRABBER: 685767522279.dkr.ecr.eu-west-1.amazonaws.com/datagrabber:4
        UTILS_AWS: 685767522279.dkr.ecr.eu-west-1.amazonaws.com/bs_python_utils_aws:2
        UTILS_GENERAL: 685767522279.dkr.ecr.eu-west-1.amazonaws.com/bs_python_utils_general:2

Outputs:
  volSmileCalcFunction:
    Description: "volSmileCalc Lambda Function ARN"
    Value: !GetAtt volSmileCalcFunction.Arn
  volSmileCalcFunctionIamRole:
    Description: "Implicit IAM Role created for volSmileCalc function"
    Value: !GetAtt volSmileCalcFunctionRole.Arn
  volSmileCalcFunctionLogGroupName:
    Description: "volSmileCalc Lambda Function name"
    Value: !Sub "/aws/lambda/${volSmileCalcFunction}"
    Export:
      Name: volSmileCalcFunctionLogGroupName
