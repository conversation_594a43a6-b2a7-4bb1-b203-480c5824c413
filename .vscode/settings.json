{
    "[python]": {
        "editor.defaultFormatter": "ms-python.black-formatter",
        "editor.formatOnSave": true,
        "editor.codeActionsOnSave": {
            "source.organizeImports": "explicit"
        },
    },
    "isort.args": [
        "--profile",
        "black"
    ],
    "python.testing.pytestArgs": [
        "api"
    ],
    "python.testing.unittestEnabled": false,
    "python.testing.pytestEnabled": true,
    "python.testing.autoTestDiscoverOnSaveEnabled": true,
    "python.pythonPath": ".venv/bin/python",
    "python.envFile": "${workspaceFolder}/.env"
}