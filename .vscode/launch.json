{"version": "0.2.0", "configurations": [{"name": "Python: Debug Tests", "type": "debugpy", "request": "launch", "program": "${file}", "purpose": ["debug-test"], "console": "integratedTerminal", "justMyCode": false}, {"name": "Python: flex stream", "type": "python", "request": "launch", "program": "api/run_stream_flex.py", "console": "integratedTerminal", "env": {"AWS_PROFILE": "staging"}, "justMyCode": false}, {"type": "aws-sam", "request": "direct-invoke", "name": "volSmileCalc - ABSOLUTE - STAGING", "invokeTarget": {"target": "template", "templatePath": "staging-template.yaml", "logicalId": "volSmileCalcFunction"}, "aws": {"credentials": "profile:staging"}, "sam": {"buildArguments": ["--parallel"]}, "lambda": {"runtime": "python3.8", "payload": {"json": {"body": {"calc": {"version": "1.0.0", "type": "volSmileCalc", "args": {"consistent_read": false, "exchanges": ["deribit", "bybit", "composite"], "currencies": ["BTC", "ETH"], "models": ["SABR", "SVI"], "types": ["params", "smile", "moneyness", "skew", "butterfly"], "frequency": {"interval": "hour", "periods": 1}, "date_range": {"absolute": {"start": "2022-04-01T00:00:00.000Z", "end": "2022-04-02T00:00:00.000Z"}, "time_condition": {}}, "tenor_mode": "standard", "smooth": true, "debug": false}, "output_options": {"version": "", "type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "blockscholes-test-staging", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}}}}, {"type": "aws-sam", "request": "direct-invoke", "name": "volSmileCalc - LATEST - STAGING", "invokeTarget": {"target": "template", "templatePath": "staging-template.yaml", "logicalId": "volSmileCalcFunction"}, "aws": {"credentials": "profile:staging"}, "sam": {"buildArguments": ["--parallel"]}, "lambda": {"runtime": "python3.8", "payload": {"json": {"body": {"calc": {"version": "1.0.0", "type": "volSmileCalc", "args": {"consistent_read": false, "exchanges": ["deribit", "bybit", "composite"], "currencies": ["BTC", "ETH"], "models": ["SABR", "SVI"], "types": ["params", "smile", "moneyness", "skew", "butterfly"], "frequency": {"interval": "hour", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "hour", "periods": 1}, "left_offset": 0, "right_offset": 0}, "time_condition": {}}, "tenor_mode": "standard", "smooth": true, "debug": false}, "output_options": {"version": "", "type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "blockscholes-test-staging", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}}}}, {"name": "volSmileCalc - LOCAL", "type": "debugpy", "request": "launch", "program": "api/app.py", "args": [], "justMyCode": false, "env": {"AWS_PROFILE": "prod"}}, {"name": "volSmileCalc - LOCAL BACKFILL_RUN - STAGING", "type": "python", "request": "launch", "program": "api/backfill_run.py", "args": ["-result_queue", "s3saver-4", "-events", "api/event.json", "-chunk_size", "1", "-target_table", "timeseries_data_staging"], "justMyCode": false, "env": {"AWS_PROFILE": "prod"}}]}