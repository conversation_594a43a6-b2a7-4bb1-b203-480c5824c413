import ast
import collections
import math
import re
from datetime import datetime, timezone

import pandas as pd

# Load the three log csvs (already in container)
stream_df = pd.read_csv("/mnt/data/Stream_output.csv")
spike_df = pd.read_csv("/mnt/data/Calibration_spikes.csv")
poor_df = pd.read_csv("/mnt/data/Poor_calibration.csv")


# Helper funcs
def parse_unix_ns(ns_str):
    """Parse nanoseconds-since-epoch string to datetime (UTC)."""
    try:
        ns_int = int(ns_str)
        return datetime.fromtimestamp(ns_int / 1e9, tz=timezone.utc)
    except Exception:
        return None


def parse_iso(iso_str):
    try:
        return datetime.fromisoformat(iso_str.replace("Z", "+00:00"))
    except Exception:
        return None


def expiry_seconds_from_now(
    expiry_token: str, snapshot_dt: datetime
) -> float | int | None:
    """
    Given an expiry token (e.g. '30m', '5d', '2025-07-25T08:00:00Z')  and snapshot datetime,
    return tenor seconds (>=0). Clamp negatives to zero.
    """
    if expiry_token.endswith("m"):
        try:
            minutes = float(expiry_token[:-1])
            sec = minutes * 60.0
        except ValueError:
            return None
    elif expiry_token.endswith("d"):
        try:
            days = float(expiry_token[:-1])
            sec = days * 86400.0
        except ValueError:
            return None
    else:
        # ISO timestamp
        exp_dt = parse_iso(expiry_token)
        if not exp_dt:
            return None
        sec = (exp_dt - snapshot_dt).total_seconds()
    if sec < 0:
        sec = 0.0
    return sec


# Bucket definitions (lower inclusive, upper exclusive)
bucket_defs = [
    ("0-15min", 0, 15 * 60),
    ("15-30min", 15 * 60, 30 * 60),
    ("30min-1h", 30 * 60, 60 * 60),
    ("1-4h", 60 * 60, 4 * 60 * 60),
    ("4h-1d", 4 * 60 * 60, 24 * 60 * 60),
    ("1-3d", 24 * 60 * 60, 3 * 24 * 60 * 60),
    ("3-7d", 3 * 24 * 60 * 60, 7 * 24 * 60 * 60),
    ("7-30d", 7 * 24 * 60 * 60, 30 * 24 * 60 * 60),
    ("30-90d", 30 * 24 * 60 * 60, 90 * 24 * 60 * 60),
    ("90-180d", 90 * 24 * 60 * 60, 180 * 24 * 60 * 60),
    (">180d", 180 * 24 * 60 * 60, math.inf),
]


def bucket_name(sec):
    for name, low, high in bucket_defs:
        if low <= sec < high:
            return name
    return None


# Initialise aggregations
bucket_counts = {}
for name, _, _ in bucket_defs:
    bucket_counts[name] = collections.Counter(
        {
            "total_params_output": 0,
            "total_params_missing": 0,
            "snapshots": 0,
            "total_spike_exclusions": 0,
            "total_r2_exclusions": 0,
        }
    )

# ---- Process stream outputs ----
timestamp_regex = re.compile(r"timestamp[:=]\s*([0-9]+)")
details_regex = re.compile(
    r"\{.*\}"
)  # naive; we'll use ast.literal_eval anyway

for _, row in stream_df.iterrows():
    msg = row["@message"]
    # snapshot time
    ts_match = timestamp_regex.search(msg)
    if not ts_match:
        continue
    snap_dt = parse_unix_ns(ts_match.group(1))
    if not snap_dt:
        continue

    # details dict
    m_det = details_regex.search(msg)
    if not m_det:
        continue
    det_text = m_det.group(0)
    try:
        det_obj = ast.literal_eval(det_text)
    except Exception:
        continue

    for exp_key, lst in det_obj.items():
        sec = expiry_seconds_from_now(exp_key.split(".")[-1], snap_dt)
        if sec is None:
            continue
        bname = bucket_name(sec)
        if not bname:
            continue

        bucket_counts[bname]["snapshots"] += 1
        if "params" in lst:
            bucket_counts[bname]["total_params_output"] += 1
        else:
            bucket_counts[bname]["total_params_missing"] += 1

# ---- Process spike logs ----
timestamp_spike_regex = re.compile(r"timestamp=([0-9]+)")
qualified_name_regex = re.compile(r"qualified_name=([^\s,]+)")

for msg in spike_df["@message"]:
    ts_match = timestamp_spike_regex.search(msg)
    qn_match = qualified_name_regex.search(msg)
    if not ts_match or not qn_match:
        continue
    snap_dt = parse_unix_ns(ts_match.group(1))
    if not snap_dt:
        continue
    qn = qn_match.group(1).strip("'\"")
    exp_token = qn.split(".")[-3]  # expiry position

    sec = expiry_seconds_from_now(exp_token, snap_dt)
    if sec is None:
        continue
    bname = bucket_name(sec)
    if not bname:
        continue
    bucket_counts[bname]["total_spike_exclusions"] += 1

# ---- Process poor-R2 logs ----
timestamps_list_regex = re.compile(r"timestamps=\[([^\]]+)\]")
qualified_name_r2_regex = re.compile(r"qualified_name='([^']+)'")

for msg in poor_df["@message"]:
    qn_match = qualified_name_r2_regex.search(msg)
    ts_list_match = timestamps_list_regex.search(msg)
    if not qn_match or not ts_list_match:
        continue
    qn = qn_match.group(1)
    # Extract the first ISO timestamp in the list
    ts_list_raw = ts_list_match.group(1)
    iso_match = re.search(
        r"([0-9]{4}-[0-9]{2}-[0-9]{2}T[0-9]{2}:[0-9]{2}:[0-9]{2}\.?[0-9]*Z)",
        ts_list_raw,
    )
    if not iso_match:
        continue
    iso_ts = iso_match.group(1)
    snap_dt = parse_iso(iso_ts)
    if not snap_dt:
        continue
    exp_token = qn.split(".")[-3]
    sec = expiry_seconds_from_now(exp_token, snap_dt)
    if sec is None:
        continue
    bname = bucket_name(sec)
    if not bname:
        continue
    bucket_counts[bname]["total_r2_exclusions"] += 1

# Convert aggregation to DataFrame
bucket_rows = []
for name in bucket_defs:
    bname = name[0]
    data = bucket_counts[bname]
    snap = data["snapshots"]
    missing = data["total_params_missing"]
    bucket_rows.append(
        {
            "tenor_bucket": bname,
            "snapshots": snap,
            "params_output": data["total_params_output"],
            "params_missing": missing,
            "missing_pct": round(missing / snap, 3) if snap else None,
            "spike_exclusions": data["total_spike_exclusions"],
            "r2_exclusions": data["total_r2_exclusions"],
        }
    )

bucket_df = pd.DataFrame(bucket_rows)
bucket_df = bucket_df.sort_values(
    "tenor_bucket", key=lambda col: [i for i, _ in enumerate(col)]
).reset_index(drop=True)

tools.display_dataframe_to_user(
    "Tenor-Bucket Issue Summary (2025-06-21 to 2025-06-22)", bucket_df
)
