# Load results from analysis; reconstruct quickly
import ast
import collections
import re
from collections import Counter, defaultdict

import ace_tools as tools
import pandas as pd


# helper functions
def extract_details(s):
    start = s.find("{")
    end = s.rfind("}")
    if start == -1 or end == -1 or end <= start:
        return None
    snippet = s[start : end + 1]
    snippet_clean = snippet.replace("...", "")
    try:
        obj = ast.literal_eval(snippet_clean)
        return obj
    except Exception:
        return None


def extract_qualified_name_spike(s):
    match = re.search(r"qualified_name=([^\s,]+)", s)
    if match:
        return match.group(1).strip("'\"")
    return None


def extract_qualified_name_r2(s):
    match = re.search(r"qualified_name='([^']+)'", s)
    if match:
        return match.group(1)
    match = re.search(r"qualified_name=([^\s,]+)", s)
    if match:
        return match.group(1).strip("'\"")
    return None


stream_df = pd.read_csv("/mnt/data/Stream_output.csv")
spike_df = pd.read_csv("/mnt/data/Calibration_spikes.csv")
poor_df = pd.read_csv("/mnt/data/Poor_calibration.csv")


params_counts = defaultdict(
    lambda: Counter({"total": 0, "params_present": 0, "params_missing": 0})
)
for _, row in stream_df.iterrows():
    details = extract_details(row["@message"])
    if not details:
        continue
    for key, lst in details.items():
        params_counts[key]["total"] += 1
        if "params" in lst:
            params_counts[key]["params_present"] += 1
        else:
            params_counts[key]["params_missing"] += 1
for key in params_counts:
    params_counts[key]["params_missing"] = (
        params_counts[key]["total"] - params_counts[key]["params_present"]
    )

spike_counts = collections.Counter()
for msg in spike_df["@message"]:
    qn = extract_qualified_name_spike(msg)
    if not qn:
        continue
    parts = qn.split(".")
    if len(parts) >= 5:
        currency = parts[2]
        expiry = parts[-3]
        key = f"{currency}.{expiry}"
        spike_counts[key] += 1

r2_counts = collections.Counter()
for msg in poor_df["@message"]:
    qn = extract_qualified_name_r2(msg)
    if not qn:
        continue
    parts = qn.split(".")
    if len(parts) >= 5:
        currency = parts[2]
        expiry = parts[-3]
        key = f"{currency}.{expiry}"
        r2_counts[key] += 1

rows = []
all_keys = (
    set(params_counts.keys()) | set(spike_counts.keys()) | set(r2_counts.keys())
)
for key in all_keys:
    pc = params_counts.get(key, Counter())
    total_snap = pc.get("total", 0)
    rows.append(
        {
            "qualified_name": key,
            "total_params_output": pc.get("params_present", 0),
            "total_params_missing": pc.get("params_missing", 0),
            "total_spike_exclusions": spike_counts.get(key, 0),
            "total_r2_exclusions": r2_counts.get(key, 0),
            "snapshots_with_expiry": total_snap,
            "missing_pct": (
                round(pc.get("params_missing", 0) / total_snap, 3)
                if total_snap
                else None
            ),
        }
    )

result_df = pd.DataFrame(rows)
result_df = result_df.sort_values(
    ["total_params_missing", "total_spike_exclusions", "total_r2_exclusions"],
    ascending=False,
).reset_index(drop=True)
tools.display_dataframe_to_user(
    "Expiry Issue Summary (2025-06-21 to 2025-06-22)", result_df
)
