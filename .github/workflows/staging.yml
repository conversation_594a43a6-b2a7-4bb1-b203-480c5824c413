name: Staging Function Deployment
on:
  workflow_dispatch:
    inputs:
      region:
        description: "AWS Region to deploy to"
        type: choice
        default: eu-west-2
        options:
          - eu-west-2
          - eu-west-1
      cpuArchitecture:
        description: "CPU architecture to deploy"
        type: choice
        default: ARM64
        options:
          - X86_64
          - ARM64
  push:
    branches:
      - main

jobs:
  deploy-function:
    uses: blockscholes/workflows/.github/workflows/lambda-function-deploy.yml@main
    name: Deploy VolSmile Function to Staging
    with:
      environment: staging
      repository: ${{ github.repository }}
      ref: ${{ github.ref }}
      cpuArchitecture: ${{ inputs.cpuArchitecture || 'ARM64' }}
      region: ${{ inputs.region || 'eu-west-2' }}
      template: staging-template.yaml
      python_version: 3.11
    secrets: inherit
