name: Python CI

on:
  pull_request:
    branches:
      - main
  workflow_call:

jobs:
  build:
    if: github.repository == 'blockscholes/volSmileCalc'
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [ "3.11" ]
    defaults:
      run:
        working-directory: ./api

    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v3
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install flake8
          pip install -r ./requirements-dev.txt
      - name: Format/lint with black/flake8
        run: |
          # stop the build if there are Python syntax errors or undefined names
          black . --check
          flake8 . --select=E9,F63,F7,F82 --show-source --statistics
      - name: Lint with ruff
        run: ruff .
      - name: Type check with mypy
        run: mypy .
      - name: Test with pytest
        run: pytest
