name: Prod1a Stream Deployment

on:
  workflow_dispatch:
    inputs:
      region:
        description: "AWS Region to deploy to"
        type: choice
        default: eu-west-2
        options:
          - eu-west-2
          - eu-west-1
      cpuArchitecture:
        description: "CPU architecture to deploy"
        type: choice
        default: ARM64
        options:
          - X86_64
          - ARM64
  pull_request:
    types:
      - closed
    branches:
      - production_stream


jobs:
  deploy:
    name: Deploy Stream VolSmileFlex to Production
    uses: blockscholes/workflows/.github/workflows/stream-deploy.yml@main
    with:
      environment: prod
      repository: ${{ github.repository }}
      ref: ${{ github.ref }}
      actor: ${{ github.actor }}
      cpuArchitecture: ${{ github.event.inputs.cpuArchitecture || 'ARM64' }}
      region: ${{ github.event.inputs.region || 'eu-west-2' }}
      ecr_repository: volsmilestream-prod1a
      ecs_service: volSmileFlexStream-service
      task_def: stream/task-definition-flex-prod1a.json
      container: volSmileFlexStream-container
      python_version: 3.11
    secrets: inherit
