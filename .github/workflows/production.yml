name: Production Function Deployment
on:
  workflow_dispatch:
    inputs:
      cpuArchitecture:
        description: "CPU architecture to deploy"
        type: choice
        default: ARM64
        options:
          - X86_64
          - ARM64

  pull_request:
    types:
      - closed
    branches:
      - production_function

jobs:
  deploy-function:
    name: Deploy VolSmile Function to Production
    uses: blockscholes/workflows/.github/workflows/lambda-function-deploy.yml@main
    with:
      environment: prod
      repository: ${{ github.repository }}
      ref: ${{ github.ref }}
      actor: ${{ github.actor }}
      cpuArchitecture: ${{ github.event.inputs.cpuArchitecture || 'ARM64' }}
      region: ${{ github.event.inputs.region || 'eu-west-2' }}
      template: prod-template.yaml
      python_version: 3.11
    secrets: inherit
