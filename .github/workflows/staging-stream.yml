name: Staging Stream Deployment

on:
  workflow_dispatch:
    inputs:
      region:
        description: "AWS Region to deploy to"
        type: choice
        default: eu-west-2
        options:
          - eu-west-2
          - eu-west-1
      cpuArchitecture:
        description: "CPU architecture to deploy"
        type: choice
        default: ARM64
        options:
          - X86_64
          - ARM64
  push:
    branches:
      - main


jobs:
  deploy:
    name: Deploy Stream VolSmileFlex to Staging
    uses: blockscholes/workflows/.github/workflows/stream-deploy.yml@main
    with:
      environment: staging
      repository: ${{ github.repository }}
      ref: ${{ github.ref }}
      cpuArchitecture: ${{ inputs.cpuArchitecture || 'ARM64' }}
      region: ${{ inputs.region || 'eu-west-2' }}
      ecr_repository: volsmilestream-staging
      ecs_service:  volSmileFlexStream-service
      task_def: stream/task-definition-flex-staging.json
      container:  volSmileFlexStream-container
      python_version: 3.11
    secrets: inherit
