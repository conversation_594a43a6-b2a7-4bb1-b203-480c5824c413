import asyncio
import logging
import time
from datetime import datetime, timezone
from typing import Any, Type

import utils_general
from block_stream import ConsumerSubscriptionManager
from block_stream.consumer.auto_scaling_cache_manager import (
    AutoScalingCacheManager,
)
from block_stream.consumer.cache import WaitForSetCalculationCache
from block_stream.utils.metrics import (
    MultiNumberMetric,
)
from constants import ECS_CLUSTER, ECS_SERVICE, SMOOTHING_CALC_TYPES
from stream_flex_function import ActiveFunction
from stream_types import SubscribeMessage

utils_general.setup_python_logger(logging.INFO)

expiry_metric = MultiNumberMetric(fields=["exchange"])


class FlexConsumerSubscriptionManager(
    ConsumerSubscriptionManager[ActiveFunction, SubscribeMessage]
):
    def __init__(
        self,
        calculator: str,
        FunctionClass: Type[ActiveFunction],
        watchdog_secs: int = 7200,
        time_to_remove_cache_secs: int = 0,  # remove from cache when unsubscribe
        auto_scaling_cache_manager: (
            AutoScalingCacheManager[str, WaitForSetCalculationCache] | None
        ) = None,
    ) -> None:
        super().__init__(
            calculator, FunctionClass, ECS_CLUSTER, ECS_SERVICE, watchdog_secs
        )
        self._target_jobs: list[str] = []
        self._fix_target_jobs: list[str] = []
        self._auto_scaling_cache_manager: (
            AutoScalingCacheManager[str, WaitForSetCalculationCache] | None
        ) = auto_scaling_cache_manager
        self._pending_to_remove: dict[str, float] = {}
        self._time_to_remove_cache_secs: int = time_to_remove_cache_secs

    def _get_fn_id(self, msg: SubscribeMessage) -> str:
        return f"{msg['exchange']}.{msg['base_asset']}.{msg['model']}"

    async def subscribe(self, msg: SubscribeMessage) -> None:
        internal_function_id = self._get_fn_id(msg)

        active_function = self.get_function(internal_function_id)
        if active_function:
            active_function.subscribe(msg)

            # ActiveFunctions that have no subscriptions _pending_to_remove for cleanup after
            # self._time_to_remove_cache_secs. If we receive a subscribe message that is handled by this
            # active function before self._time_to_remove_cache_secs need to un-queue this Active funtion
            # to be removed
            if internal_function_id in self._pending_to_remove:
                logging.info(
                    f"Subscription received for {internal_function_id=} that is pending removal. Deleting from "
                    f"pending removal map."
                )
                del self._pending_to_remove[internal_function_id]

        else:
            sub = ActiveFunction(command=msg)
            await self._add_function(internal_function_id, sub)
            await self._update_target_job(internal_function_id)

        utils_general.log_bsdebug(
            f"Subscription added for {msg['subscriber_id']} to {internal_function_id}",
        )

    async def unsubscribe(self, msg: SubscribeMessage) -> None:
        internal_function_id = self._get_fn_id(msg)

        function = self.get_function(internal_function_id)
        if not function:
            logging.warning(
                f"Unable to unsubscribe {msg['subscriber_id']}, internal function not found: {internal_function_id}"
            )
            return

        remain_expiries = function.unsubscribe(msg)
        if remain_expiries == 0:
            await self._remove_function(internal_function_id)

        logging.info(
            f"Unsubscribed: {msg['subscriber_id']} from {internal_function_id}"
        )

    async def on_delete_fn(self) -> Any:
        pass

    def get_all_functions(self) -> dict[str, ActiveFunction]:
        return self._active_functions.copy()

    async def expiry_watcher(self, refresh_interval: float = 3600) -> None:
        # Refresh aligned to the hour in an attempt to catch expiries on the hour
        next_iter: float = int(
            (time.time() // refresh_interval) * refresh_interval
        )
        while True:
            try:
                next_iter += (
                    refresh_interval / 4
                )  # Run every 15 mins in case there's a minutely expiry
                sleep_time = next_iter - time.time()
                if sleep_time > 0:
                    await asyncio.sleep(sleep_time)

                utils_general.log_bsdebug("Expiry watchdog iteration")
                fns = self.get_all_functions()
                # Copy into new list due to possible delete
                for function_id, fn in list(fns.items()):
                    expiries = fn.get_expiries()
                    for expiry in expiries:
                        if not utils_general.is_constant_maturity(
                            expiry
                        ) and utils_general.from_iso(
                            expiry.upper()
                        ) < datetime.now(
                            tz=timezone.utc
                        ):
                            remaining_expiries = fn.remove_expiry(expiry)

                            if remaining_expiries == 0:
                                await self._remove_function(function_id)
                                utils_general.log_bsdebug(
                                    f"Removed running function as all expiries were in the past {function_id}"
                                )
                                break

                remaining_fns = self.get_all_functions()
                # Avoids modifying dict while iterating
                for id, fn in list(remaining_fns.items()):
                    if fn.is_expired(refresh_s=self._watchdog_secs):
                        await self.delete_function(id)
                        logging.info(f"Removed expired function: {id}")

            except Exception as e:
                logging.exception(f"Expiry function watchdog failed: {e}")

    def get_run_vars(self, function_id: str) -> dict[str, Any]:
        func = self.get_function(function_id)
        if func is not None:
            # raw messages turned from MessageValues to ExpiryArbFields
            expiries = func.get_expiries_totals()
            tokens = function_id.split(".")
            expiry_metric.add(len(expiries), exchange=tokens[0])
            return {
                "calc_types": sorted(func.get_calc_types()),
                # exchange -> currency
                "subscribed_arbitrary_info": {tokens[0]: {tokens[1]: expiries}},
            }
        return {
            "calc_types": list(SMOOTHING_CALC_TYPES),
            "subscribed_arbitrary_info": None,
        }

    def get_target_jobs(self) -> list[str]:
        return self._target_jobs

    async def _remove_function(self, function_id: str) -> list[str]:
        if self._time_to_remove_cache_secs:
            # We only delete caches that are not in the fix_target_jobs
            if function_id not in self._fix_target_jobs:
                self._pending_to_remove[function_id] = time.time()
                utils_general.log_bsdebug(
                    f"Adding function '{function_id}' to pending removal"
                )
        else:
            await self.delete_function(function_id=function_id)
            if self._auto_scaling_cache_manager is not None:
                await self._auto_scaling_cache_manager.rebalance_caches()
        return self._target_jobs

    async def cleaning_target_jobs_watchdog(
        self, cleaning_interval_sec: int = 30
    ) -> None:
        # This watchdog checks every {cleaning_interval_sec} seconds for target_jobs
        # inside _pending_to_remove that need to be cleaned up.
        next_iter: float = int(
            (time.time() // cleaning_interval_sec) * cleaning_interval_sec
        )
        while True:
            try:
                next_iter += cleaning_interval_sec
                sleep_time = next_iter - time.time()
                if sleep_time > 0:
                    await asyncio.sleep(sleep_time)

                logging.info("cleaning target jobs...")

                function_ids_to_remove: list[str] = []
                for (
                    function_id,
                    last_subscription,
                ) in self._pending_to_remove.items():
                    if (
                        time.time() - last_subscription
                    ) > self._time_to_remove_cache_secs:
                        function_ids_to_remove.append(function_id)

                if function_ids_to_remove:
                    logging.info(
                        f"cleaning function_ids: {function_ids_to_remove}"
                    )
                    for function_id in function_ids_to_remove:
                        self._target_jobs.remove(function_id)
                        del self._pending_to_remove[function_id]
                        await self.delete_function(function_id=function_id)

                    if self._auto_scaling_cache_manager is not None:
                        await self._auto_scaling_cache_manager.rebalance_caches()

            except Exception as e:
                logging.exception(f"Cleaning target jobs watchdog failed: {e}")

    async def _update_target_job(self, function_id: str) -> None:
        if function_id not in self._target_jobs:
            # auto_scaling_cache_manager and this consumer share the same reference of target_jobs list
            self._target_jobs.append(function_id)
            if self._auto_scaling_cache_manager is not None:
                await self._auto_scaling_cache_manager.rebalance_caches()

            logging.info(
                f"Successfully added '{function_id}' to to target job list. target_jobs={self._target_jobs}"
            )

    def set_auto_scaling_cache_manager(
        self,
        auto_scaling_cache_manager: AutoScalingCacheManager[
            str, WaitForSetCalculationCache
        ],
        fix_target_jobs: list[str],
    ) -> None:
        self._auto_scaling_cache_manager = auto_scaling_cache_manager
        self._fix_target_jobs = fix_target_jobs

        # Cleaning old target jobs and init with the fixed one
        self._target_jobs.clear()
        self._target_jobs.extend(self._fix_target_jobs)
