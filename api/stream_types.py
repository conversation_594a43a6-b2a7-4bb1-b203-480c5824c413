from typing import Literal, TypedDict

from lambda_types import (
    CalcType,
)
from typing_extensions import NotRequired

MessageValueKeys = Literal["strikes", "deltas", "moneyness", "params"]

TYPES = Literal[
    "strikes", "deltas", "moneyness"
]  # the different keys a user is able to ask values for

StreamCalcTypes = Literal[
    "params", "smile", "moneyness", "strike"
]  # calc types that users are able to subscribe to


class StreamCalcTypeToMessageType(TypedDict):
    strike: Literal["strikes"]
    smile: Literal["deltas"]
    moneyness: Literal["moneyness"]


class SubscriberMessageValues(TypedDict, total=False):
    strikes: list[float | int]
    deltas: list[float | int]
    moneyness: list[float | int]
    params: bool


class FlexInternalMessageValues(TypedDict, total=False):
    strikes: set[float | int]
    deltas: set[float | int]
    moneyness: set[float | int]
    params: bool


class SubscribeMessage(TypedDict):
    command: str
    calculator: str
    function_id: str
    subscriber_id: str
    expiry: str
    type: CalcType
    values: NotRequired[SubscriberMessageValues]
    exchange: str
    model: str
    base_asset: str


class SingleSubscribeInfo(TypedDict):
    strikes: NotRequired[set[float]]
    deltas: NotRequired[set[float]]
    moneyness: NotRequired[set[float]]
    params: NotRequired[bool]


# strike -> set(sub_id)
IVTypeToSub = dict[float, set[str]]


class Totals(TypedDict):
    strikes: NotRequired[IVTypeToSub]
    deltas: NotRequired[IVTypeToSub]
    moneyness: NotRequired[IVTypeToSub]


class ExpirySubInfo(TypedDict):
    subscribers: dict[str, SingleSubscribeInfo]
    totals: Totals


class ListedParamsRecord(TypedDict):
    q: str
    t: int
    p: str
    intermediate: NotRequired[str]


Expiry = str
