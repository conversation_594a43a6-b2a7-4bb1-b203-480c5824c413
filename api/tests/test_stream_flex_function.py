from datetime import datetime, timedelta, timezone
from typing import Any, Callable, Optional, cast, get_args

import pytest
from stream_flex_function import ActiveFunction
from stream_types import StreamCalcTypes, SubscribeMessage
from utils_general import from_iso
from utils_general.aggregate_logger import setup_agg_logger

from tests.helpers import _get_arb_output_for_flex

from .helpers import sub_type_to_message_value_helper, sub_types_cast_helper
from .test_types import SubOverides, TestMessageTypes

SubFactory = Callable[[Optional[SubOverides]], SubscribeMessage]


@pytest.fixture
def iso_7d() -> str:
    return (datetime.now(tz=timezone.utc) + timedelta(days=7)).strftime(
        "%Y-%m-%dT%H:%M:%S.000Z"
    )


@pytest.fixture
def sub_message_factory(iso_7d: Optional[str]) -> SubFactory:
    def create_sub_message(
        args: Optional[SubOverides] = None,
    ) -> SubscribeMessage:
        setup_agg_logger()
        fields: dict[str, Any] = {
            "type": "params",
            "subscriber_id": "sub_1",
            "expiry": iso_7d,
            "command": "subscribe",
            "calculator": "volsmileflex",
            "function_id": "1",
        }
        if args is not None:
            for k, v in args.items():
                fields[k] = v
        return cast(SubscribeMessage, fields)

    return create_sub_message


class TestSubscribe:
    def test_init_fn(
        self, sub_message_factory: SubFactory, iso_7d: str
    ) -> None:
        init_sub_msg = sub_message_factory(None)
        fn = ActiveFunction(command=init_sub_msg)
        assert fn.get_expiries() == [iso_7d]
        assert fn.get_expiries_in_days(
            (from_iso(iso_7d) - timedelta(days=7)).timestamp() * 1e9,
        ) == {7: _get_arb_output_for_flex({"iso_expiry": iso_7d})}
        assert fn._expiry_subscriber_map[iso_7d]["subscribers"] == {
            "sub_1": {"params": True},
        }

    @pytest.mark.parametrize("sub_type", get_args(TestMessageTypes))
    def test_different_user_same_expiry(
        self,
        sub_type: TestMessageTypes,
        sub_message_factory: SubFactory,
        iso_7d: str,
    ) -> None:
        init_sub_msg = sub_message_factory(None)
        fn = ActiveFunction(command=init_sub_msg)

        message_value_type = sub_type_to_message_value_helper(sub_type)
        values = sub_types_cast_helper(sub_type, [1])
        msg = sub_message_factory(
            {"type": sub_type, "subscriber_id": "sub_2", "values": values}
        )
        fn.subscribe(message=msg)

        assert fn.get_expiries() == [iso_7d]
        base = _get_arb_output_for_flex({"iso_expiry": iso_7d})
        base.update(message_value_type, [1])

        assert fn.get_expiries_in_days(
            (from_iso(iso_7d) - timedelta(days=7)).timestamp() * 1e9,
        ) == {7: base}

        assert fn._expiry_subscriber_map[iso_7d]["subscribers"] == {
            "sub_1": {"params": True},
            "sub_2": {message_value_type: {1}},
        }

    def test_same_user_multiple_same_sub_type(
        self, sub_message_factory: SubFactory, iso_7d: str
    ) -> None:
        init_sub_msg = sub_message_factory(None)
        fn = ActiveFunction(command=init_sub_msg)
        strikes: list[float] = []
        message_value_type = sub_type_to_message_value_helper("strike")
        for i in range(1, 5):
            strikes.append(i)
            msg = sub_message_factory(
                {"type": "strike", "values": {"strikes": [i]}}
            )
            fn.subscribe(message=msg)

            assert fn.get_expiries() == [iso_7d]
            assert fn.get_expiries_in_days(
                (from_iso(iso_7d) - timedelta(days=7)).timestamp() * 1e9,
            ) == {
                7: _get_arb_output_for_flex(
                    {
                        "iso_expiry": iso_7d,
                        "strikes": strikes,
                    }
                )
            }
        assert fn._expiry_subscriber_map[iso_7d]["subscribers"] == {
            "sub_1": {"params": True, message_value_type: set(strikes)}
        }

    @pytest.mark.parametrize("sub_type", get_args(TestMessageTypes))
    def test_same_user_different_sub(
        self,
        sub_type: TestMessageTypes,
        sub_message_factory: SubFactory,
        iso_7d: str,
    ) -> None:
        init_sub_msg = sub_message_factory(None)
        fn = ActiveFunction(command=init_sub_msg)

        message_value_type = sub_type_to_message_value_helper(sub_type)

        values = sub_types_cast_helper(sub_type, [1])
        msg = sub_message_factory({"type": sub_type, "values": values})

        fn.subscribe(message=msg)
        assert fn.get_expiries() == [iso_7d]
        base = _get_arb_output_for_flex(
            {
                "iso_expiry": iso_7d,
            }
        )
        base.update(message_value_type, [1])

        assert fn.get_expiries_in_days(
            (from_iso(iso_7d) - timedelta(days=7)).timestamp() * 1e9,
        ) == {7: base}
        assert fn._expiry_subscriber_map[iso_7d]["subscribers"] == {
            "sub_1": {"params": True, message_value_type: {1}}
        }

    @pytest.mark.parametrize("sub_type", get_args(TestMessageTypes))
    def test_same_user_multiple_expiries_and_sub_type(
        self,
        sub_type: TestMessageTypes,
        sub_message_factory: SubFactory,
        iso_7d: str,
    ) -> None:
        init_sub_msg = sub_message_factory(None)
        fn = ActiveFunction(command=init_sub_msg)
        iso_14d = (from_iso(iso_7d) + timedelta(days=7)).strftime(
            "%Y-%m-%dT%H:%M:%S.000Z"
        )

        message_value_type = sub_type_to_message_value_helper(sub_type)
        values = sub_types_cast_helper(sub_type, [1.1])
        msg = sub_message_factory(
            {"type": sub_type, "expiry": iso_14d, "values": values}
        )

        fn.subscribe(message=msg)

        assert fn.get_expiries() == [iso_7d, iso_14d]
        assert fn.get_expiries_in_days(
            (from_iso(iso_7d) - timedelta(days=7)).timestamp() * 1e9,
        ) == {
            7: _get_arb_output_for_flex(
                {
                    "iso_expiry": iso_7d,
                }
            ),
            14: _get_arb_output_for_flex(
                {
                    "iso_expiry": iso_14d,
                    message_value_type: [1.1],
                }
            ),
        }

        assert fn._expiry_subscriber_map[iso_7d]["subscribers"] == {
            "sub_1": {"params": True}
        }
        assert fn._expiry_subscriber_map[iso_14d]["subscribers"] == {
            "sub_1": {message_value_type: {1.1}}
        }

    def test_same_user_same_message(
        self,
        sub_message_factory: SubFactory,
        iso_7d: str,
    ) -> None:
        init_sub_msg = sub_message_factory(
            {
                "type": "strike",
                "values": {"strikes": sorted([1000, 1500, 2000])},
            }
        )
        fn = ActiveFunction(command=init_sub_msg)
        fn.subscribe(init_sub_msg)
        fn.subscribe(init_sub_msg)

        assert fn.get_expiries() == [iso_7d]

        assert fn.get_expiries_in_days(
            (from_iso(iso_7d) - timedelta(days=7)).timestamp() * 1e9,
        ) == {
            7: _get_arb_output_for_flex(
                {
                    "iso_expiry": iso_7d,
                    "strikes": [1000, 1500, 2000],
                }
            ),
        }

        assert fn._expiry_subscriber_map[iso_7d]["subscribers"] == {
            "sub_1": {"strikes": {1000, 1500, 2000}}
        }

    def test_old_msg(
        self, sub_message_factory: SubFactory, iso_7d: str
    ) -> None:
        init_sub_msg = sub_message_factory(
            {
                "expiry": iso_7d,
                "exchange": "EXCHANGE",
                "base_asset": "BTC",
                "model": "SVI",
                "type": cast(TestMessageTypes, "params"),
                "subscriber_id": "sub_1",
            }
        )
        fn = ActiveFunction(command=init_sub_msg)
        assert fn.get_expiries() == [iso_7d]

        assert fn.get_expiries_in_days(
            (from_iso(iso_7d) - timedelta(days=7)).timestamp() * 1e9,
        ) == {
            7: _get_arb_output_for_flex({"iso_expiry": iso_7d}),
        }

    def test_same_user_arb_expiry_and_arb_maturity_sub(
        self,
        sub_message_factory: SubFactory,
        iso_7d: str,
    ) -> None:
        init_sub_msg = sub_message_factory(None)
        fn = ActiveFunction(command=init_sub_msg)
        msg = sub_message_factory({"expiry": "0d5d"})

        fn.subscribe(message=msg)
        assert fn.get_expiries() == [iso_7d, "0d5d"]

        assert fn.get_expiries_in_days(
            (from_iso(iso_7d) - timedelta(days=7)).timestamp() * 1e9,
        ) == {
            0.5: _get_arb_output_for_flex(
                {
                    "tenor_str": "0d5d",
                    "arbitrary_constant_maturity": True,
                    "arbitrary_expiry": False,
                }
            ),
            7.0: _get_arb_output_for_flex(
                {
                    "iso_expiry": iso_7d,
                    "arbitrary_constant_maturity": False,
                }
            ),
        }
        assert fn._expiry_subscriber_map[iso_7d]["subscribers"] == {
            "sub_1": {"params": True}
        }
        assert fn._expiry_subscriber_map["0d5d"]["subscribers"] == {
            "sub_1": {"params": True}
        }

    def test_multi_arb_maturity_sub(
        self,
        sub_message_factory: SubFactory,
        iso_7d: str,
    ) -> None:
        init_sub_msg = sub_message_factory({"expiry": "120m"})
        fn = ActiveFunction(command=init_sub_msg)
        msg = sub_message_factory({"expiry": "365d25d"})

        fn.subscribe(message=msg)
        assert fn.get_expiries() == ["120m", "365d25d"]

        assert fn.get_expiries_in_days(
            (from_iso(iso_7d) - timedelta(days=7)).timestamp() * 1e9,
        ) == {
            0.0833333: _get_arb_output_for_flex(
                {
                    "tenor_str": "120m",
                    "arbitrary_constant_maturity": True,
                    "arbitrary_expiry": False,
                }
            ),
            365.25: _get_arb_output_for_flex(
                {
                    "tenor_str": "365d25d",
                    "arbitrary_constant_maturity": True,
                    "arbitrary_expiry": False,
                }
            ),
        }

        assert fn._expiry_subscriber_map["120m"]["subscribers"] == {
            "sub_1": {"params": True}
        }
        assert fn._expiry_subscriber_map["365d25d"]["subscribers"] == {
            "sub_1": {"params": True}
        }


class TestUnsubscribe:
    def test_init_fn(
        self,
        sub_message_factory: SubFactory,
        iso_7d: str,
    ) -> None:
        init_sub_msg = sub_message_factory({"command": "unsubscribe"})
        fn = ActiveFunction(command=init_sub_msg)
        assert fn.get_expiries() == []
        assert (
            fn.get_expiries_in_days(
                (from_iso(iso_7d) - timedelta(days=7)).timestamp() * 1e9,
            )
            == {}
        )

    @pytest.mark.parametrize("sub_type", get_args(TestMessageTypes))
    def test_same_user_unsub(
        self,
        sub_type: TestMessageTypes,
        sub_message_factory: SubFactory,
        iso_7d: str,
    ) -> None:
        message_value_type = sub_type_to_message_value_helper(sub_type)
        values = sub_types_cast_helper(sub_type, [1, 2, 3])
        init_sub_msg = sub_message_factory({"type": sub_type, "values": values})
        fn = ActiveFunction(command=init_sub_msg)

        values = sub_types_cast_helper(sub_type, [1])
        msg = sub_message_factory(
            {"command": "unsubscibe", "type": sub_type, "values": values}
        )
        assert fn.unsubscribe(message=msg) == 1
        assert fn.get_expiries() == [iso_7d]

        assert fn.get_expiries_in_days(
            (from_iso(iso_7d) - timedelta(days=7)).timestamp() * 1e9,
        ) == {
            7: _get_arb_output_for_flex(
                {"iso_expiry": iso_7d, message_value_type: [2, 3]}
            )
        }

        assert fn._expiry_subscriber_map[iso_7d]["subscribers"] == {
            "sub_1": {message_value_type: {2, 3}}
        }

    def test_same_user_unsub_params(
        self,
        sub_message_factory: SubFactory,
        iso_7d: str,
    ) -> None:
        init_sub_msg = sub_message_factory(None)
        fn = ActiveFunction(command=init_sub_msg)
        msg = sub_message_factory({"command": "unsubscribe"})
        assert fn.unsubscribe(message=msg) == 0
        assert fn.get_expiries() == []

        assert (
            fn.get_expiries_in_days(
                (from_iso(iso_7d) - timedelta(days=7)).timestamp() * 1e9,
            )
            == {}
        )

        assert fn._expiry_subscriber_map[iso_7d]["subscribers"] == {}

    @pytest.mark.parametrize("sub_type", get_args(TestMessageTypes))
    def test_same_user_1_unsub_with_params(
        self,
        sub_type: TestMessageTypes,
        sub_message_factory: SubFactory,
        iso_7d: str,
    ) -> None:
        init_sub_msg = sub_message_factory(None)
        values = sub_types_cast_helper(sub_type, [1, 2, 3])
        sub_msg = sub_message_factory({"type": sub_type, "values": values})

        fn = ActiveFunction(command=init_sub_msg)
        fn.subscribe(sub_msg)

        message_value_type = sub_type_to_message_value_helper(sub_type)
        msg_values = sub_types_cast_helper(sub_type, [1])
        msg = sub_message_factory(
            {"command": "unsubscibe", "type": sub_type, "values": msg_values}
        )
        assert fn.unsubscribe(message=msg) == 1
        assert fn.get_expiries() == [iso_7d]
        assert fn.get_expiries_in_days(
            (from_iso(iso_7d) - timedelta(days=7)).timestamp() * 1e9,
        ) == {
            7: _get_arb_output_for_flex(
                {"iso_expiry": iso_7d, message_value_type: [2, 3]}
            )
        }
        assert (
            fn.unsubscribe(
                message=sub_message_factory(
                    {
                        "command": "unsubscibe",
                        "type": "params",
                    }
                )
            )
            == 1
        )

        assert fn.get_expiries_in_days(
            (from_iso(iso_7d) - timedelta(days=7)).timestamp() * 1e9,
        ) == {
            7: _get_arb_output_for_flex(
                {"iso_expiry": iso_7d, message_value_type: [2, 3]}
            )
        }

        assert fn._expiry_subscriber_map[iso_7d]["subscribers"] == {
            "sub_1": {message_value_type: {2, 3}}
        }

    @pytest.mark.parametrize("sub_type", get_args(TestMessageTypes))
    def test_different_user_same_expiry(
        self,
        sub_type: TestMessageTypes,
        sub_message_factory: SubFactory,
        iso_7d: str,
    ) -> None:
        message_value_type = sub_type_to_message_value_helper(sub_type)
        values = sub_types_cast_helper(sub_type, [1])
        sub_val: SubOverides = {"type": sub_type, "values": values}
        init_sub_msg = sub_message_factory(sub_val)
        second_sub_msg = sub_message_factory(
            {
                "subscriber_id": "sub_2",
                **sub_val,
            }
        )
        fn = ActiveFunction(command=init_sub_msg)
        fn.subscribe(second_sub_msg)
        assert len(fn._expiry_subscriber_map[iso_7d]["subscribers"]) == 2

        first_unsub_msg = sub_message_factory(
            {"command": "unsubscribe", **sub_val}
        )
        fn.unsubscribe(message=first_unsub_msg)

        assert fn.get_expiries() == [iso_7d]
        assert fn.get_expiries_in_days(
            (from_iso(iso_7d) - timedelta(days=7)).timestamp() * 1e9,
        ) == {
            7: _get_arb_output_for_flex(
                {"iso_expiry": iso_7d, message_value_type: [1]}
            )
        }

        assert fn._expiry_subscriber_map[iso_7d]["subscribers"] == {
            "sub_2": {message_value_type: {1}}
        }

    @pytest.mark.parametrize("sub_type", get_args(TestMessageTypes))
    def test_different_user_same_expiry_params(
        self,
        sub_type: TestMessageTypes,
        sub_message_factory: SubFactory,
        iso_7d: str,
    ) -> None:
        init_sub_msg = sub_message_factory(None)
        second_sub_msg = sub_message_factory({"subscriber_id": "sub_2"})
        fn = ActiveFunction(command=init_sub_msg)
        fn.subscribe(second_sub_msg)
        assert len(fn._expiry_subscriber_map[iso_7d]["subscribers"]) == 2

        first_unsub_msg = sub_message_factory({"command": "unsubscribe"})
        fn.unsubscribe(message=first_unsub_msg)

        assert fn.get_expiries() == [iso_7d]
        assert fn.get_expiries_in_days(
            (from_iso(iso_7d) - timedelta(days=7)).timestamp() * 1e9,
        ) == {
            7: _get_arb_output_for_flex(
                {
                    "iso_expiry": iso_7d,
                }
            )
        }

        assert fn._expiry_subscriber_map[iso_7d]["subscribers"] == {
            "sub_2": {"params": True}
        }

    def test_multiple_subs_and_unsubs(
        self,
        sub_message_factory: SubFactory,
        iso_7d: str,
    ) -> None:
        init_sub_msg = sub_message_factory(None)
        fn = ActiveFunction(command=init_sub_msg)

        strikes = []
        for i in range(1, 5):
            strikes.append(i)
            values = sub_types_cast_helper("strike", [i])
            msg = sub_message_factory({"type": "strike", "values": values})
            second_msg = sub_message_factory(
                {
                    "subscriber_id": "sub_2",
                    "type": "strike",
                    "values": values,
                }
            )
            fn.subscribe(message=msg)
            fn.subscribe(message=second_msg)

        values = sub_types_cast_helper("strike", [5, 6])
        fn.subscribe(
            message=sub_message_factory(
                {
                    "subscriber_id": "sub_3",
                    "type": "strike",
                    "values": values,
                }
            )
        )

        assert fn.get_expiries() == [iso_7d]

        values = sub_types_cast_helper("strike", [1, 2])
        fn.unsubscribe(
            sub_message_factory(
                {
                    "type": "strike",
                    "values": values,
                }
            )
        )

        values = sub_types_cast_helper(
            "strike", [2, 3]
        )  # overlap unsubscribe by one val (2)
        fn.unsubscribe(
            sub_message_factory(
                {
                    "subscriber_id": "sub_2",
                    "type": "strike",
                    "values": values,
                }
            )
        )
        values = sub_types_cast_helper("strike", [5])
        fn.unsubscribe(
            sub_message_factory(
                {
                    "subscriber_id": "sub_3",
                    "type": "strike",
                    "values": values,
                }
            )
        )

        assert fn.get_expiries() == [iso_7d]

        assert fn.get_expiries_in_days(
            (from_iso(iso_7d) - timedelta(days=7)).timestamp() * 1e9,
        ) == {
            7: _get_arb_output_for_flex(
                {
                    "iso_expiry": iso_7d,
                    "strikes": [1, 3, 4, 6],
                }
            )
        }

        assert fn._expiry_subscriber_map[iso_7d]["subscribers"] == {
            "sub_1": {"params": True, "strikes": {3, 4}},
            "sub_2": {"strikes": {1, 4}},
            "sub_3": {"strikes": {6}},
        }

    def test_old_msg(
        self, sub_message_factory: SubFactory, iso_7d: str
    ) -> None:
        init_sub_msg = sub_message_factory(
            {
                "expiry": iso_7d,
                "exchange": "EXCHANGE",
                "base_asset": "BTC",
                "model": "SVI",
                "type": cast(StreamCalcTypes, "params"),
                "subscriber_id": "sub_1",
            }
        )
        fn = ActiveFunction(command=init_sub_msg)
        fn.unsubscribe(
            sub_message_factory(
                {
                    "expiry": iso_7d,
                    "exchange": "EXCHANGE",
                    "base_asset": "BTC",
                    "model": "SVI",
                    "type": cast(StreamCalcTypes, "params"),
                    "subscriber_id": "sub_1",
                }
            )
        )
        assert (
            fn.get_expiries_in_days(
                (from_iso(iso_7d) - timedelta(days=7)).timestamp() * 1e9,
            )
            == {}
        )

    def test_different_user_same_arb_maturity_params(
        self,
        sub_message_factory: SubFactory,
        iso_7d: str,
    ) -> None:
        init_sub_msg = sub_message_factory({"expiry": "0d5d"})
        second_sub_msg = sub_message_factory(
            {"subscriber_id": "sub_2", "expiry": "0d5d"}
        )
        fn = ActiveFunction(command=init_sub_msg)
        fn.subscribe(second_sub_msg)
        assert len(fn._expiry_subscriber_map["0d5d"]["subscribers"]) == 2

        first_unsub_msg = sub_message_factory(
            {"command": "unsubscribe", "expiry": "0d5d"}
        )
        fn.unsubscribe(message=first_unsub_msg)

        assert fn.get_expiries() == ["0d5d"]

        assert fn.get_expiries_in_days(
            (from_iso(iso_7d) - timedelta(days=7)).timestamp() * 1e9,
        ) == {
            0.5: _get_arb_output_for_flex(
                {
                    "tenor_str": "0d5d",
                    "arbitrary_constant_maturity": True,
                    "arbitrary_expiry": False,
                }
            )
        }

        assert fn._expiry_subscriber_map["0d5d"]["subscribers"] == {
            "sub_2": {"params": True}
        }

    def test_same_user_same_arb_maturity_and_expiry_params(
        self,
        sub_message_factory: SubFactory,
        iso_7d: str,
    ) -> None:
        init_sub_msg = sub_message_factory(None)
        second_sub_msg = sub_message_factory({"expiry": "7d"})
        fn = ActiveFunction(command=init_sub_msg)
        fn.subscribe(second_sub_msg)
        assert len(fn._expiry_subscriber_map["7d"]["subscribers"]) == 1
        assert len(fn._expiry_subscriber_map[iso_7d]["subscribers"]) == 1
        assert fn.get_expiries() == [iso_7d, "7d"]

        assert fn.get_expiries_in_days(
            (from_iso(iso_7d) - timedelta(days=7)).timestamp() * 1e9,
        ) == {
            7.0: _get_arb_output_for_flex(
                {
                    "iso_expiry": iso_7d,
                    "tenor_str": "7d",
                    "arbitrary_constant_maturity": True,
                    "arbitrary_expiry": True,
                }
            )
        }

        first_unsub_msg = sub_message_factory(
            {"command": "unsubscribe", "expiry": "7d"}
        )
        fn.unsubscribe(message=first_unsub_msg)

        assert fn.get_expiries() == [iso_7d]

        assert fn.get_expiries_in_days(
            (from_iso(iso_7d) - timedelta(days=7)).timestamp() * 1e9,
        ) == {
            7.0: _get_arb_output_for_flex(
                {
                    "iso_expiry": iso_7d,
                    "arbitrary_constant_maturity": False,
                    "arbitrary_expiry": True,
                }
            )
        }

        assert fn._expiry_subscriber_map[iso_7d]["subscribers"] == {
            "sub_1": {"params": True}
        }
