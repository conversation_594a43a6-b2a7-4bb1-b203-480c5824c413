import json
import pathlib

import pandas as pd
import pytest
from lambda_types import (
    ExpiryArbFields,
    OptionContractDetails,
    TenorExpiryArbFields,
)
from pytest_mock import MockerFixture

from .test_types import SmileDerivedDataTestType

SampleData = tuple[
    dict[str, OptionContractDetails], dict[str, set[str]], set[int]
]


@pytest.fixture
def sample_smile_derived_data_values() -> SmileDerivedDataTestType:
    test_data_path = (
        pathlib.Path(__file__).parent.resolve()
        / "input_data/smile_derived_data"
    )
    data_types = {"timestamp": int, "tenor_days": float}

    with open(test_data_path / "expiry_iv_info.json") as f:
        expiry_iv_info_serialized = json.load(f)

    expiry_iv_info = TenorExpiryArbFields()
    for tenor in expiry_iv_info_serialized.keys():
        expiry_iv_info[float(tenor)] = ExpiryArbFields(
            strikes=set(expiry_iv_info_serialized[tenor]["strikes"]),
            moneyness=set(expiry_iv_info_serialized[tenor]["moneyness"]),
            deltas=set(expiry_iv_info_serialized[tenor]["deltas"]),
            listed_expiry=expiry_iv_info_serialized[tenor]["listed_expiry"],
            arbitrary_expiry=expiry_iv_info_serialized[tenor][
                "arbitrary_expiry"
            ],
        )

    return {
        "delta_vol_matrix_df": pd.read_json(
            test_data_path / "delta_vol_matix.json",
            dtype=data_types,  # type: ignore
        ),
        "expiry_iv_info": expiry_iv_info,
        "params_surface": pd.read_json(
            test_data_path / "params_surface.json",
            dtype=data_types,  # type: ignore
        ),
    }


@pytest.fixture
def sample_data() -> SampleData:
    instruments: dict[str, OptionContractDetails] = {
        "deribit.option.BTC-01JAN23-1-C": {
            "qualified_name": "deribit.option.contracts",
            "listing": "2022-11-01T00:00:00.000Z",
            "availableSince": "2022-11-01T00:00:00.000Z",
            "baseAsset": "BTC",
            "instrument_name": "BTC-01JAN23-1-C",
            "expiry": "2023-01-01T00:00:00.000Z",
            "strike": 1,
        },
        "deribit.option.BTC-01JAN23-2-C": {
            "qualified_name": "deribit.option.contracts",
            "listing": "2022-11-01T00:00:00.000Z",
            "availableSince": "2022-11-01T00:00:00.000Z",
            "baseAsset": "BTC",
            "instrument_name": "BTC-01JAN23-2-C",
            "expiry": "2023-01-01T00:00:00.000Z",
            "strike": 2,
        },
        "deribit.option.ETH-01JAN23-2500-C": {
            "qualified_name": "deribit.option.contracts",
            "listing": "2022-11-01T00:00:00.000Z",
            "availableSince": "2022-11-01T00:00:00.000Z",
            "baseAsset": "ETH",
            "expiry": "2023-01-01T00:00:00.000Z",
            "instrument_name": "ETH-01JAN23-2500-C",
            "strike": 2500,
        },
        "v2composite.option.BTC_2023-01-01T08:00:00Z_155_C": {
            "qualified_name": "v2composite.option.contracts",
            "listing": "2022-11-01T00:00:00.000Z",
            "availableSince": "2022-11-01T00:00:00.000Z",
            "baseAsset": "BTC",
            "expiry": "2023-01-02T00:00:00.000Z",
            "instrument_name": "BTC_2023-01-01T08:00:00Z_155_C",
            "strike": 155,
        },
    }
    exchange_map = {
        "deribit": {"deribit"},
        "v2composite": {"v2composite", "v2lyra-supercomposite"},
    }
    # 2022-11-01T00:00:00
    timestamps = {1667260800000000000}
    return instruments, exchange_map, timestamps


@pytest.fixture(autouse=True)
def mock_domestic_rates(mocker: MockerFixture) -> None:
    # Mock boto3 client
    mocker.patch(
        "boto3.Session.client",
        return_value=mocker.Mock(get_parameters=mocker.Mock()),
    )
    # Mock get_ssm_params
    mocker.patch(
        "utils_aws.utils_aws.get_ssm_params",
        return_value={
            "/data/interest-rates/domestic": {
                "28": 0.00,
                "56": 0.00,
                "91": 0.00,
                "182": 0.00,
                "365": 0.00,
            }
        },
    )
