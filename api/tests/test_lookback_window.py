import json
import math
import pathlib
from typing import Any, Literal, Optional, cast
from unittest import mock

import numpy as np
import pandas as pd
import pytest
import utils_general
from _pytest.python import Metafunc
from constants import LOOKBACK_COLUMN_NAME, MIN_SMOOTHING_LOOKBACK_RATIO
from lambda_types import (
    ClosestTenorInfo,
    ExchangeCurrencyToTimestampTenorExpiryArbFields,
    ExpiryArbFields,
    PreviousResults,
    SmoothingCalcType,
    SnapshotDates,
    TenorExpiryArbFields,
    TenorType,
    VolSurfaceCalcType,
)
from lookback_window import (
    get_cold_db_data,
    get_warm_cache_data,
    lookback_and_append,
    lookback_from_db,
    results_to_historic_qualified_names,
)
from utils.lookback import (
    determine_closest_tenor,
    extract_constant_tenors_from_maturities,
    extract_listed_params_from_df,
    filter_qualified_names,
    get_calc_type_qns_from_qualified_name_prefix,
    get_closest_listed_expiry,
    get_closest_tenor,
    get_qualified_name_prefixs_from_qns,
)
from utils_calc import TENORS_DAYS_STANDARD

from .helpers import compare_dataframes_row_by_row, set_snapshot_path_to_parent
from .test_types import GetColdDbTestData, LookbackAndAppendInputData

GLOB_DATA_TEST_PATH = pathlib.Path(__file__).parent.resolve()


@pytest.fixture()
def lookback_and_append_setup() -> tuple[PreviousResults, pd.DataFrame]:
    previous_results: PreviousResults = {"df": None}

    # fmt:off
    current_result = pd.DataFrame([
        {'tenor_days': 0.753472, 'forward': 48100.563531, 'sabr_alpha': 0.359665, 'sabr_rho': 0.274071,'sabr_volvol': 16.153131, 'atm_vol': 0.374286, 'expiry': 0.002064, 'R2_calib': np.nan, 'spot': 48084.44,'duplicate_listed_exp': False, 'listed_expiry': True, 'arbitrary_constant_maturity': False, 'standard_constant_maturity': False, 'arbitrary_expiry': False,'qualified_name': 'deribit.option.BTC.SABR.2024-02-12T08:00:00Z.1m.params', 'timestamp': 1707659700000000000,'isodate': '2024-02-11T13:55:00.000000Z', 'runtime': '2024-02-11T18:10:53.000Z', 'api_version': '1.0.0'},
        {'tenor_days': 1.0, 'forward': 48105.840146, 'sabr_alpha': 0.388401, 'sabr_rho': 0.213908,'sabr_volvol': 13.260606, 'atm_vol': 0.403216, 'expiry': 0.00274, 'R2_calib': 0.999992, 'spot': 48084.44,'duplicate_listed_exp': False, 'listed_expiry': False, 'arbitrary_constant_maturity': False, 'standard_constant_maturity': True, 'arbitrary_expiry': False,'qualified_name': 'deribit.option.BTC.SABR.1d.1m.params', 'timestamp': 1707659700000000000,'isodate': '2024-02-11T13:55:00.000000Z', 'runtime': '2024-02-11T18:10:53.000Z', 'api_version': '1.0.0'},
        {'tenor_days': 1.753472, 'forward': 48121.970853, 'sabr_alpha': 0.425194, 'sabr_rho': 0.104638,'sabr_volvol': 9.322864, 'atm_vol': 0.439958, 'expiry': 0.004804, 'R2_calib': np.nan, 'spot': 48084.44,'duplicate_listed_exp': False, 'listed_expiry': True, 'arbitrary_constant_maturity': False, 'standard_constant_maturity': False, 'arbitrary_expiry': False,'qualified_name': 'deribit.option.BTC.SABR.2024-02-13T08:00:00Z.1m.params', 'timestamp': 1707659700000000000,'isodate': '2024-02-11T13:55:00.000000Z', 'runtime': '2024-02-11T18:10:53.000Z', 'api_version': '1.0.0'},
        {'tenor_days': 2.0, 'forward': 48127.249816, 'sabr_alpha': 0.433412, 'sabr_rho': 0.116204, 'sabr_volvol': 8.532629,'atm_vol': 0.447784, 'expiry': 0.005479, 'R2_calib': 1.0, 'spot': 48084.44, 'duplicate_listed_exp': False,'listed_expiry': False, 'arbitrary_constant_maturity': False, 'standard_constant_maturity': True, 'arbitrary_expiry': False, 'qualified_name': 'deribit.option.BTC.SABR.2d.1m.params','timestamp': 1707659700000000000, 'isodate': '2024-02-11T13:55:00.000000Z', 'runtime': '2024-02-11T18:10:53.000Z','api_version': '1.0.0'},
        {'tenor_days': 2.753472, 'forward': 48143.387702, 'sabr_alpha': 0.449242, 'sabr_rho': 0.146139,'sabr_volvol': 6.931066, 'atm_vol': 0.46276, 'expiry': 0.007544, 'R2_calib': np.nan, 'spot': 48084.44,'duplicate_listed_exp': False, 'listed_expiry': True, 'arbitrary_constant_maturity': False, 'standard_constant_maturity': False, 'arbitrary_expiry': False,'qualified_name': 'deribit.option.BTC.SABR.2024-02-14T08:00:00Z.1m.params', 'timestamp': 1707659700000000000,'isodate': '2024-02-11T13:55:00.000000Z', 'runtime': '2024-02-11T18:10:53.000Z', 'api_version': '1.0.0'},
        {'tenor_days': 3.0, 'forward': 48148.669015, 'sabr_alpha': 0.446437, 'sabr_rho': 0.151275, 'sabr_volvol': 6.977164,'atm_vol': 0.461243, 'expiry': 0.008219, 'R2_calib': 1.0, 'spot': 48084.44, 'duplicate_listed_exp': False,'listed_expiry': False, 'arbitrary_constant_maturity': False, 'standard_constant_maturity': True, 'arbitrary_expiry': False, 'qualified_name': 'deribit.option.BTC.SABR.3d.1m.params','timestamp': 1707659700000000000, 'isodate': '2024-02-11T13:55:00.000000Z', 'runtime': '2024-02-11T18:10:53.000Z','api_version': '1.0.0'},
        {'tenor_days': 4.0, 'forward': 48170.097746, 'sabr_alpha': 0.437237, 'sabr_rho': 0.16521, 'sabr_volvol': 7.079609,'atm_vol': 0.457043, 'expiry': 0.010959, 'R2_calib': 1.0, 'spot': 48084.44, 'duplicate_listed_exp': False,'listed_expiry': False, 'arbitrary_constant_maturity': False, 'standard_constant_maturity': True, 'arbitrary_expiry': False, 'qualified_name': 'deribit.option.BTC.SABR.4d.1m.params','timestamp': 1707659700000000000, 'isodate': '2024-02-11T13:55:00.000000Z', 'runtime': '2024-02-11T18:10:53.000Z','api_version': '1.0.0'},
        {'tenor_days': 4.753472, 'forward': 48186.25, 'sabr_alpha': 0.431706, 'sabr_rho': 0.171637,'sabr_volvol': 7.106615, 'atm_vol': 0.455063, 'expiry': 0.013023, 'R2_calib': np.nan, 'spot': 48084.44,'duplicate_listed_exp': False, 'listed_expiry': True, 'arbitrary_constant_maturity': False, 'standard_constant_maturity': False, 'arbitrary_expiry': False,'qualified_name': 'deribit.option.BTC.SABR.2024-02-16T08:00:00Z.1m.params', 'timestamp': 1707659700000000000,'isodate': '2024-02-11T13:55:00.000000Z', 'runtime': '2024-02-11T18:10:53.000Z', 'api_version': '1.0.0'},
        {'tenor_days': 5.0, 'forward': 48190.602869, 'sabr_alpha': 0.431427, 'sabr_rho': 0.164354, 'sabr_volvol': 6.883802,'atm_vol': 0.45454, 'expiry': 0.013699, 'R2_calib': 1.0, 'spot': 48084.44, 'duplicate_listed_exp': False,'listed_expiry': False, 'arbitrary_constant_maturity': False, 'standard_constant_maturity': True, 'arbitrary_expiry': False, 'qualified_name': 'deribit.option.BTC.SABR.5d.1m.params','timestamp': 1707659700000000000, 'isodate': '2024-02-11T13:55:00.000000Z', 'runtime': '2024-02-11T18:10:53.000Z','api_version': '1.0.0'},
        {'tenor_days': 6.0, 'forward': 48208.263609, 'sabr_alpha': 0.43112, 'sabr_rho': 0.136963, 'sabr_volvol': 6.134668,'atm_vol': 0.453363, 'expiry': 0.016438, 'R2_calib': 0.999997, 'spot': 48084.44, 'duplicate_listed_exp': False,'listed_expiry': False, 'arbitrary_constant_maturity': False, 'standard_constant_maturity': True, 'arbitrary_expiry': False, 'qualified_name': 'deribit.option.BTC.SABR.6d.1m.params','timestamp': 1707659700000000000, 'isodate': '2024-02-11T13:55:00.000000Z', 'runtime': '2024-02-11T18:10:53.000Z','api_version': '1.0.0'}
      ])
    # fmt: on

    return previous_results, current_result


@pytest.fixture()
def previous_results_df() -> pd.DataFrame:
    "Fixture contains lookback data for standard, listed, and arbitrary expiries"
    warm_cache_path = (
        GLOB_DATA_TEST_PATH
        / "input_data/lookback_and_append/previous_results/mock_previous_results_df.json"
    )
    return pd.read_json(warm_cache_path, dtype={"timestamp": int})


@pytest.fixture()
def cold_db_test_fixture() -> GetColdDbTestData:
    # we use a smaller set of data for this test to avoid bloating up the results
    snapshot_dates: SnapshotDates = {
        "timestamps": [1715302740000000000, 1715302680000000000],
        "range": {
            "start": 1715302740000000000,
            "end": 1715302680000000000,
        },
    }
    expected_qualified_name_prefixs = [
        "deribit.option.BTC.SVI.2024-05-11T08:00:00Z.1m",
        "deribit.option.BTC.SVI.listed.1m",
        "deribit.option.BTC.SVI.3d.1m",
    ]
    historic_qualified_names = [
        "deribit.option.BTC.SVI.3d.1m.moneyness",
        "deribit.option.BTC.SVI.3d.1m.smile",
        "deribit.option.BTC.SVI.listed.1m.moneyness",
        "deribit.option.BTC.SVI.listed.1m.smile",
    ]
    test_qualified_names = [
        "deribit.option.BTC.SVI.2024-05-11T08:00:00Z.1m.params",
        "deribit.option.BTC.SVI.3d.1m.params",
    ]
    return {
        "snapshot_dates": snapshot_dates,
        "expected_qualified_name_prefixs": expected_qualified_name_prefixs,
        "historic_qualified_names": historic_qualified_names,
        "test_qualified_names": test_qualified_names,
    }


@pytest.fixture()
def mock_get_cold_db_data() -> tuple[list[str], pd.DataFrame]:
    test_data_path = (
        GLOB_DATA_TEST_PATH
        / "input_data/lookback_and_append/cold_db_lookup_test_data"
    )

    return (
        [
            "deribit.option.BTC.SVI.730d.1m",
            "deribit.option.BTC.SVI.7d.1m",
            "deribit.option.BTC.SVI.4d.1m",
            "deribit.option.BTC.SVI.3d.1m",
            "deribit.option.BTC.SVI.6d.1m",
            "deribit.option.BTC.SVI.2024-06-28T08:00:00Z.1m",
            "deribit.option.BTC.SVI.90d.1m",
            "deribit.option.BTC.SVI.2024-07-26T08:00:00Z.1m",
            "deribit.option.BTC.SVI.listed.1m",
            "deribit.option.BTC.SVI.2024-05-11T08:00:00Z.1m",
            "deribit.option.BTC.SVI.5d.1m",
        ],
        pd.read_json(
            test_data_path / "mock_get_cold_db_data.json",
            dtype={"timestamp": int},
        ),
    )


@pytest.fixture()
def standard_listed_and_arb_current_result() -> pd.DataFrame:
    test_data_path = (
        GLOB_DATA_TEST_PATH
        / "input_data/lookback_and_append/current_result_input_data"
    )
    json_files = list(test_data_path.glob("*.json"))
    current_result = pd.concat(
        [pd.read_json(file, dtype={"timestamp": int}) for file in json_files],
        ignore_index=True,
    )
    return current_result


@pytest.fixture()
def lookback_and_append_input_data() -> LookbackAndAppendInputData:
    end_str = "2024-05-10T01:00:00Z"
    calc_types: list[SmoothingCalcType] = ["smile", "moneyness"]
    interval = "minute"
    periods = 1
    freq = f"{periods}{utils_general.INTERVAL_TO_LETTER[interval]}"
    exchange_curr_timestamp_to_expiry_info = (
        ExchangeCurrencyToTimestampTenorExpiryArbFields()
    )
    tenor_expiry_arb_fields = TenorExpiryArbFields(
        {
            10.5: ExpiryArbFields(
                strikes=set(),
                deltas=set(),
                moneyness=set(),
                listed_expiry=False,
                arbitrary_expiry=True,
                iso_expiry="2024-05-20T13:00:00",
            ),
            15.5: ExpiryArbFields(
                strikes=set(),
                deltas=set(),
                moneyness=set(),
                listed_expiry=False,
                arbitrary_expiry=True,
                iso_expiry="2024-05-25T13:00:00",
            ),
            100.5: ExpiryArbFields(
                strikes=set(),
                deltas=set(),
                moneyness=set(),
                listed_expiry=False,
                arbitrary_expiry=True,
                iso_expiry="2024-08-18T13:00:00",
            ),
        }
    )

    exchange_curr_timestamp_to_expiry_info["deribit"]["BTC"][
        int(utils_general.from_iso(end_str).timestamp() * 1e9)
    ] = tenor_expiry_arb_fields

    return {
        "previous_results": {"df": None},
        "end_str": end_str,
        "interval": interval,
        "periods": periods,
        "freq": freq,
        "lookback_window_size": 10,
        "debug": False,
        "consistent_read": False,
        "exchange_curr_timestamp_to_expiry_info": exchange_curr_timestamp_to_expiry_info,
        "include_listed_expiries": True,
        "calc_types": calc_types,
        "scheduled_version": "",
    }


@pytest.fixture
def snapshot(snapshot: Any) -> Any:
    return set_snapshot_path_to_parent(snapshot)


# fmt: off
@mock.patch("lookback_window.retrieve_data_for_qns", return_value=pd.DataFrame(data=[
    [27219.79731,False,1.44729,120,"2023-09-18T22:05:36.000Z",26844.89000,1695074400000000000,"bybit.option.BTC.SABR.120d.1h.params",0.32877,0.38410,-0.00855,True],
    [27257.58219,False,1.44754,120,"2023-09-18T23:05:38.000Z",26880.40000,1695078000000000000,"bybit.option.BTC.SABR.120d.1h.params",0.32877,0.38408,-0.01088,True],
    [26880.37477,False,4.55653,14,"2023-09-18T22:05:36.000Z",26844.89000,1695074400000000000,"bybit.option.BTC.SABR.14d.1h.params",0.03836,0.28053,-0.11052,True],
    [26914.44120,False,4.56561,14,"2023-09-18T23:05:38.000Z",26880.40000,1695078000000000000,"bybit.option.BTC.SABR.14d.1h.params",0.03836,0.27990,-0.11696,True],
    [27407.31353,False,1.28608,180,"2023-09-18T22:05:36.000Z",26844.89000,1695074400000000000,"bybit.option.BTC.SABR.180d.1h.params",0.49315,0.37748,0.07507,True],
    [27443.94455,False,1.28854,180,"2023-09-18T23:05:38.000Z",26880.40000,1695078000000000000,"bybit.option.BTC.SABR.180d.1h.params",0.49315,0.37680,0.07136,True],
    [27691.01274,False,1.21151,270,"2023-09-18T22:05:36.000Z",26844.89000,1695074400000000000,"bybit.option.BTC.SABR.270d.1h.params",0.73973,0.36946,0.08704,True],
    [27725.87989,False,1.21411,270,"2023-09-18T23:05:38.000Z",26880.40000,1695078000000000000,"bybit.option.BTC.SABR.270d.1h.params",0.73973,0.36871,0.08315,True],
    [26930.39078,False,2.84759,30,"2023-09-18T22:05:36.000Z",26844.89000,1695074400000000000,"bybit.option.BTC.SABR.30d.1h.params",0.08219,0.32551,-0.05878,True],
    [26964.16166,False,2.84405,30,"2023-09-18T23:05:38.000Z",26880.40000,1695078000000000000,"bybit.option.BTC.SABR.30d.1h.params",0.08219,0.32477,-0.06340,True],
    [27993.65951,False,1.15363,365,"2023-09-18T22:05:36.000Z",26844.89000,1695074400000000000,"bybit.option.BTC.SABR.365d.1h.params",1.00000,0.36296,0.08691,True],
    [28026.62109,False,1.15606,365,"2023-09-18T23:05:38.000Z",26880.40000,1695078000000000000,"bybit.option.BTC.SABR.365d.1h.params",1.00000,0.36222,0.08310,True],
    [28582.73646,False,1.05581,547,"2023-09-18T22:05:36.000Z",26844.89000,1695074400000000000,"bybit.option.BTC.SABR.547d.1h.params",1.49863,0.35465,0.08660,True],
    [28611.91926,False,1.05797,547,"2023-09-18T23:05:38.000Z",26880.40000,1695078000000000000,"bybit.option.BTC.SABR.547d.1h.params",1.49863,0.35393,0.08295,True],
    [27027.02009,False,2.06971,60,"2023-09-18T22:05:36.000Z",26844.89000,1695074400000000000,"bybit.option.BTC.SABR.60d.1h.params",0.16438,0.35316,0.00448,True],
    [27062.19301,False,2.06398,60,"2023-09-18T23:05:38.000Z",26880.40000,1695078000000000000,"bybit.option.BTC.SABR.60d.1h.params",0.16438,0.35295,0.00284,True],
    [29187.54900,False,0.96880,730,"2023-09-18T22:05:36.000Z",26844.89000,1695074400000000000,"bybit.option.BTC.SABR.730d.1h.params",2.00000,0.35016,0.08622,True],
    [29212.75791,False,0.97073,730,"2023-09-18T23:05:38.000Z",26880.40000,1695078000000000000,"bybit.option.BTC.SABR.730d.1h.params",2.00000,0.34946,0.08274,True],
    [26857.37839,False,6.92172,7,"2023-09-18T22:05:36.000Z",26844.89000,1695074400000000000,"bybit.option.BTC.SABR.7d.1h.params",0.01918,0.27412,-0.09397,True],
    [26892.23391,False,6.94817,7,"2023-09-18T23:05:38.000Z",26880.40000,1695078000000000000,"bybit.option.BTC.SABR.7d.1h.params",0.01918,0.27398,-0.10449,True],
    [27124.73648,False,1.64383,90,"2023-09-18T22:05:36.000Z",26844.89000,1695074400000000000,"bybit.option.BTC.SABR.90d.1h.params",0.24658,0.37929,-0.03217,True],
    [27162.06540,False,1.64144,90,"2023-09-18T23:05:38.000Z",26880.40000,1695078000000000000,"bybit.option.BTC.SABR.90d.1h.params",0.24658,0.37955,-0.03392,True],
],
    columns=['forward', 'smoothed', 'sabr_volvol', 'tenor_days', 'runtime', 'spot', 'timestamp', 'qualified_name', 'expiry', 'sabr_alpha', 'sabr_rho', 'from_lookback']),
            )
# fmt: on
def test_lookback_from_db(mock_object: mock.MagicMock) -> None:
    df = lookback_from_db(
        snapshot_dates={
            "range": {"end": 1695078000000000000, "start": 1695074400000000000}
        },
        qualified_names=[
            "bybit.option.BTC.SABR.7d.1h.params",
            "bybit.option.BTC.SABR.14d.1h.params",
            "bybit.option.BTC.SABR.30d.1h.params",
            "bybit.option.BTC.SABR.60d.1h.params",
            "bybit.option.BTC.SABR.90d.1h.params",
            "bybit.option.BTC.SABR.120d.1h.params",
            "bybit.option.BTC.SABR.180d.1h.params",
            "bybit.option.BTC.SABR.270d.1h.params",
            "bybit.option.BTC.SABR.365d.1h.params",
            "bybit.option.BTC.SABR.547d.1h.params",
            "bybit.option.BTC.SABR.730d.1h.params",
        ],
        debug=False,
        consistent_read=False,
    )
    mock_object.assert_called_once_with(
        snapshot_dates={
            "range": {"end": 1695078000000000000, "start": 1695074400000000000}
        },
        qualified_names=[
            "bybit.option.BTC.SABR.7d.1h.params",
            "bybit.option.BTC.SABR.14d.1h.params",
            "bybit.option.BTC.SABR.30d.1h.params",
            "bybit.option.BTC.SABR.60d.1h.params",
            "bybit.option.BTC.SABR.90d.1h.params",
            "bybit.option.BTC.SABR.120d.1h.params",
            "bybit.option.BTC.SABR.180d.1h.params",
            "bybit.option.BTC.SABR.270d.1h.params",
            "bybit.option.BTC.SABR.365d.1h.params",
            "bybit.option.BTC.SABR.547d.1h.params",
            "bybit.option.BTC.SABR.730d.1h.params",
        ],
        debug=False,
        consistent_read=False,
    )
    assert list(df.columns) == [
        "forward",
        "smoothed",
        "sabr_volvol",
        "tenor_days",
        "runtime",
        "spot",
        "timestamp",
        "qualified_name",
        "expiry",
        "sabr_alpha",
        "sabr_rho",
        "from_lookback",
    ]

    assert list(df.index.unique()) == list(range(22))


def test_lookback_and_append_no_available_lookback(
    mocker: mock.MagicMock,
    lookback_and_append_setup: tuple[PreviousResults, pd.DataFrame],
) -> None:
    previous_results, current_result = lookback_and_append_setup
    end_str = "2024-02-11T13:55:00Z"
    interval = "minute"
    periods = 1
    freq = f"{periods}{utils_general.INTERVAL_TO_LETTER[interval]}"
    calc_types: list[SmoothingCalcType] = ["smile", "moneyness"]
    lookback_window_size = 10
    include_listed_expiries = True
    expected_result = current_result.copy()
    expected_result.loc[:, LOOKBACK_COLUMN_NAME] = False
    expected_result.loc[:, "qualified_name_prefix"] = (
        get_qualified_name_prefixs_from_qns(expected_result["qualified_name"])
    )
    expected_result.sort_index(axis=1, inplace=True)

    mock_db_lookup = mocker.patch(
        "lookback_window.lookback_from_db", return_value=pd.DataFrame()
    )  # mock empty dataframe returned from db
    mock_extract_listed_expiries = mocker.patch(
        "lookback_window.extract_listed_params_from_df",
        return_value=pd.DataFrame(),
    )
    result = lookback_and_append(
        previous_results=previous_results,
        current_result=current_result,
        end_str=end_str,
        interval=interval,
        freq=freq,
        lookback_window_size=lookback_window_size,
        include_listed_expiries=include_listed_expiries,
        calc_types=calc_types,
    ).sort_index(axis=1)

    mock_db_lookup.assert_called_with(
        consistent_read=False,
        debug=False,
        qualified_names=sorted(
            [
                "deribit.option.BTC.SABR.4d.1m.smile",
                "deribit.option.BTC.SABR.4d.1m.moneyness",
                "deribit.option.BTC.SABR.2d.1m.smile",
                "deribit.option.BTC.SABR.2d.1m.moneyness",
                "deribit.option.BTC.SABR.5d.1m.smile",
                "deribit.option.BTC.SABR.5d.1m.moneyness",
                "deribit.option.BTC.SABR.1d.1m.smile",
                "deribit.option.BTC.SABR.1d.1m.moneyness",
                "deribit.option.BTC.SABR.listed.1m.smile",
                "deribit.option.BTC.SABR.listed.1m.moneyness",
                "deribit.option.BTC.SABR.6d.1m.smile",
                "deribit.option.BTC.SABR.6d.1m.moneyness",
                "deribit.option.BTC.SABR.3d.1m.smile",
                "deribit.option.BTC.SABR.3d.1m.moneyness",
            ]
        ),
        snapshot_dates={
            "range": {"end": 1707659640000000000, "start": 1707659100000000000}
        },
    )
    mock_extract_listed_expiries.assert_not_called()  # assert that we don't try to extract listed expiries
    compare_dataframes_row_by_row(
        result.reset_index().sort_index(axis=1),
        expected_result.sort_index(axis=1),
    )


@pytest.mark.parametrize(
    "tenor_days, qualified_names, tenor_to_compare, expected_result",
    [
        ([], [], 30, (math.inf, "")),
        ([10, 20, 30], ["expiry1", "expiry2", "expiry3"], 20, (20, "expiry2")),
        ([10, 25, 40], ["expiry1", "expiry2", "expiry3"], 30, (25, "expiry2")),
        ([15], ["single_expiry"], 14, (15, "single_expiry")),
    ],
)
def test_get_closest_listed_expiry(
    tenor_days: list[float],
    qualified_names: list[str],
    tenor_to_compare: float,
    expected_result: tuple[float, str],
) -> None:
    data = {"tenor_days": tenor_days, "qualified_name": qualified_names}
    df = pd.DataFrame(data)

    result = get_closest_listed_expiry(df, tenor_to_compare)

    assert (
        result == expected_result
    ), f"Expected {expected_result}, got {result}"


@pytest.mark.parametrize(
    "tenor_days, expected",
    [
        (25, 30),
        (45, 30),
        (75, 60),
        (169.9, 180),
        (185, 180),
        (
            105,
            90,
        ),  # equidistant from 90 and 120, function should return min() of the 2
        (115, 120),
    ],
)
def test_get_closest_tenor(tenor_days: float | int, expected: int) -> None:
    """This test implicitly looks in the TENORS_DAYS_STANDARD variable"""
    assert (
        get_closest_tenor(tenor_days=tenor_days, tenors=TENORS_DAYS_STANDARD)  # type: ignore
        == expected
    )


def test_extract_listed_params_from_df() -> None:
    input_df = pd.DataFrame(
        [
            {
                "qualified_name": "deribit.option.BTC.SVI.listed.1m.moneyness",
                "moneyness": "[{'from_lookback': true, 'arbitrary_expiry': false, '100money': 0.7}]",
                "smile": None,
                "timestamp": 4,
            },
            {
                "qualified_name": "deribit.option.BTC.SVI.listed.1m.smile",
                "moneyness": None,
                "smile": "[{'from_lookback': true, 'arbitrary_expiry': false, 'atm': 0.7}]",
                "timestamp": 5,
            },
        ]
    )
    calc_types: list[SmoothingCalcType] = ["moneyness", "smile"]

    expected_df = pd.DataFrame(
        [
            # fmt: off
            {'100money': 0.7, 'arbitrary_expiry': False, 'listed_expiry': True, 'standard_constant_maturity': False, 'arbitrary_constant_maturity': False, 'atm': np.nan, 'from_lookback': True, 'timestamp': 4},
            {'100money': np.nan, 'arbitrary_expiry': False, 'listed_expiry': True, 'standard_constant_maturity': False, 'arbitrary_constant_maturity': False, 'atm': 0.7, 'from_lookback': True, 'timestamp': 5}
            # fmt: on
        ]
    )

    output_df = extract_listed_params_from_df(input_df, calc_types)

    compare_dataframes_row_by_row(
        output_df.sort_index(axis=1),
        expected_df.sort_index(axis=1),
    )


@pytest.mark.parametrize(
    "calc_types, qualified_name_prefixs, expected_output",
    [
        (
            ["smile"],
            ["deribit.option.BTC.SVI.2023-05-23T08:00:00Z.1m"],
            ["deribit.option.BTC.SVI.2023-05-23T08:00:00Z.1m.smile"],
        ),
        (
            ["smile", "moneyness"],
            [
                "deribit.option.BTC.SVI.2023-05-23T08:00:00Z.1m",
                "deribit.option.BTC.SVI.7d.1m",
            ],
            [
                "deribit.option.BTC.SVI.2023-05-23T08:00:00Z.1m.smile",
                "deribit.option.BTC.SVI.2023-05-23T08:00:00Z.1m.moneyness",
                "deribit.option.BTC.SVI.7d.1m.smile",
                "deribit.option.BTC.SVI.7d.1m.moneyness",
            ],
        ),
    ],
)
def test_get_qns_from_qualified_name_prefix(
    calc_types: list[SmoothingCalcType],
    qualified_name_prefixs: list[str],
    expected_output: list[str],
) -> None:
    assert (
        get_calc_type_qns_from_qualified_name_prefix(
            calc_types, qualified_name_prefixs
        )
        == expected_output
    )


@pytest.mark.parametrize(
    "target_tenor, expected_result",
    [
        # tests for closest listed expiry
        (
            0.026027397260273973,
            {
                "closest_qualified_name": "v-00002.deribit.option.BTC.SVI.2024-04-09T08:00:00Z.1m.smile",
                "closest_tenor": 0.820138888889,
                "is_constant_tenor": False,
            },
        ),
        # tests for closest standard tenor
        (
            6.7555555,
            {
                "closest_qualified_name": "v-00002.deribit.option.BTC.SVI.7d.1m.smile",
                "closest_tenor": 7,
                "is_constant_tenor": True,
            },
        ),
        (
            350,
            {
                "closest_qualified_name": "v-00002.deribit.option.BTC.SVI.2025-03-28T08:00:00Z.1m.smile",
                "closest_tenor": 353.820138888889,
                "is_constant_tenor": False,
            },
        ),
    ],
)
def test_determine_closest_tenor(
    target_tenor: int | float, expected_result: ClosestTenorInfo
) -> None:
    """This test also implicitly looks for tenors in the TENORS_DAYS_STANDARD variable"""
    listed_expiry_df = pd.DataFrame(
        [
            # fmt: off
            {'tenor_days': 0.820138888889,
             'qualified_name': 'v-00002.deribit.option.BTC.SVI.2024-04-09T08:00:00Z.1m.smile'},
            {'tenor_days': 1.820138888889,
             'qualified_name': 'v-00002.deribit.option.BTC.SVI.2024-04-10T08:00:00Z.1m.smile'},
            {'tenor_days': 2.820138888889,
             'qualified_name': 'v-00002.deribit.option.BTC.SVI.2024-04-11T08:00:00Z.1m.smile'},
            {'tenor_days': 3.820138888889,
             'qualified_name': 'v-00002.deribit.option.BTC.SVI.2024-04-12T08:00:00Z.1m.smile'},
            {'tenor_days': 10.820138888889,
             'qualified_name': 'v-00002.deribit.option.BTC.SVI.2024-04-19T08:00:00Z.1m.smile'},
            {'tenor_days': 17.820138888889,
             'qualified_name': 'v-00002.deribit.option.BTC.SVI.2024-04-26T08:00:00Z.1m.smile'},
            {'tenor_days': 52.820138888889,
             'qualified_name': 'v-00002.deribit.option.BTC.SVI.2024-05-31T08:00:00Z.1m.smile'},
            {'tenor_days': 80.820138888889,
             'qualified_name': 'v-00002.deribit.option.BTC.SVI.2024-06-28T08:00:00Z.1m.smile'},
            {'tenor_days': 171.820138888889,
             'qualified_name': 'v-00002.deribit.option.BTC.SVI.2024-09-27T08:00:00Z.1m.smile'},
            {'tenor_days': 262.820138888889,
             'qualified_name': 'v-00002.deribit.option.BTC.SVI.2024-12-27T08:00:00Z.1m.smile'},
            {'tenor_days': 353.820138888889,
             'qualified_name': 'v-00002.deribit.option.BTC.SVI.2025-03-28T08:00:00Z.1m.smile'}
            # fmt: on
        ]
    )
    version, qn_tokens = utils_general.get_qfn_and_version(
        listed_expiry_df.iloc[0]["qualified_name"]
    )
    result = determine_closest_tenor(
        target_tenor_days=target_tenor,
        listed_expiry_df=listed_expiry_df,
        constant_tenors=TENORS_DAYS_STANDARD,  # type: ignore
        exchange=qn_tokens[0],
        instrument=qn_tokens[1],
        currency=qn_tokens[2],
        model=qn_tokens[3],
        calc_type=cast(VolSurfaceCalcType, qn_tokens[-1]),
        freq=qn_tokens[-2],
        scheduled_version=version,
    )
    assert result == expected_result


def test_determine_closest_tenor_with_empty_dataframe() -> None:
    """Test the _determine_closest_tenor function with an empty DataFrame to ensure it handles no data gracefully."""
    empty_df = pd.DataFrame(columns=["tenor_days", "qualified_name"])

    target_tenor_days = 1.0
    qualified_name = "deribit.option.BTC.SVI.DUMMY_TENOR.1m.params"
    version, qn_tokens = utils_general.get_qfn_and_version(qualified_name)

    result = determine_closest_tenor(
        target_tenor_days=target_tenor_days,
        listed_expiry_df=empty_df,
        constant_tenors=TENORS_DAYS_STANDARD,  # type: ignore
        exchange=qn_tokens[0],
        instrument=qn_tokens[1],
        currency=qn_tokens[2],
        model=qn_tokens[3],
        calc_type=cast(VolSurfaceCalcType, qn_tokens[-1]),
        freq=qn_tokens[-2],
        scheduled_version=version,
    )

    expected_result = {
        "closest_qualified_name": "deribit.option.BTC.SVI.1d.1m.params",
        "closest_tenor": 1,
        "is_constant_tenor": True,
    }

    assert result == expected_result


class TestGetSmoothingKey:
    def test_get_qualified_name_prefixs_from_qns_pandas_series(self) -> None:
        input_data = pd.Series(
            [
                "v-00002.deribit.option.BTC.SVI.2024-04-09T08:00:00Z.1m.smile",
                "v-00002.deribit.option.BTC.SVI.2024-04-09T08:00:00Z.1m.moneyness",
                "v-00002.deribit.option.BTC.SVI.2024-04-09T08:00:00Z.1m.params",
            ],
            name="qualified_name",
        )
        expected_result = pd.Series(
            [
                "v-00002.deribit.option.BTC.SVI.2024-04-09T08:00:00Z.1m",
                "v-00002.deribit.option.BTC.SVI.2024-04-09T08:00:00Z.1m",
                "v-00002.deribit.option.BTC.SVI.2024-04-09T08:00:00Z.1m",
            ],
            name="qualified_name",
        )
        result = get_qualified_name_prefixs_from_qns(input_data)
        pd.testing.assert_series_equal(result, expected_result)

    def test_get_qualified_name_prefixs_from_qns_list(self) -> None:
        input_data = [
            "v-00002.deribit.option.BTC.SVI.2024-04-09T08:00:00Z.1m.smile",
            "v-00002.deribit.option.BTC.SVI.2024-04-09T08:00:00Z.1m.moneyness",
            "v-00002.deribit.option.BTC.SVI.2024-04-09T08:00:00Z.1m.params",
        ]
        expected_output = [
            "v-00002.deribit.option.BTC.SVI.2024-04-09T08:00:00Z.1m",
        ]
        result = get_qualified_name_prefixs_from_qns(input_data)
        assert (
            result == expected_output
        ), "The output list does not match the expected list."


@pytest.mark.parametrize(
    "input_data, threshold, filter_condition, expected_qualified_names",
    [
        # Tests for 'below_threshold' filter condition
        # Case 1: All groups are below the threshold
        (
            [
                {"qualified_name": "expiry1", "data": 10},
                {"qualified_name": "expiry1", "data": 20},
                {"qualified_name": "expiry2", "data": 30},
            ],
            3,
            "below_threshold",
            {"expiry1", "expiry2"},
        ),
        # Case 2: One group is above the threshold
        (
            [
                {"qualified_name": "expiry1", "data": 10},
                {"qualified_name": "expiry1", "data": 20},
                {"qualified_name": "expiry2", "data": 30},
                {"qualified_name": "expiry2", "data": 40},
                {"qualified_name": "expiry2", "data": 50},
            ],
            3,
            "below_threshold",
            {"expiry1"},
        ),
        # Tests for 'above_or_equal_threshold' filter condition
        # Case 3: Multiple groups, one with sufficient data
        (
            [
                {"qualified_name": "expiry1", "data": 1},
                {"qualified_name": "expiry2", "data": 2},
                {"qualified_name": "expiry2", "data": 3},
            ],
            2,  # threshold set to 2 to test sufficient lookback
            "above_eq_threshold",
            {"expiry2"},
        ),
        # Case 4: No group with sufficient data
        (
            [
                {"qualified_name": "expiry1", "data": 1},
                {"qualified_name": "expiry2", "data": 2},
            ],
            3,
            "above_eq_threshold",
            set(),
        ),
    ],
)
def test_filter_qualified_names(
    input_data: list[dict[str, str | int]],
    threshold: int,
    filter_condition: Literal["below_threshold", "above_eq_threshold"],
    expected_qualified_names: set[str],
) -> None:
    """
    Tests filtering of DataFrame based on either 'below_threshold' or 'above_or_equal_threshold' conditions.
    """

    df = pd.DataFrame(input_data)
    filtered_df = filter_qualified_names(df, threshold, filter_condition)
    actual_names = set(filtered_df["qualified_name"].unique())

    assert (
        actual_names == expected_qualified_names
    ), f"Expected names {expected_qualified_names} but got {actual_names}"


@pytest.mark.parametrize(
    "include_listed_expiries, scheduled_version",
    [
        (
            False,
            "",
        ),
        (
            False,
            "v-00002",
        ),
        (
            True,
            "",
        ),
        (
            True,
            "v-00002",
        ),
    ],
)
def test_results_to_historic_qualified_names(
    snapshot: Any,
    include_listed_expiries: bool,
    scheduled_version: str,
    standard_listed_and_arb_current_result: pd.DataFrame,
) -> None:
    # add_versioning to data that requires it as the data stored for the tests
    if scheduled_version:
        standard_listed_and_arb_current_result["qualified_name"] = (
            f"{scheduled_version}."
            + standard_listed_and_arb_current_result["qualified_name"].astype(
                str
            )
        )

    result = results_to_historic_qualified_names(
        current_result=standard_listed_and_arb_current_result,
        freq="1m",
        include_listed_expiries=include_listed_expiries,
        scheduled_version=scheduled_version,
    )

    assert result

    full_qns, db_qns = result

    if not scheduled_version:
        custom_snapshot_file_name = f"includeListedExpiries_{include_listed_expiries}-scheduledVersion_None"
    else:
        custom_snapshot_file_name = f"includeListedExpiries_{include_listed_expiries}-scheduledVersion_{scheduled_version}"

    assert all(item in full_qns for item in db_qns)
    snapshot.assert_match(
        json.dumps(result, indent=2),
        f"{custom_snapshot_file_name}.json",
    )


@pytest.mark.parametrize("live", [True, False])
@pytest.mark.parametrize("include_listed_expiries", [True, False])
def test_get_cold_db_data(
    live: bool,
    mocker: mock.MagicMock,
    standard_listed_and_arb_current_result: pd.DataFrame,
    include_listed_expiries: bool,
    lookback_and_append_input_data: LookbackAndAppendInputData,
    snapshot: Any,
    cold_db_test_fixture: GetColdDbTestData,
) -> None:
    def _setup_live_data() -> None:
        cold_db_test_fixture["expected_qualified_name_prefixs"] = [
            qn.rsplit(".", 1)[0] + ".live"
            for qn in cold_db_test_fixture["expected_qualified_name_prefixs"]
        ]
        cold_db_test_fixture["test_qualified_names"] = [
            qn.replace(f".{lookback_and_append_input_data['freq']}.", ".live.")
            for qn in cold_db_test_fixture["test_qualified_names"]
        ]
        standard_listed_and_arb_current_result["qualified_name"] = (
            standard_listed_and_arb_current_result[
                "qualified_name"
            ].str.replace(
                f".{lookback_and_append_input_data['freq']}.", ".live."
            )
        )
        return

    if live:
        _setup_live_data()

    test_data_path = (
        GLOB_DATA_TEST_PATH / "input_data/lookback_and_append/lookback_from_db"
    )
    mock_db_lookup = mocker.patch(
        "lookback_window.lookback_from_db",
        return_value=pd.read_json(
            test_data_path / "mock_lookback_from_db.json",
            dtype={"timestamp": int},
        ),
    )

    test_current_result = standard_listed_and_arb_current_result[
        standard_listed_and_arb_current_result["qualified_name"].isin(
            cold_db_test_fixture["test_qualified_names"]
        )
    ]

    # modify the fixtures and db lookup mock, based on the qualified_names that are returned
    if not include_listed_expiries:
        mock_db_lookup.return_value = mock_db_lookup.return_value[
            ~mock_db_lookup.return_value["qualified_name"].str.contains(
                "listed"
            )
        ]
        cold_db_test_fixture["historic_qualified_names"] = [
            qn
            for qn in cold_db_test_fixture["historic_qualified_names"]
            if ".listed." not in qn
        ]
        cold_db_test_fixture["expected_qualified_name_prefixs"] = [
            qn
            for qn in cold_db_test_fixture["expected_qualified_name_prefixs"]
            if ".listed." not in qn
        ]

    qualified_name_prefixs_in_current_result, lookback_slice = get_cold_db_data(
        current_result=test_current_result,  # will be used to create qn's to query
        include_listed_expiries=include_listed_expiries,
        calc_types=cast(list[SmoothingCalcType], ["smile", "moneyness"]),
        snapshot_dates=cold_db_test_fixture["snapshot_dates"],
        snapshot_timestamps=cold_db_test_fixture["snapshot_dates"][
            "timestamps"
        ],
        historic_freq=lookback_and_append_input_data["freq"],
        debug=lookback_and_append_input_data["debug"],
        consistent_read=lookback_and_append_input_data["consistent_read"],
        scheduled_version=lookback_and_append_input_data["scheduled_version"],
    )

    mock_db_lookup.assert_called_with(
        snapshot_dates=cold_db_test_fixture["snapshot_dates"],
        debug=lookback_and_append_input_data["debug"],
        consistent_read=lookback_and_append_input_data["consistent_read"],
        qualified_names=cold_db_test_fixture["historic_qualified_names"],
    )

    custom_snapshot_file_name = (
        f"live_{live}-includelisted_{include_listed_expiries}-result.json"
    )

    assert set(qualified_name_prefixs_in_current_result) == set(
        cold_db_test_fixture["expected_qualified_name_prefixs"]
    )
    lookback_slice.sort_index(axis=1, inplace=True)
    snapshot.assert_match(
        json.dumps(lookback_slice.to_dict(orient="row"), indent=2),  # type: ignore
        custom_snapshot_file_name,
    )


@pytest.mark.parametrize(
    "listed, arbitrary",
    [(True, False), (False, True), (False, False), (None, None)],
)
@pytest.mark.parametrize(
    "calc_types",
    [["smile"], ["moneyness"], ["smile", "moneyness"]],
)
def test_get_warm_cache_data(
    standard_listed_and_arb_current_result: pd.DataFrame,
    listed: Optional[bool],
    arbitrary: Optional[bool],
    calc_types: list[SmoothingCalcType],
    previous_results_df: pd.DataFrame,
    snapshot: Any,
) -> None:
    if listed is not None and arbitrary is not None:
        current_result = standard_listed_and_arb_current_result[
            (standard_listed_and_arb_current_result["listed_expiry"].eq(listed))
            & (
                standard_listed_and_arb_current_result["arbitrary_expiry"].eq(
                    arbitrary
                )
            )
        ]
        custom_snapshot_file_name = f"listed_{listed}-arbitrary_{arbitrary}-standard_False-calc_types_{'+'.join(calc_types)}-result.json"
    else:
        custom_snapshot_file_name = f"listed_{listed}-arbitrary_{arbitrary}-standard_True-calc_types_{'+'.join(calc_types)}-result.json"
        # load everything
        current_result = standard_listed_and_arb_current_result

    # cold db data has been passed in to the next run
    previous_results: PreviousResults = {"df": previous_results_df}

    assert previous_results["df"] is not None

    unique_tstamps = previous_results["df"]["timestamp"].unique()
    assert len(unique_tstamps) == 10

    # choose the most recent 2 timestamps for testing
    test_timestamps = sorted(unique_tstamps)[-2:]

    (
        qualified_name_prefixs_in_current_result,
        filtered_lookback_slice,
    ) = get_warm_cache_data(
        previous_results=previous_results,
        current_result=current_result,
        calc_types=calc_types,
        snapshot_timestamps=test_timestamps,
    )

    assert (
        sorted(filtered_lookback_slice["timestamp"].unique()) == test_timestamps
    )  # these should be the only timestamps that are present
    assert not any(
        filtered_lookback_slice["qualified_name"].str.contains(".listed")
    )  # we should properly extract any listed rows

    filtered_lookback_slice.sort_index(axis=1, inplace=True)
    snapshot.assert_match(
        json.dumps(filtered_lookback_slice.to_dict(orient="row"), indent=2),  # type: ignore
        custom_snapshot_file_name,
    )


def pytest_generate_tests(metafunc: Metafunc) -> None:
    """
    Dynamically parameterizes test fixtures using data from 'listed_expiry.json'.

    This function reads a JSON file containing qualified names from listed expiries,
    processes these names to derive specific calculation types, and then uses them
    to parameterize pytest tests that use the 'listed_qualified_name' fixture.

    """

    current_result_data_path = (
        GLOB_DATA_TEST_PATH
        / "input_data/lookback_and_append/current_result_input_data"
    )
    if "listed_qualified_name" in metafunc.fixturenames:
        listed_expiry_current_result = pd.read_json(
            current_result_data_path / "mock_listed_expiry.json",
            dtype={"timestamp": int},
        )
        calc_type_qns = get_calc_type_qns_from_qualified_name_prefix(
            qualified_name_prefixs=get_qualified_name_prefixs_from_qns(
                listed_expiry_current_result["qualified_name"].tolist()
            ),
            calc_types=cast(list[SmoothingCalcType], ["smile", "moneyness"]),
        )
        # parameterize the qualified_names of the listed expiries
        metafunc.parametrize("listed_qualified_name", calc_type_qns)


def test_lookback_and_append_cold_cache_insufficient_listed_expiries(
    snapshot: Any,
    listed_qualified_name: str,
    lookback_and_append_input_data: LookbackAndAppendInputData,
    standard_listed_and_arb_current_result: pd.DataFrame,
    mocker: mock.MagicMock,
    mock_get_cold_db_data: tuple[list[str], pd.DataFrame],
) -> None:
    """
    We test the functionality of the lookback_and_append function. We pass in a set of standard
    tenors and listed expiries as out currently result (qualified names we want to fetch lookback data for).
    We mock the retrival of lookback data, and augment this data such that one listed expiry is missing data
    We then assert that the correct listed/standard tenors are used to populate its lookback.

    """

    def _setup_test_data() -> tuple[list[str], pd.DataFrame]:
        qualified_name_prefixs, db_df = mock_get_cold_db_data
        qn_df = db_df[db_df["qualified_name"] == listed_qualified_name]

        # remove data such that we are required to append lookback data to the qualified_name
        earliest_n_timestamps = qn_df.sort_values(by=["timestamp"]).head(
            len(qn_df) - round((MIN_SMOOTHING_LOOKBACK_RATIO * len(qn_df)) - 1)
        )
        return qualified_name_prefixs, db_df.drop(earliest_n_timestamps.index)

    # remove arbitrary expiries
    listed_and_standard_current_result = standard_listed_and_arb_current_result[
        ~standard_listed_and_arb_current_result[
            TenorType.ARBITRARY_EXPIRY.value
        ]
    ]

    mock_db_lookup = mocker.patch(
        "lookback_window.get_cold_db_data", return_value=_setup_test_data()
    )
    assert len(mock_db_lookup.return_value[1]) < (
        len(mock_get_cold_db_data[1]) + len(listed_and_standard_current_result)
    ), "Mock Lookback not set up correctly"

    lookback_result = lookback_and_append(
        previous_results={"df": None},
        current_result=listed_and_standard_current_result,
        end_str=lookback_and_append_input_data["end_str"],
        interval=lookback_and_append_input_data["interval"],
        lookback_window_size=lookback_and_append_input_data[
            "lookback_window_size"
        ],
        freq=lookback_and_append_input_data["freq"],
        debug=lookback_and_append_input_data["debug"],
        consistent_read=lookback_and_append_input_data["consistent_read"],
        include_listed_expiries=lookback_and_append_input_data[
            "include_listed_expiries"
        ],
        calc_types=lookback_and_append_input_data["calc_types"],
        scheduled_version=lookback_and_append_input_data["scheduled_version"],
    ).reset_index()

    # extract data related to the qualified_name for snapshot testing
    listed_qn_result = lookback_result[
        lookback_result["qualified_name"] == listed_qualified_name
    ].sort_index(axis=1)

    assert len(lookback_result) == (
        len(mock_get_cold_db_data[1]) + len(listed_and_standard_current_result)
    )

    snapshot.snapshot_dir = pathlib.Path(
        str(snapshot.snapshot_dir).replace(".", "_").replace(":", "")
    )

    # Sense check the "closest_qualified_name" column as its denotes the qualified_name of the
    # data that has used to populate the lookback. When this is NaN, then no the lookback contains
    # was not modified.
    snapshot.assert_match(
        json.dumps(listed_qn_result.to_dict(orient="row"), indent=2),  # type: ignore
        f"{listed_qualified_name}.json",
    )


@pytest.mark.parametrize("live", [True, False])
def test_lookback_and_append_cold_cache_insufficient_arbitrary_expiry(
    live: bool,
    snapshot: Any,
    lookback_and_append_input_data: LookbackAndAppendInputData,
    standard_listed_and_arb_current_result: pd.DataFrame,
    mocker: mock.MagicMock,
    mock_get_cold_db_data: tuple[list[str], pd.DataFrame],
) -> None:
    def _setup_live_data() -> tuple[list[str], pd.DataFrame]:
        new_qns = [
            qn.rsplit(".", 1)[0] + ".live" for qn in mock_get_cold_db_data[0]
        ]
        mock_get_cold_db_data[1]["qualified_name"] = mock_get_cold_db_data[1][
            "qualified_name"
        ].str.replace(f".{lookback_and_append_input_data['freq']}.", ".live.")
        standard_listed_and_arb_current_result["qualified_name"] = (
            standard_listed_and_arb_current_result[
                "qualified_name"
            ].str.replace(
                f".{lookback_and_append_input_data['freq']}.", ".live."
            )
        )
        return (new_qns, mock_get_cold_db_data[1])

    if live:
        mock_get_cold_db_data = _setup_live_data()

    mocker.patch(
        "lookback_window.get_cold_db_data", return_value=mock_get_cold_db_data
    )
    exchange_curr_timestamp_to_expiry_info = lookback_and_append_input_data[
        "exchange_curr_timestamp_to_expiry_info"
    ]
    lookback_result = lookback_and_append(
        previous_results={"df": None},
        current_result=standard_listed_and_arb_current_result,
        end_str=lookback_and_append_input_data["end_str"],
        interval=lookback_and_append_input_data["interval"],
        lookback_window_size=lookback_and_append_input_data[
            "lookback_window_size"
        ],
        debug=lookback_and_append_input_data["debug"],
        consistent_read=lookback_and_append_input_data["consistent_read"],
        include_listed_expiries=lookback_and_append_input_data[
            "include_listed_expiries"
        ],
        freq=lookback_and_append_input_data["freq"],
        calc_types=lookback_and_append_input_data["calc_types"],
        scheduled_version=lookback_and_append_input_data["scheduled_version"],
    ).reset_index()

    # extract data related to the qualified_name for snapshot testing
    arb_in_qn_result = lookback_result[
        lookback_result["arbitrary_expiry"].eq(True)
    ].sort_index(axis=1)

    lookback_appended_data_len = (
        len(lookback_and_append_input_data["calc_types"])
        * lookback_and_append_input_data["lookback_window_size"]
    )
    arb_expiries_len = len(
        exchange_curr_timestamp_to_expiry_info["deribit"]["BTC"][
            int(
                utils_general.from_iso(
                    lookback_and_append_input_data["end_str"]
                ).timestamp()
                * 1e9
            )
        ]
    )

    assert len(lookback_result) == (
        len(mock_get_cold_db_data[1])  # lookback_data
        + len(standard_listed_and_arb_current_result)  # current_result
        + (arb_expiries_len * lookback_appended_data_len)
    )
    for arb_exp_qn, arb_exp_df in arb_in_qn_result.groupby("qualified_name"):
        assert isinstance(arb_exp_qn, str)  # mypy
        custom_snapshot_file_name = f"live_{live}-{arb_exp_qn}"

        snapshot.assert_match(
            json.dumps(arb_exp_df.to_dict(orient="row"), indent=2),  # type: ignore
            f"{custom_snapshot_file_name}.json",
        )


@pytest.mark.parametrize(
    "records, expected_output",
    [
        # fmt: off
        # Case where no records are listed or arbitrary
        ([{"tenor_days": 30, "listed_expiry": False, "arbitrary_expiry": False, "standard_constant_maturity": True, "arbitrary_constant_maturity": False},
          {"tenor_days": 60, "listed_expiry": False, "arbitrary_expiry": False, "standard_constant_maturity": True, "arbitrary_constant_maturity": False},
          {"tenor_days": 90, "listed_expiry": False, "arbitrary_expiry": False, "standard_constant_maturity": True, "arbitrary_constant_maturity": False},
          {"tenor_days": 180, "listed_expiry": False, "arbitrary_expiry": False, "standard_constant_maturity": True, "arbitrary_constant_maturity": False},
          {"tenor_days": 365, "listed_expiry": False, "arbitrary_expiry": False, "standard_constant_maturity": True, "arbitrary_constant_maturity": False}],
         [30, 60, 90 ,180, 365]),

        # Case with some records listed and some arbitrary, testing for no overlaps
        ([{"tenor_days": 30, "listed_expiry": True, "arbitrary_expiry": False, "standard_constant_maturity": False, "arbitrary_constant_maturity": False},
          {"tenor_days": 60, "listed_expiry": False, "arbitrary_expiry": False, "standard_constant_maturity": True, "arbitrary_constant_maturity": False},
          {"tenor_days": 90, "listed_expiry": True, "arbitrary_expiry": False, "standard_constant_maturity": False, "arbitrary_constant_maturity": False},
          {"tenor_days": 180, "listed_expiry": False, "arbitrary_expiry": True, "standard_constant_maturity": False, "arbitrary_constant_maturity": False},
          {"tenor_days": 365, "listed_expiry": False, "arbitrary_expiry": True, "standard_constant_maturity": False, "arbitrary_constant_maturity": False}],
         [60]),

        # Case where all records are either listed or arbitrary
        ([{"tenor_days": 30, "listed_expiry": True, "arbitrary_expiry": False, "standard_constant_maturity": False, "arbitrary_constant_maturity": False},
          {"tenor_days": 60, "listed_expiry": False, "arbitrary_expiry": True, "standard_constant_maturity": False, "arbitrary_constant_maturity": False},
          {"tenor_days": 90, "listed_expiry": True, "arbitrary_expiry": False, "standard_constant_maturity": False, "arbitrary_constant_maturity": False},
          {"tenor_days": 180, "listed_expiry": False, "arbitrary_expiry": True, "standard_constant_maturity": False, "arbitrary_constant_maturity": False},
          {"tenor_days": 365, "listed_expiry": True, "arbitrary_expiry": False, "standard_constant_maturity": False, "arbitrary_constant_maturity": False}],
         []),
        # Case where all records are either standard_constant_maturities or arbitrary_constant_maturities
        ([{"tenor_days": 30, "listed_expiry": False, "arbitrary_expiry": False, "standard_constant_maturity": True,"arbitrary_constant_maturity": False},
          {"tenor_days": 60, "listed_expiry": False, "arbitrary_expiry": False, "standard_constant_maturity": False,"arbitrary_constant_maturity": True},
          {"tenor_days": 90, "listed_expiry": False, "arbitrary_expiry": False, "standard_constant_maturity": False,"arbitrary_constant_maturity": True},
          {"tenor_days": 180, "listed_expiry": False, "arbitrary_expiry": False, "standard_constant_maturity": True,"arbitrary_constant_maturity": False},
          {"tenor_days": 365, "listed_expiry": False, "arbitrary_expiry": False, "standard_constant_maturity": True,"arbitrary_constant_maturity": False}],
         [30, 60, 90, 180, 365]),
        # Duplicates
        ([{"tenor_days": 30, "listed_expiry": True, "arbitrary_expiry": False, "standard_constant_maturity": False, "arbitrary_constant_maturity": False},
          {"tenor_days": 60, "listed_expiry": False, "arbitrary_expiry": True, "standard_constant_maturity": False, "arbitrary_constant_maturity": False},
          {"tenor_days": 90, "listed_expiry": True, "arbitrary_expiry": False, "standard_constant_maturity": False, "arbitrary_constant_maturity": False},
          {"tenor_days": 90, "listed_expiry": False, "arbitrary_expiry": False, "standard_constant_maturity": True, "arbitrary_constant_maturity": False},
          {"tenor_days": 180, "listed_expiry": True, "arbitrary_expiry": True, "standard_constant_maturity": False, "arbitrary_constant_maturity": False},
          {"tenor_days": 365, "listed_expiry": False, "arbitrary_expiry": False, "standard_constant_maturity": False, "arbitrary_constant_maturity": False}],
         [90]),
        # fmt: on
    ],
)
def test_extract_constant_tenors_from_maturities(
    records: list[dict[str, int | bool]], expected_output: list[int]
) -> None:
    df = pd.DataFrame.from_records(records)

    result = extract_constant_tenors_from_maturities(df)
    if result:
        assert sorted(result) == sorted(expected_output)
    else:
        assert result == expected_output
