import pytest
from lambda_types import (
    ExpiryArbFields,
    TenorExpiryArbFields,
    TimestampToTenorExpiryArbFields,
)


def test_merge_different_fields() -> None:
    fields1 = ExpiryArbFields(
        strikes={1.0, 2.0},
        deltas={0.1, 0.2},
        moneyness={0.95, 1.05},
        listed_expiry=True,
        iso_expiry="2024-12-31T08:00:00",
    )
    fields2 = ExpiryArbFields(
        strikes={3.0},
        deltas={0.3},
        moneyness={1.10},
        arbitrary_expiry=True,
        iso_expiry="2024-12-31T08:00:00",
    )

    merged = fields1.merge(fields2)

    assert merged.strikes == {1.0, 2.0, 3.0}
    assert merged.deltas == {0.1, 0.2, 0.3}
    assert merged.moneyness == {0.95, 1.05, 1.10}
    assert merged.listed_expiry
    assert not merged.arbitrary_expiry


def test_listed_overrides_arbitrary() -> None:
    fields_listed = ExpiryArbFields(
        listed_expiry=True, iso_expiry="2024-12-31T08:00:00"
    )
    fields_arbitrary = ExpiryArbFields(
        arbitrary_expiry=True, iso_expiry="2024-12-31T08:00:00"
    )

    result_from_listed = fields_listed.merge(fields_arbitrary)
    result_from_arbitrary = fields_arbitrary.merge(fields_listed)

    for result in [result_from_listed, result_from_arbitrary]:
        assert result.listed_expiry
        assert not result.arbitrary_expiry


def test_arbitrary_only_when_both_are() -> None:
    arbitrary1 = ExpiryArbFields(
        arbitrary_expiry=True, iso_expiry="2024-12-31T08:00:00"
    )
    arbitrary2 = ExpiryArbFields(
        arbitrary_expiry=True, iso_expiry="2024-12-31T08:00:00"
    )
    fields_listed = ExpiryArbFields(
        listed_expiry=True, iso_expiry="2024-12-31T08:00:00"
    )

    result_both_arbitrary = arbitrary1.merge(arbitrary2)
    result_one_not_arbitrary = arbitrary1.merge(fields_listed)

    assert result_both_arbitrary.arbitrary_expiry
    assert not result_both_arbitrary.listed_expiry

    assert not result_one_not_arbitrary.arbitrary_expiry
    assert result_one_not_arbitrary.listed_expiry


@pytest.mark.parametrize(
    "e1, e2, expected",
    [
        # fmt: off
        # listed exp + constant maturity and arbitrary expiry
        (
            ExpiryArbFields(strikes={100}, deltas={0.5}, moneyness={1.0}, listed_expiry=True, iso_expiry='2024-12-31T08:00:00'),
            ExpiryArbFields(strikes={110}, deltas={0.6}, moneyness={1.1},  arbitrary_expiry=True, standard_constant_maturity=True, iso_expiry='2024-12-31T08:00:00', tenor_str='7d'),
            ExpiryArbFields(strikes={100, 110}, deltas={0.5, 0.6}, moneyness={1.0, 1.1}, listed_expiry=True,  standard_constant_maturity=True, iso_expiry='2024-12-31T08:00:00', tenor_str='7d')
        ),
        # Listed exp  + arb expiry
        (
            ExpiryArbFields(strikes={100}, deltas={0.5}, moneyness={1.0},    arbitrary_constant_maturity=True, tenor_str='7d'),
            ExpiryArbFields(strikes={110}, deltas={0.6}, moneyness={1.1},   standard_constant_maturity=True, tenor_str='7d'),
            ExpiryArbFields(strikes={100, 110}, deltas={0.5, 0.6}, moneyness={1.0, 1.1},   standard_constant_maturity=True, tenor_str='7d')
        ),
        # listed + listed
        (
            ExpiryArbFields(strikes={100}, deltas={0.5}, moneyness={1.0}, listed_expiry=True, iso_expiry='2024-12-31T08:00:00'),
            ExpiryArbFields(strikes={110}, deltas={0.6}, moneyness={1.1}, listed_expiry=True, iso_expiry='2024-12-31T08:00:00'),
            ExpiryArbFields(strikes={100, 110}, deltas={0.5, 0.6}, moneyness={1.0, 1.1}, listed_expiry=True, iso_expiry='2024-12-31T08:00:00')
        ),
        # arbitrary + arbitrary
        (
            ExpiryArbFields(strikes={100}, deltas={0.5}, moneyness={1.0},  arbitrary_expiry=True, iso_expiry='2024-12-31T08:00:00'),
            ExpiryArbFields(strikes={110}, deltas={0.6}, moneyness={1.1},  arbitrary_expiry=True, iso_expiry='2024-12-31T08:00:00'),
            ExpiryArbFields(strikes={100, 110}, deltas={0.5, 0.6}, moneyness={1.0, 1.1},  arbitrary_expiry=True, iso_expiry='2024-12-31T08:00:00')
        ),
        # listed expiry and arbitrary constant maturity + arbitrary expiry
        (
            ExpiryArbFields(strikes={100}, deltas={0.5}, moneyness={1.0}, listed_expiry=True, arbitrary_constant_maturity=True, iso_expiry='2024-12-31T08:00:00', tenor_str='7d'),
            ExpiryArbFields(strikes={110}, deltas={0.6}, moneyness={1.1}, arbitrary_expiry=True, iso_expiry='2024-12-31T08:00:00'),
            ExpiryArbFields(strikes={100, 110}, deltas={0.5, 0.6}, moneyness={1.0, 1.1}, listed_expiry=True, arbitrary_constant_maturity=True, iso_expiry='2024-12-31T08:00:00', tenor_str='7d')
        ),
        # listed and arbitrary constant maturity + arbitrary expiry + standard constant maturity
        (
            ExpiryArbFields(strikes={100}, deltas={0.5}, moneyness={1.0}, listed_expiry=True,   arbitrary_constant_maturity=True, iso_expiry='2024-12-31T08:00:00', tenor_str='7d'),
            ExpiryArbFields(strikes={110}, deltas={0.6}, moneyness={1.1},  arbitrary_expiry=True, standard_constant_maturity=True, iso_expiry='2024-12-31T08:00:00', tenor_str='7d'),
            ExpiryArbFields(strikes={100, 110}, deltas={0.5, 0.6}, moneyness={1.0, 1.1}, listed_expiry=True,  standard_constant_maturity=True, iso_expiry='2024-12-31T08:00:00', tenor_str='7d')
        ),
        # fmt: on
    ],
)
def test_merge(
    e1: ExpiryArbFields, e2: ExpiryArbFields, expected: ExpiryArbFields
) -> None:
    merged = e1.merge(e2)
    assert merged == expected


@pytest.mark.parametrize(
    "input_tenors, expected_order",
    [
        (
            [
                (2.0, ExpiryArbFields(strikes={100.0})),
                (1.0, ExpiryArbFields(strikes={100.0})),
                (3.0, ExpiryArbFields(strikes={100.0})),
            ],
            [1.0, 2.0, 3.0],
        ),
        (
            [
                (3.0, ExpiryArbFields(strikes={100.0})),
                (2.0, ExpiryArbFields(strikes={100.0})),
                (1.0, ExpiryArbFields(strikes={100.0})),
            ],
            [1.0, 2.0, 3.0],
        ),
        (
            [
                (1.0, ExpiryArbFields(strikes={100.0})),
                (3.0, ExpiryArbFields(strikes={100.0})),
                (2.0, ExpiryArbFields(strikes={100.0})),
            ],
            [1.0, 2.0, 3.0],
        ),
    ],
)
def test_add_and_sort_expiry_arb_fields(
    input_tenors: list[tuple[float, ExpiryArbFields]],
    expected_order: list[float],
) -> None:
    tenor_expiry = TenorExpiryArbFields()
    for tenor, field in input_tenors:
        tenor_expiry.add_expiry_arb_fields(tenor, field)
    tenor_expiry.sort_data()
    sorted_keys = list(tenor_expiry.data.keys())
    assert sorted_keys == expected_order


@pytest.mark.parametrize(
    "input_timestamps, input_tenors, expected_order",
    [
        (
            [1, 2],
            {
                1: [
                    (2.0, ExpiryArbFields(strikes={100.0})),
                    (1.0, ExpiryArbFields(strikes={100.0})),
                ],
                2: [
                    (3.0, ExpiryArbFields(strikes={100.0})),
                    (1.0, ExpiryArbFields(strikes={100.0})),
                ],
            },
            {1: [1.0, 2.0], 2: [1.0, 3.0]},
        ),
        (
            [1],
            {
                1: [
                    (3.0, ExpiryArbFields(strikes={100.0})),
                    (2.0, ExpiryArbFields(strikes={100.0})),
                    (1.0, ExpiryArbFields(strikes={100.0})),
                ]
            },
            {1: [1.0, 2.0, 3.0]},
        ),
    ],
)
def test_timestamp_to_tenor_expiry_arb_fields(
    input_timestamps: list[int],
    input_tenors: dict[int, list[tuple[float, ExpiryArbFields]]],
    expected_order: dict[int, list[float]],
) -> None:
    timestamp_to_tenor = TimestampToTenorExpiryArbFields()
    for timestamp in input_timestamps:
        tenor_expiry = TenorExpiryArbFields()
        for tenor, field in input_tenors[timestamp]:
            tenor_expiry.add_expiry_arb_fields(tenor, field)
        timestamp_to_tenor[timestamp] = tenor_expiry

    for timestamp in input_timestamps:
        tenor_expiry = timestamp_to_tenor[timestamp]
        sorted_keys = list(tenor_expiry.data.keys())
        assert sorted_keys == expected_order[timestamp]
