import logging
from typing import cast

import numpy as np
import pandas as pd
import pytest
import utils_general
from constants import (
    LOOKBACK_COLUMN_NAME,
    R2_SMOOTHCALIB_COLUMN_NAME,
    SPIKED_PARAMS_COLUMN_NAME,
)
from lambda_types import VolSurfaceCalcType
from utils.arbitrary_lookback import (
    _check_data_quality_issues_and_filter_df,
    identify_and_populate_arbitrary_values_for_smoothing,
)
from utils.common import set_qualified_name_timestamp_index

from .helpers import compare_dataframes_row_by_row


@pytest.fixture
def input_fixture() -> dict[str, pd.DataFrame]:
    # fmt: off
    current_result = [
        {'tenor_days': 4.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.params', 'timestamp': 1719820800000000000, 'expiry': 0.01095890411, 'forward': 63323.75, 'svi_rho': -0.3792102010621424, 'svi_a': 0.0007657525805969175, 'spot': 63235.496235239676, 'svi_b': 0.0225280083034741, 'svi_sigma': 0.054003418599337924, 'atm_vol': 0.415965608949725, 'contains_recalib_spike': np.nan, 'svi_m': -0.01687717892074818, 'from_lookback': False, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m'},
        {'tenor_days': 4.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.strike', 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', 'timestamp': 1719820800000000000, 'from_lookback': False, '61000strike': 0.4523415376074195, '66000strike': 0.4333235785228331,
         '696969strike': 1.775799527726476}, # arb_values with no lookback
        {'tenor_days': 4.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.moneyness', 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', 'timestamp': 1719820800000000000, 'expiry': 0.010958904109589041, 'from_lookback': False, '100money': 0.41596560894192564, '80money': 0.8179096800280996, '120money': 0.5821192654059997,
         '277money': 1.180890526689011}, # arb_values with no lookback
        {'tenor_days': 4.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.smile', 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', 'timestamp': 1719820800000000000, 'expiry': 0.010958904109589041, 'from_lookback': False,
         "3d5delta": 0.486326312234048},  # arb_values with no lookback

        {'tenor_days': 1.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.params', 'timestamp': 1719820800000000000, 'expiry': 0.002739726027, 'forward': 63257.54813861788, 'svi_rho': 0.148439952971213, 'svi_a': 0.00023317104820170384, 'spot': 63235.496235239676, 'svi_b': 0.009490116334020892, 'svi_sigma': 0.02171158944581066, 'atm_vol': 0.41478540034441846, 'contains_recalib_spike': np.nan, 'svi_m': 0.017100257365180446, 'from_lookback': False, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m'},
        {'tenor_days': 1.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.strike', 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', 'timestamp': 1719820800000000000, 'from_lookback': False, '61000strike': 0.507382104921187, '66000strike': 0.4623033929377359, '696969strike': 3.0923962106695506},
        {'tenor_days': 1.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.moneyness', 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', 'timestamp': 1719820800000000000, 'expiry': 0.0027397260273972603, 'from_lookback': False, '277money': 2.0176582415844933, '100money': 0.4147854003744904, '80money': 0.8928320107277017, '120money': 0.8644593729997618},
        {'tenor_days': 1.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.smile', 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', 'timestamp': 1719820800000000000, 'expiry': 0.0027397260273972603, 'from_lookback': False, "3d5delta": 0.1}
    ]

    lookback_data  = [
        {'tenor_days': 4.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.params', 'timestamp': 1719820680000000000, 'expiry': 0.010962709285, 'forward': 63323.75, 'svi_rho': -0.4141646328901876, 'svi_a': 0.000801429377357313, 'spot': 63239.422231016244, 'svi_b': 0.022112415347691806, 'svi_sigma': 0.05084441366960688, 'atm_vol': 0.4085247173736632, 'contains_recalib_spike': False, 'svi_m': -0.01781071505957095, 'expiry_str': '2024-07-05T08:00:00Z', 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m'},
        {'tenor_days': 4.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.params', 'timestamp': 1719820740000000000, 'expiry': 0.010960806697, 'forward': 63316.25, 'svi_rho': -0.42561767551672347, 'svi_a': 0.0008402853772529233, 'spot': 63233.14128656461, 'svi_b': 0.02193943888749022, 'svi_sigma': 0.05014943608742622, 'atm_vol': 0.40964969107003174, 'contains_recalib_spike': False, 'svi_m': -0.01904590941394013, 'expiry_str': '2024-07-05T08:00:00Z', 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m'},

        {'tenor_days': 4.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.strike', 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', 'timestamp': 1719820680000000000, 'from_lookback': True, '61000strike': 0.4464901384463531, '66000strike': 0.425509804408031, '696969strike': np.nan},
        {'tenor_days': 4.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.strike', 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', 'timestamp': 1719820740000000000, 'from_lookback': True, '61000strike': 0.4460811593833499, '66000strike': 0.4271468437940875, '696969strike': np.nan},

        {'tenor_days': 4.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.moneyness', 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', 'timestamp': 1719820680000000000, 'expiry': 0.010962709284627397, 'from_lookback': True, '277money': np.nan, '100money': 0.40852471736671503, '80money': 0.8193384801225349, '120money': 0.5678184451309949},
        {'tenor_days': 4.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.moneyness', 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', 'timestamp': 1719820740000000000, 'expiry': 0.01096080669710685, 'from_lookback': True, '277money': np.nan, '100money': 0.40964969107205124, '80money': 0.819278950230451, '120money': 0.5661150240538926},

        {'tenor_days': 4.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.smile', 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', 'timestamp': 1719820680000000000,'expiry': 0.010962709284627397, 'from_lookback': True, "3d5delta": np.nan},
        {'tenor_days': 4.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.smile','qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', 'timestamp': 1719820740000000000,'expiry': 0.01096080669710685, 'from_lookback': True,  "3d5delta": np.nan},

        {'tenor_days': 1.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.params', 'timestamp': 1719820680000000000, 'expiry': 0.002743531202, 'forward': 63260.51558484025, 'svi_rho': -0.17923184376835205, 'svi_a': 0.0002940442056225631, 'spot': 63239.422231016244, 'svi_b': 0.007977080284092004, 'svi_sigma': 0.018951541515816336, 'atm_vol': 0.4040888376438661, 'contains_recalib_spike': False, 'svi_m': 0.0015701873900868897, 'expiry_str': '2024-07-02T08:00:00Z', 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m'},
        {'tenor_days': 1.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.params', 'timestamp': 1719820740000000000, 'expiry': 0.002741628615, 'forward': 63253.91904832017, 'svi_rho': 0.07995049465032875, 'svi_a': 0.0001904591996133537, 'spot': 63233.14128656461, 'svi_b': 0.009565467141848387, 'svi_sigma': 0.0262434454540807, 'atm_vol': 0.4115472950692524, 'contains_recalib_spike': False, 'svi_m': 0.014022198579339746, 'expiry_str': '2024-07-02T08:00:00Z', 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m'},

        {'tenor_days': 1.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.strike', 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', 'timestamp': 1719820680000000000, 'from_lookback': True, '61000strike': 0.5003152563396541, '66000strike': 0.4655825996461777, '696969strike': np.nan},
        {'tenor_days': 1.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.strike', 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', 'timestamp': 1719820740000000000, 'from_lookback': True, '61000strike': 0.503387478354699, '66000strike': 0.4609993652643708, '696969strike': np.nan},

        {'tenor_days': 1.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.moneyness', 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', 'timestamp': 1719820680000000000, 'expiry': 0.002743531202435616, 'from_lookback': True, '100money': 0.40408883767592413, '80money': 0.9380722021577146, '120money': 0.7358087054906611},
        {'tenor_days': 1.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.moneyness', 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', 'timestamp': 1719820740000000000, 'expiry': 0.0027416286149150684, 'from_lookback': True, '100money': 0.4115472950629692, '80money': 0.9142365366187051, '120money': 0.8430322921524035},

        {'tenor_days': 1.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.smile', 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', 'timestamp': 1719820680000000000, 'expiry': 0.002743531202435616, 'from_lookback': True, "3d5delta": np.nan},
        {'tenor_days': 1.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.smile', 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', 'timestamp': 1719820740000000000, 'expiry': 0.0027416286149150684, 'from_lookback': True,  "3d5delta": np.nan},
    ]
    # fmt: on

    return {
        "current_result": pd.DataFrame(current_result),
        "lookback_data": pd.DataFrame(lookback_data),
    }


def test_identify_arbitrary_values_for_smoothing(
    input_fixture: dict[str, pd.DataFrame], caplog: pytest.LogCaptureFixture
) -> None:
    """ """

    # fmt: off
    expected_result = [
        {'tenor_days': 4.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.params', 'timestamp': 1719820800000000000, 'expiry': 0.01095890411, 'forward': 63323.75, 'svi_rho': -0.3792102010621424, 'svi_a': 0.0007657525805969175, 'spot': 63235.496235239676, 'svi_b': 0.0225280083034741, 'svi_sigma': 0.054003418599337924, 'atm_vol': 0.415965608949725, 'svi_m': -0.01687717892074818, 'from_lookback': False, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m'},
        {'tenor_days': 4.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.strike', 'timestamp': 1719820800000000000, 'from_lookback': False, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', '61000strike': 0.4523415376074195, '66000strike': 0.4333235785228331, '696969strike': 1.775799527726476},
        {'tenor_days': 4.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.moneyness', 'timestamp': 1719820800000000000, 'expiry': 0.010958904109589041, 'from_lookback': False, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', '277money': 1.180890526689011, '100money': 0.41596560894192564, '80money': 0.8179096800280996, '120money': 0.5821192654059997},
        {'tenor_days': 4.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.smile', 'timestamp': 1719820800000000000, 'expiry': 0.010958904109589041, 'from_lookback': False, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', '3d5delta': 0.486326312234048},

        {'tenor_days': 1.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.params', 'timestamp': 1719820800000000000, 'expiry': 0.002739726027, 'forward': 63257.54813861788, 'svi_rho': 0.148439952971213, 'svi_a': 0.00023317104820170384, 'spot': 63235.496235239676, 'svi_b': 0.009490116334020892, 'svi_sigma': 0.02171158944581066, 'atm_vol': 0.41478540034441846, 'svi_m': 0.017100257365180446, 'from_lookback': False, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m'},
        {'tenor_days': 1.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.strike', 'timestamp': 1719820800000000000, 'from_lookback': False, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', '61000strike': 0.507382104921187, '66000strike': 0.4623033929377359, '696969strike': 3.0923962106695506},
        {'tenor_days': 1.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.moneyness', 'timestamp': 1719820800000000000, 'expiry': 0.0027397260273972603, 'from_lookback': False, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', '277money': 2.0176582415844933, '100money': 0.4147854003744904, '80money': 0.8928320107277017, '120money': 0.8644593729997618},
        {'tenor_days': 1.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.smile', 'timestamp': 1719820800000000000, 'expiry': 0.0027397260273972603, 'from_lookback': False, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', '3d5delta': 0.1},

        # lookback results - arb_cols should be populated
        {'tenor_days': 4.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.params', 'timestamp': 1719820680000000000, 'expiry': 0.010962709285, 'forward': 63323.75, 'svi_rho': -0.4141646328901876, 'svi_a': 0.000801429377357313, 'spot': 63239.422231016244, 'svi_b': 0.022112415347691806, 'svi_sigma': 0.05084441366960688, 'atm_vol': 0.4085247173736632, 'contains_recalib_spike': False, 'svi_m': -0.01781071505957095, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', 'expiry_str': '2024-07-05T08:00:00Z'},
        {'tenor_days': 4.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.params', 'timestamp': 1719820740000000000, 'expiry': 0.010960806697, 'forward': 63316.25, 'svi_rho': -0.42561767551672347, 'svi_a': 0.0008402853772529233, 'spot': 63233.14128656461, 'svi_b': 0.02193943888749022, 'svi_sigma': 0.05014943608742622, 'atm_vol': 0.40964969107003174, 'contains_recalib_spike': False, 'svi_m': -0.01904590941394013, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', 'expiry_str': '2024-07-05T08:00:00Z'},
        {'tenor_days': 4.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.strike', 'timestamp': 1719820680000000000, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', '61000strike': 0.4464901384463531, '66000strike': 0.425509804408031,
            '696969strike': 1.711557005852037}, # populated arb_values
        {'tenor_days': 4.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.strike', 'timestamp': 1719820740000000000, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', '61000strike': 0.4460811593833499, '66000strike': 0.4271468437940875,
            '696969strike': 1.690345229269062}, # populated arb_values
        {'tenor_days': 4.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.moneyness', 'timestamp': 1719820680000000000, 'expiry': 0.010962709284627397, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', '100money': 0.40852471736671503, '80money': 0.8193384801225349, '120money': 0.5678184451309949,
            '277money': 1.1404383593600842}, # populated arb_values
        {'tenor_days': 4.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.moneyness', 'timestamp': 1719820740000000000, 'expiry': 0.01096080669710685, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', '100money': 0.40964969107205124, '80money': 0.819278950230451, '120money': 0.5661150240538926,
            '277money': 1.1279853702794376}, # populated arb_values
        {'tenor_days': 4.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.smile', 'timestamp': 1719820680000000000, 'expiry': 0.010962709284627397, 'from_lookback': True,'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m',
         '3d5delta': 0.47395927535140714}, # populated arb_values
        {'tenor_days': 4.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.smile', 'timestamp': 1719820740000000000, 'expiry': 0.01096080669710685, 'from_lookback': True,'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m',
         '3d5delta': 0.47462137796023446}, # populated arb_values

        {'tenor_days': 1.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.params', 'timestamp': 1719820680000000000, 'expiry': 0.002743531202, 'forward': 63260.51558484025, 'svi_rho': -0.17923184376835205, 'svi_a': 0.0002940442056225631, 'spot': 63239.422231016244, 'svi_b': 0.007977080284092004, 'svi_sigma': 0.018951541515816336, 'atm_vol': 0.4040888376438661, 'contains_recalib_spike': False, 'svi_m': 0.0015701873900868897, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', 'expiry_str': '2024-07-02T08:00:00Z'},
        {'tenor_days': 1.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.params', 'timestamp': 1719820740000000000, 'expiry': 0.002741628615, 'forward': 63253.91904832017, 'svi_rho': 0.07995049465032875, 'svi_a': 0.0001904591996133537, 'spot': 63233.14128656461, 'svi_b': 0.009565467141848387, 'svi_sigma': 0.0262434454540807, 'atm_vol': 0.4115472950692524, 'contains_recalib_spike': False, 'svi_m': 0.014022198579339746, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', 'expiry_str': '2024-07-02T08:00:00Z'},
        {'tenor_days': 1.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.strike', 'timestamp': 1719820680000000000, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', '61000strike': 0.5003152563396541, '66000strike': 0.4655825996461777,
         '696969strike': 2.414521709925129}, # populated arb_values
        {'tenor_days': 1.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.strike', 'timestamp': 1719820740000000000, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', '61000strike': 0.503387478354699, '66000strike': 0.4609993652643708,
         '696969strike': 3.009746323826699}, # populated arb_values
        {'tenor_days': 1.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.moneyness', 'timestamp': 1719820680000000000, 'expiry': 0.002743531202435616, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', '100money': 0.40408883767592413, '80money': 0.9380722021577146, '120money': 0.7358087054906611,
            '277money': 1.5922888950965868}, # populated arb_values
        {'tenor_days': 1.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.moneyness', 'timestamp': 1719820740000000000, 'expiry': 0.0027416286149150684, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', '100money': 0.4115472950629692, '80money': 0.9142365366187051, '120money': 0.8430322921524035,
            '277money': 1.963864355802979}, # populated arb_values
        {'tenor_days': 1.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.smile', 'timestamp': 1719820680000000000, 'expiry': 0.002743531202435616, 'from_lookback': True,'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m',
            '3d5delta': 0.47167553458171424}, # populated arb_values
        {'tenor_days': 1.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.smile', 'timestamp': 1719820740000000000, 'expiry': 0.0027416286149150684, 'from_lookback': True,'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m',
            '3d5delta': 0.46780109380515156}, # populated arb_values

    ]
    # fmt: on
    with caplog.at_level(logging.INFO):

        current_and_lookback_results = set_qualified_name_timestamp_index(
            df=pd.concat(
                [
                    input_fixture["current_result"],
                    input_fixture["lookback_data"],
                ],
                ignore_index=True,
            )
        )
        lookback_result = identify_and_populate_arbitrary_values_for_smoothing(
            current_and_lookback_results=current_and_lookback_results,
            current_result=input_fixture["current_result"],
            calc_types=[
                "moneyness",
                "strike",
                "smile",
            ],
            scheduled_version="",
            chunk_start=utils_general.to_iso(1719820800000000000),
        )

    compare_dataframes_row_by_row(
        lookback_result.reset_index().sort_index(axis=1),
        pd.DataFrame(expected_result).sort_index(axis=1),
    )
    assert "Arbitrary Lookback Summary Info" in caplog.text


def test_identify_arbitrary_values_for_smoothing_spiked_params(
    input_fixture: dict[str, pd.DataFrame], caplog: pytest.LogCaptureFixture
) -> None:
    """ """

    # fmt: off
    expected_result = [
        {'tenor_days': 4.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.params', 'timestamp': 1719820800000000000, 'expiry': 0.01095890411, 'forward': 63323.75, 'svi_rho': -0.3792102010621424, 'svi_a': 0.0007657525805969175, 'spot': 63235.496235239676, 'svi_b': 0.0225280083034741, 'svi_sigma': 0.054003418599337924, 'atm_vol': 0.415965608949725, 'svi_m': -0.01687717892074818, 'from_lookback': False, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m'},
        {'tenor_days': 4.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.strike', 'timestamp': 1719820800000000000, 'from_lookback': False, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', '61000strike': 0.4523415376074195, '66000strike': 0.4333235785228331, '696969strike': 1.775799527726476},
        {'tenor_days': 4.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.moneyness', 'timestamp': 1719820800000000000, 'expiry': 0.010958904109589041, 'from_lookback': False, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', '277money': 1.180890526689011, '100money': 0.41596560894192564, '80money': 0.8179096800280996, '120money': 0.5821192654059997},
        {'tenor_days': 4.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.smile', 'timestamp': 1719820800000000000, 'expiry': 0.010958904109589041, 'from_lookback': False, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', '3d5delta': 0.486326312234048},

        {'tenor_days': 1.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.params', 'timestamp': 1719820800000000000, 'expiry': 0.002739726027, 'forward': 63257.54813861788, 'svi_rho': 0.148439952971213, 'svi_a': 0.00023317104820170384, 'spot': 63235.496235239676, 'svi_b': 0.009490116334020892, 'svi_sigma': 0.02171158944581066, 'atm_vol': 0.41478540034441846, 'svi_m': 0.017100257365180446, 'from_lookback': False, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m'},
        {'tenor_days': 1.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.strike', 'timestamp': 1719820800000000000, 'from_lookback': False, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', '61000strike': 0.507382104921187, '66000strike': 0.4623033929377359, '696969strike': 3.0923962106695506},
        {'tenor_days': 1.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.moneyness', 'timestamp': 1719820800000000000, 'expiry': 0.0027397260273972603, 'from_lookback': False, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', '277money': 2.0176582415844933, '100money': 0.4147854003744904, '80money': 0.8928320107277017, '120money': 0.8644593729997618},
        {'tenor_days': 1.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.smile', 'timestamp': 1719820800000000000, 'expiry': 0.0027397260273972603, 'from_lookback': False, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', '3d5delta': 0.1},

        # lookback results - arb_cols should be populated for one timestamp only
        {'tenor_days': 4.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.params', 'timestamp': 1719820680000000000, 'expiry': 0.010962709285, 'forward': 63323.75, 'svi_rho': -0.4141646328901876, 'svi_a': 0.000801429377357313, 'spot': 63239.422231016244, 'svi_b': 0.022112415347691806, 'svi_sigma': 0.05084441366960688, 'atm_vol': 0.4085247173736632, 'contains_recalib_spike': True, 'svi_m': -0.01781071505957095, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', 'expiry_str': '2024-07-05T08:00:00Z'},
        {'tenor_days': 4.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.params', 'timestamp': 1719820740000000000, 'expiry': 0.010960806697, 'forward': 63316.25, 'svi_rho': -0.42561767551672347, 'svi_a': 0.0008402853772529233, 'spot': 63233.14128656461, 'svi_b': 0.02193943888749022, 'svi_sigma': 0.05014943608742622, 'atm_vol': 0.40964969107003174, 'contains_recalib_spike': False, 'svi_m': -0.01904590941394013, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', 'expiry_str': '2024-07-05T08:00:00Z'},

        {'tenor_days': 4.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.strike', 'timestamp': 1719820680000000000, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', '61000strike': 0.4464901384463531, '66000strike': 0.425509804408031,
            '696969strike': np.nan}, # lookback data not populated
        {'tenor_days': 4.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.strike', 'timestamp': 1719820740000000000, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', '61000strike': 0.4460811593833499, '66000strike': 0.4271468437940875,
            '696969strike': 1.690345229269062},

        {'tenor_days': 4.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.moneyness', 'timestamp': 1719820680000000000, 'expiry': 0.010962709284627397, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', '100money': 0.40852471736671503, '80money': 0.8193384801225349, '120money': 0.5678184451309949,
            '277money': np.nan}, # lookback data not populated
        {'tenor_days': 4.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.moneyness', 'timestamp': 1719820740000000000, 'expiry': 0.01096080669710685, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', '100money': 0.40964969107205124, '80money': 0.819278950230451, '120money': 0.5661150240538926,
            '277money': 1.1279853702794376},

        {'tenor_days': 4.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.smile', 'timestamp': 1719820680000000000, 'expiry': 0.010962709284627397, 'from_lookback': True,'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m',
            '3d5delta': np.nan}, # lookback data not populated
        {'tenor_days': 4.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.smile', 'timestamp': 1719820740000000000, 'expiry': 0.01096080669710685, 'from_lookback': True,'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m',
            '3d5delta': 0.47462137796023446},

        {'tenor_days': 1.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.params', 'timestamp': 1719820680000000000, 'expiry': 0.002743531202, 'forward': 63260.51558484025, 'svi_rho': -0.17923184376835205, 'svi_a': 0.0002940442056225631, 'spot': 63239.422231016244, 'svi_b': 0.007977080284092004, 'svi_sigma': 0.018951541515816336, 'atm_vol': 0.4040888376438661, 'contains_recalib_spike': True, 'svi_m': 0.0015701873900868897, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', 'expiry_str': '2024-07-02T08:00:00Z'},
        {'tenor_days': 1.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.params', 'timestamp': 1719820740000000000, 'expiry': 0.002741628615, 'forward': 63253.91904832017, 'svi_rho': 0.07995049465032875, 'svi_a': 0.0001904591996133537, 'spot': 63233.14128656461, 'svi_b': 0.009565467141848387, 'svi_sigma': 0.0262434454540807, 'atm_vol': 0.4115472950692524, 'contains_recalib_spike': False, 'svi_m': 0.014022198579339746, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', 'expiry_str': '2024-07-02T08:00:00Z'},

        {'tenor_days': 1.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.strike', 'timestamp': 1719820680000000000, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', '61000strike': 0.5003152563396541, '66000strike': 0.4655825996461777,
            '696969strike': np.nan}, # lookback data not populated
        {'tenor_days': 1.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.strike', 'timestamp': 1719820740000000000, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', '61000strike': 0.503387478354699, '66000strike': 0.4609993652643708,
            '696969strike': 3.009746323826699},

        {'tenor_days': 1.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.moneyness', 'timestamp': 1719820680000000000, 'expiry': 0.002743531202435616, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', '100money': 0.40408883767592413, '80money': 0.9380722021577146, '120money': 0.7358087054906611,
            '277money': np.nan}, # lookback data not populated
        {'tenor_days': 1.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.moneyness', 'timestamp': 1719820740000000000, 'expiry': 0.0027416286149150684, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', '100money': 0.4115472950629692, '80money': 0.9142365366187051, '120money': 0.8430322921524035,
            '277money': 1.963864355802979},

        {'tenor_days': 1.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.smile', 'timestamp': 1719820680000000000, 'expiry': 0.002743531202435616, 'from_lookback': True,'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m',
            '3d5delta': np.nan}, # lookback data not populated
        {'tenor_days': 1.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.smile', 'timestamp': 1719820740000000000, 'expiry': 0.0027416286149150684, 'from_lookback': True,'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m',
            '3d5delta': 0.46780109380515156},

    ]
    # fmt: on

    input_data = pd.concat(
        [
            input_fixture["current_result"],
            input_fixture["lookback_data"],
        ],
        ignore_index=True,
    )
    input_data.loc[
        input_data["qualified_name"].str.contains("params")
        & input_data[LOOKBACK_COLUMN_NAME]
        & input_data["timestamp"].eq(1719820680000000000),
        SPIKED_PARAMS_COLUMN_NAME,
    ] = True
    input_data = set_qualified_name_timestamp_index(df=input_data)

    calc_types = cast(
        list[VolSurfaceCalcType], ["moneyness", "strike", "smile"]
    )
    with caplog.at_level(logging.INFO):
        lookback_result = identify_and_populate_arbitrary_values_for_smoothing(
            current_and_lookback_results=input_data,
            current_result=input_fixture["current_result"],
            calc_types=calc_types,
            scheduled_version="",
            chunk_start=utils_general.to_iso(1719820800000000000),
        )

    unique_qns = input_data.index.get_level_values("qualified_name").unique()
    non_params_qns = [name for name in unique_qns if "params" not in name]

    info_messages = [
        record.message
        for record in caplog.records
        if record.levelno == logging.INFO
    ]

    assert len(info_messages) == len(calc_types)
    assert all(qn.rstrip(".")[0] in caplog.text for qn in non_params_qns)
    compare_dataframes_row_by_row(
        lookback_result.reset_index().sort_index(axis=1),
        pd.DataFrame(expected_result).sort_index(axis=1),
    )


class TestCheckDataQualityIssues:

    def test_no_quality_issues(self) -> None:
        """Test function with no data quality issues."""
        data = [
            # fmt: off
            # Current data (no lookback) - R2 should be NaN, no spikes
            {LOOKBACK_COLUMN_NAME: False, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: np.nan, 'other_col': 1},
            {LOOKBACK_COLUMN_NAME: False, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: np.nan, 'other_col': 2},
            # === LOOKBACK DATA STARTS ===
            {LOOKBACK_COLUMN_NAME: True, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: 0.8, 'other_col': 3},
            {LOOKBACK_COLUMN_NAME: True, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: 0.9, 'other_col': 4},
            # === LOOKBACK DATA ENDS ===
            # fmt: on
        ]
        df = pd.DataFrame(data)

        filtered_df, num_spiked, num_low_r2, full_lookback_len = (
            _check_data_quality_issues_and_filter_df(df)
        )

        assert len(filtered_df) == 4
        assert num_spiked is None
        assert num_low_r2 is None
        assert full_lookback_len == 2
        pd.testing.assert_frame_equal(filtered_df, df)

    def test_spiked_rows_only(self) -> None:
        """Test function with only spiked parameter rows in lookback."""
        data = [
            # fmt: off
            # Current data (no lookback) - R2 should be NaN, no spikes
            {LOOKBACK_COLUMN_NAME: False, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: np.nan, 'other_col': 1},
            {LOOKBACK_COLUMN_NAME: False, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: np.nan, 'other_col': 2},
            # === LOOKBACK DATA STARTS ===
            {LOOKBACK_COLUMN_NAME: True, SPIKED_PARAMS_COLUMN_NAME: True, R2_SMOOTHCALIB_COLUMN_NAME: 0.8, 'other_col': 3},  # SPIKED
            {LOOKBACK_COLUMN_NAME: True, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: 0.9, 'other_col': 4},
            {LOOKBACK_COLUMN_NAME: True, SPIKED_PARAMS_COLUMN_NAME: True, R2_SMOOTHCALIB_COLUMN_NAME: 0.85, 'other_col': 5},
            # SPIKED
            # === LOOKBACK DATA ENDS ===
            # fmt: on
        ]
        df = pd.DataFrame(data)

        filtered_df, num_spiked, num_low_r2, full_lookback_len = (
            _check_data_quality_issues_and_filter_df(df)
        )

        assert len(filtered_df) == 3  # 2 spiked rows should be filtered out
        assert num_spiked == 2
        assert num_low_r2 is None
        assert full_lookback_len == 3
        # Check that spiked rows are removed
        assert not filtered_df[SPIKED_PARAMS_COLUMN_NAME].any()

    def test_low_r2_rows_only(self) -> None:
        """Test function with only low R2 rows in lookback."""
        data = [
            # fmt: off
            # Current data (no lookback) - R2 should be NaN, no spikes
            {LOOKBACK_COLUMN_NAME: False, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: np.nan, 'other_col': 1},
            {LOOKBACK_COLUMN_NAME: False, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: np.nan, 'other_col': 2},
            # === LOOKBACK DATA STARTS ===
            {LOOKBACK_COLUMN_NAME: True, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: 0.6, 'other_col': 3},   # LOW R2
            {LOOKBACK_COLUMN_NAME: True, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: 0.9, 'other_col': 4},
            {LOOKBACK_COLUMN_NAME: True, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: 0.5, 'other_col': 5},   # LOW R2
            {LOOKBACK_COLUMN_NAME: True, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: 0.75, 'other_col': 6},
            # === LOOKBACK DATA ENDS ===
            # fmt: on
        ]
        df = pd.DataFrame(data)

        filtered_df, num_spiked, num_low_r2, full_lookback_len = (
            _check_data_quality_issues_and_filter_df(df)
        )

        assert len(filtered_df) == 4  # 2 low R2 rows should be filtered out
        assert num_spiked is None
        assert num_low_r2 == 2
        assert full_lookback_len == 4
        # Check that all remaining lookback rows have R2 >= 0.7
        lookback_rows = filtered_df[filtered_df[LOOKBACK_COLUMN_NAME] == True]
        assert all(lookback_rows[R2_SMOOTHCALIB_COLUMN_NAME] >= 0.7)

    def test_both_spiked_and_low_r2_rows(self) -> None:
        """Test function with both spiked and low R2 rows in lookback."""
        data = [
            # fmt: off
            # Current data (no lookback) - R2 should be NaN, no spikes
            {LOOKBACK_COLUMN_NAME: False, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: np.nan, 'other_col': 1},
            {LOOKBACK_COLUMN_NAME: False, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: np.nan, 'other_col': 2},
            # === LOOKBACK DATA STARTS ===
            {LOOKBACK_COLUMN_NAME: True, SPIKED_PARAMS_COLUMN_NAME: True, R2_SMOOTHCALIB_COLUMN_NAME: 0.8, 'other_col': 3},   # SPIKED
            {LOOKBACK_COLUMN_NAME: True, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: 0.6, 'other_col': 4},  # LOW R2 (after spike filter)
            {LOOKBACK_COLUMN_NAME: True, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: 0.85, 'other_col': 5},
            {LOOKBACK_COLUMN_NAME: True, SPIKED_PARAMS_COLUMN_NAME: True, R2_SMOOTHCALIB_COLUMN_NAME: 0.5, 'other_col': 6},   # SPIKED
            {LOOKBACK_COLUMN_NAME: True, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: 0.75, 'other_col': 7},
            # === LOOKBACK DATA ENDS ===
            # fmt: on
        ]
        df = pd.DataFrame(data)

        filtered_df, num_spiked, num_low_r2, full_lookback_len = (
            _check_data_quality_issues_and_filter_df(df)
        )

        assert len(filtered_df) == 4  # 2 current + 2 remaining lookback rows
        assert num_spiked == 2
        assert (
            num_low_r2 == 1
        )  # Only count low R2 from remaining rows after spike filtering
        assert full_lookback_len == 5
        # Check that no spiked rows remain and all R2 values are >= 0.7
        assert not filtered_df[SPIKED_PARAMS_COLUMN_NAME].any()
        lookback_rows = filtered_df[filtered_df[LOOKBACK_COLUMN_NAME] == True]
        assert all(lookback_rows[R2_SMOOTHCALIB_COLUMN_NAME] >= 0.7)

    def test_r2_column_missing(self) -> None:
        """Test function when R2_smoothcalib column is missing."""
        data = [
            # fmt: off
            # Current data (no lookback) - no spikes
            {LOOKBACK_COLUMN_NAME: False, SPIKED_PARAMS_COLUMN_NAME: False, 'other_col': 1},
            {LOOKBACK_COLUMN_NAME: False, SPIKED_PARAMS_COLUMN_NAME: False, 'other_col': 2},
            # === LOOKBACK DATA STARTS ===
            {LOOKBACK_COLUMN_NAME: True, SPIKED_PARAMS_COLUMN_NAME: True, 'other_col': 3},   # SPIKED
            {LOOKBACK_COLUMN_NAME: True, SPIKED_PARAMS_COLUMN_NAME: False, 'other_col': 4},
            # === LOOKBACK DATA ENDS ===
            # fmt: on
        ]
        df = pd.DataFrame(data)

        filtered_df, num_spiked, num_low_r2, full_lookback_len = (
            _check_data_quality_issues_and_filter_df(df)
        )

        assert len(filtered_df) == 3  # Only spiked row should be removed
        assert num_spiked == 1
        assert num_low_r2 is None  # Should be None when column is missing
        assert full_lookback_len == 2

    def test_r2_with_nan_values_in_lookback(self) -> None:
        """Test function with NaN values in R2_smoothcalib column within lookback."""
        data = [
            # fmt: off
            # Current data (no lookback) - R2 should be NaN, no spikes
            {LOOKBACK_COLUMN_NAME: False, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: np.nan, 'other_col': 1},
            {LOOKBACK_COLUMN_NAME: False, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: np.nan, 'other_col': 2},
            # === LOOKBACK DATA STARTS ===
            {LOOKBACK_COLUMN_NAME: True, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: np.nan, 'other_col': 3},  # NaN in lookback
            {LOOKBACK_COLUMN_NAME: True, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: 0.6, 'other_col': 4},    # LOW R2
            {LOOKBACK_COLUMN_NAME: True, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: np.nan, 'other_col': 5},  # NaN in lookback
            {LOOKBACK_COLUMN_NAME: True, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: 0.8, 'other_col': 6},
            # === LOOKBACK DATA ENDS ===
            # fmt: on
        ]
        df = pd.DataFrame(data)

        filtered_df, num_spiked, num_low_r2, full_lookback_len = (
            _check_data_quality_issues_and_filter_df(df)
        )

        assert (
            len(filtered_df) == 5
        )  # Should remove 1 row with R2 < 0.7 (excluding NaN)
        assert num_spiked is None
        assert num_low_r2 == 1  # Only non-NaN values below 0.7
        assert full_lookback_len == 4
        # Check that NaN values are preserved and low R2 values are removed
        lookback_rows = filtered_df[filtered_df[LOOKBACK_COLUMN_NAME] == True]
        non_nan_r2_values = lookback_rows[R2_SMOOTHCALIB_COLUMN_NAME].dropna()
        assert all(non_nan_r2_values >= 0.7)
        assert (
            lookback_rows[R2_SMOOTHCALIB_COLUMN_NAME].isna().sum() == 2
        )  # Two NaN values should remain

    def test_only_current_data_no_lookback(self) -> None:
        """Test function when all lookback values are False."""
        data = [
            # fmt: off
            # Current data only (no lookback) - R2 should be NaN, no spikes allowed
            {LOOKBACK_COLUMN_NAME: False, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: np.nan, 'other_col': 1},
            {LOOKBACK_COLUMN_NAME: False, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: np.nan, 'other_col': 2},
            {LOOKBACK_COLUMN_NAME: False, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: np.nan, 'other_col': 3},
            {LOOKBACK_COLUMN_NAME: False, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: np.nan, 'other_col': 4},
            # === NO LOOKBACK DATA ===
            # fmt: on
        ]
        df = pd.DataFrame(data)

        filtered_df, num_spiked, num_low_r2, full_lookback_len = (
            _check_data_quality_issues_and_filter_df(df)
        )

        assert len(filtered_df) == 4  # All current data should remain
        assert num_spiked is None
        assert num_low_r2 is None
        assert full_lookback_len == 0  # No lookback rows

    def test_r2_exactly_threshold(self) -> None:
        """Test function with R2 values exactly at the 0.7 threshold."""
        data = [
            # fmt: off
            # Current data (no lookback) - R2 should be NaN, no spikes
            {LOOKBACK_COLUMN_NAME: False, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: np.nan, 'other_col': 1},
            {LOOKBACK_COLUMN_NAME: False, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: np.nan, 'other_col': 2},
            # === LOOKBACK DATA STARTS ===
            {LOOKBACK_COLUMN_NAME: True, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: 0.7, 'other_col': 3},      # EXACTLY threshold
            {LOOKBACK_COLUMN_NAME: True, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: 0.69999, 'other_col': 4},  # BELOW threshold
            {LOOKBACK_COLUMN_NAME: True, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: 0.70001, 'other_col': 5},  # ABOVE threshold
            {LOOKBACK_COLUMN_NAME: True, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: 0.7, 'other_col': 6},
            # EXACTLY threshold
            # === LOOKBACK DATA ENDS ===
            # fmt: on
        ]
        df = pd.DataFrame(data)

        filtered_df, num_spiked, num_low_r2, full_lookback_len = (
            _check_data_quality_issues_and_filter_df(df)
        )

        assert (
            len(filtered_df) == 5
        )  # Only one row with 0.69999 should be removed
        assert num_spiked is None
        assert num_low_r2 == 1
        assert full_lookback_len == 4
        # Check that 0.7 is considered valid (not filtered out)
        lookback_rows = filtered_df[filtered_df[LOOKBACK_COLUMN_NAME] == True]
        assert 0.7 in lookback_rows[R2_SMOOTHCALIB_COLUMN_NAME].values
        assert 0.69999 not in lookback_rows[R2_SMOOTHCALIB_COLUMN_NAME].values

    def test_complex_mixed_scenario(self) -> None:
        """Test a complex scenario with various combinations."""
        data = [
            # fmt: off
            # Current data (no lookback) - R2 should be NaN, no spikes
            {LOOKBACK_COLUMN_NAME: False, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: np.nan, 'other_col': 1},
            {LOOKBACK_COLUMN_NAME: False, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: np.nan, 'other_col': 2},
            {LOOKBACK_COLUMN_NAME: False, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: np.nan, 'other_col': 3},
            # === LOOKBACK DATA STARTS ===
            {LOOKBACK_COLUMN_NAME: True, SPIKED_PARAMS_COLUMN_NAME: True, R2_SMOOTHCALIB_COLUMN_NAME: 0.8, 'other_col': 4},    # SPIKED (good R2)
            {LOOKBACK_COLUMN_NAME: True, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: 0.6, 'other_col': 5},   # LOW R2
            {LOOKBACK_COLUMN_NAME: True, SPIKED_PARAMS_COLUMN_NAME: True, R2_SMOOTHCALIB_COLUMN_NAME: 0.5, 'other_col': 6},    # SPIKED (bad R2)
            {LOOKBACK_COLUMN_NAME: True, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: np.nan, 'other_col': 7}, # NaN R2
            {LOOKBACK_COLUMN_NAME: True, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: 0.75, 'other_col': 8},  # GOOD
            {LOOKBACK_COLUMN_NAME: True, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: 0.65, 'other_col': 9},  # LOW R2
            {LOOKBACK_COLUMN_NAME: True, SPIKED_PARAMS_COLUMN_NAME: False, R2_SMOOTHCALIB_COLUMN_NAME: 0.85, 'other_col': 10},
            # GOOD
            # === LOOKBACK DATA ENDS ===
            # fmt: on
        ]
        df = pd.DataFrame(data)

        filtered_df, num_spiked, num_low_r2, full_lookback_len = (
            _check_data_quality_issues_and_filter_df(df)
        )

        # After spike removal: rows 4 and 6 removed (2 spiked rows)
        # Remaining lookback rows: 5, 7, 8, 9, 10
        # After R2 filtering: rows 5 and 9 removed (R2 < 0.7)
        # Final remaining: 3 current + 3 lookback = 6 rows

        assert len(filtered_df) == 6
        assert num_spiked == 2  # Two spiked rows removed
        assert num_low_r2 == 2  # Two low R2 rows removed from remaining data
        assert full_lookback_len == 7  # Total lookback rows initially

        # Verify no spikes remain
        assert not filtered_df[SPIKED_PARAMS_COLUMN_NAME].any()

        # Verify all remaining lookback R2 values are >= 0.7 or NaN
        lookback_rows = filtered_df[filtered_df[LOOKBACK_COLUMN_NAME] == True]
        non_nan_r2 = lookback_rows[R2_SMOOTHCALIB_COLUMN_NAME].dropna()
        assert all(non_nan_r2 >= 0.7)
