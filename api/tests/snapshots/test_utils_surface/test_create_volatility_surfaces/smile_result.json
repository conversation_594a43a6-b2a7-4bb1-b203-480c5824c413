[{"-10delta": 0.62246444, "-15delta": 0.58628927, "-1delta": 0.94325996, "-20delta": 0.57010383, "-25delta": 0.56334234, "-2delta": 0.84386722, "-30delta": 0.56165094, "-35delta": 0.56301028, "-3delta": 0.78430667, "-40delta": 0.56640424, "-45delta": 0.57129906, "-4delta": 0.7422275, "-50delta": 0.5774195, "-5delta": 0.71028848, "10delta": 0.6857587, "15delta": 0.66034256, "1delta": 0.81061561, "20delta": 0.64144658, "25delta": 0.62626555, "27d77delta": 0.61896203, "2delta": 0.77578611, "30delta": 0.61353616, "35delta": 0.60259216, "3delta": 0.75445243, "40delta": 0.59305609, "45delta": 0.58471702, "46delta": 0.58318267, "4delta": 0.73882249, "50delta": 0.57747956, "5delta": 0.72638741, "atm": 0.57665178, "expiry": 0.00273973, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "BTC-24MAY24", "tenor_days": 1, "timestamp": 1716451200000000000, "upper_bordering_expiry": "BTC-24MAY24"}, {"-10delta": 0.55869883, "-15delta": 0.52754611, "-1delta": 0.78274821, "-20delta": 0.51218493, "-25delta": 0.50544456, "-2delta": 0.71596022, "-30delta": 0.50371571, "-35delta": 0.50506142, "-3delta": 0.67569272, "-40delta": 0.50842338, "-45delta": 0.51322084, "-4delta": 0.6468439, "-50delta": 0.51914393, "-5delta": 0.62451491, "10delta": 0.61766739, "15delta": 0.59520858, "1delta": 0.72571442, "20delta": 0.57833346, "25delta": 0.56463425, "27d77delta": NaN, "2delta": 0.69583329, "30delta": 0.55302532, "35delta": 0.54293471, "3delta": 0.67745193, "40delta": 0.53404071, "45delta": 0.5261684, "46delta": 0.52470877, "4delta": 0.66393836, "50delta": 0.51924849, "5delta": 0.65315438, "atm": 0.51822208, "expiry": 0.00547945, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "SYN.BTC-25MAY24", "tenor_days": 2, "timestamp": 1716451200000000000, "upper_bordering_expiry": "SYN.BTC-25MAY24"}, {"-10delta": 0.55392177, "-15delta": 0.52914594, "-1delta": 0.74631437, "-20delta": 0.51561431, "-25delta": 0.50802569, "-2delta": 0.68544829, "-30delta": 0.50406974, "-35delta": 0.50257167, "-3delta": 0.6499749, "-40delta": 0.50288105, "-45delta": 0.50462449, "-4delta": 0.62528009, "-50delta": 0.50759291, "-5delta": 0.60662995, "10delta": 0.58864952, "15delta": 0.56762183, "1delta": 0.69797272, "20delta": 0.55248321, "25delta": 0.54073211, "27d77delta": NaN, "2delta": 0.66681237, "30delta": 0.53125168, "35delta": 0.52346005, "3delta": 0.64792445, "40delta": 0.51703372, "45delta": 0.51179732, "46delta": NaN, "4delta": 0.63420531, "50delta": 0.50767649, "5delta": 0.62337458, "atm": 0.5070012, "expiry": 0.00821918, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "SYN.BTC-25MAY24", "tenor_days": 3, "timestamp": 1716451200000000000, "upper_bordering_expiry": "BTC-31MAY24"}, {"-10delta": 0.54967198, "-15delta": 0.52758327, "-1delta": 0.73247557, "-20delta": 0.51513442, "-25delta": 0.50768032, "-2delta": 0.67176196, "-30delta": 0.50328242, "-35delta": 0.50098176, "-3delta": 0.63753498, "-40delta": 0.50025838, "-45delta": 0.50082109, "-4delta": 0.61429074, "-50delta": 0.50251492, "-5delta": 0.59706294, "10delta": 0.57429001, "15delta": 0.55377414, "1delta": 0.69072058, "20delta": 0.5395942, "25delta": 0.52900889, "27d77delta": NaN, "2delta": 0.65631073, "30delta": 0.52080196, "35delta": 0.51433992, "3delta": 0.63584524, "40delta": 0.50926656, "45delta": 0.50538101, "46delta": NaN, "4delta": 0.62120341, "50delta": 0.50258417, "5delta": 0.60979482, "atm": 0.50210068, "expiry": 0.0109589, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "SYN.BTC-25MAY24", "tenor_days": 4, "timestamp": 1716451200000000000, "upper_bordering_expiry": "BTC-31MAY24"}, {"-10delta": 0.54682787, "-15delta": 0.52579325, "-1delta": 0.73073634, "-20delta": 0.51379457, "-25delta": 0.50641807, "-2delta": 0.66714319, "-30delta": 0.50185943, "-35delta": 0.49923755, "-3delta": 0.63237821, "-40delta": 0.49807982, "-45delta": 0.49812545, "-4delta": 0.60925645, "-50delta": 0.49924029, "-5delta": 0.59237005, "10delta": 0.56696986, "15delta": 0.54632843, "1delta": 0.69413796, "20delta": 0.53250972, "25delta": 0.52248072, "27d77delta": NaN, "2delta": 0.65517257, "30delta": 0.51491266, "35delta": 0.50911912, "3delta": 0.63246652, "40delta": 0.50471495, "45delta": 0.50148072, "46delta": NaN, "4delta": 0.61647553, "50delta": 0.499303, "5delta": 0.60417926, "atm": 0.49891477, "expiry": 0.01369863, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "SYN.BTC-25MAY24", "tenor_days": 5, "timestamp": 1716451200000000000, "upper_bordering_expiry": "BTC-31MAY24"}, {"-10delta": 0.54512172, "-15delta": 0.52432854, "-1delta": 0.73690612, "-20delta": 0.51242593, "-25delta": 0.50502327, "-2delta": 0.668133, "-30delta": 0.50035103, "-35delta": 0.49755259, "-3delta": 0.63163081, "-40delta": 0.4961717, "-45delta": 0.49595888, "-4delta": 0.6078109, "-50delta": 0.4967885, "-5delta": 0.59063857, "10delta": 0.5635716, "15delta": 0.54232243, "1delta": 0.70463504, "20delta": 0.5284403, "25delta": 0.51856557, "27d77delta": NaN, "2delta": 0.65988947, "30delta": 0.51124884, "35delta": 0.50574958, "3delta": 0.63435793, "40delta": 0.50165497, "45delta": 0.49872971, "46delta": NaN, "4delta": 0.61665692, "50delta": 0.49684992, "5delta": 0.60321821, "atm": 0.49650661, "expiry": 0.01643836, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "SYN.BTC-25MAY24", "tenor_days": 6, "timestamp": 1716451200000000000, "upper_bordering_expiry": "BTC-31MAY24"}, {"-10delta": 0.54426068, "-15delta": 0.52325641, "-1delta": 0.74928342, "-20delta": 0.51124607, "-25delta": 0.5037425, "-2delta": 0.67307343, "-30delta": 0.49895912, "-35delta": 0.49603757, "-3delta": 0.6338431, "-40delta": 0.4945233, "-45delta": 0.49416953, "-4delta": 0.60872507, "-50delta": 0.49485386, "-5delta": 0.59084544, "10delta": 0.56256542, "15delta": 0.54034987, "1delta": 0.72076982, "20delta": 0.52611731, "25delta": 0.51614393, "27d77delta": NaN, "2delta": 0.66885599, "30delta": 0.50884852, "35delta": 0.50343227, "3delta": 0.63987821, "40delta": 0.49945305, "45delta": 0.49665938, "46delta": NaN, "4delta": 0.62010496, "50delta": 0.49491763, "5delta": 0.60528022, "atm": 0.4945909, "expiry": 0.01917808, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "SYN.BTC-25MAY24", "tenor_days": 7, "timestamp": 1716451200000000000, "upper_bordering_expiry": "BTC-31MAY24"}, {"-10delta": 0.54405755, "-15delta": 0.52253418, "-1delta": 0.76740774, "-20delta": 0.51027374, "-25delta": 0.50260888, "-2delta": 0.68127097, "-30delta": 0.49770242, "-35delta": 0.49467787, "-3delta": 0.63836257, "-40delta": 0.49307363, "-45delta": 0.4926406, "-4delta": 0.61142857, "-50delta": 0.49325674, "-5delta": 0.59250402, "10delta": 0.56318974, "15delta": 0.53971391, "1delta": 0.74207978, "20delta": 0.52491627, "25delta": 0.51466718, "27d77delta": NaN, "2delta": 0.68136836, "30delta": 0.50724012, "35delta": 0.50177216, "3delta": 0.64825429, "40delta": 0.49778898, "45delta": 0.49502148, "46delta": 0.49459949, "4delta": 0.62602392, "50delta": 0.49332587, "5delta": 0.60956434, "atm": 0.49299704, "expiry": 0.02191781, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "BTC-31MAY24", "tenor_days": 8, "timestamp": 1716451200000000000, "upper_bordering_expiry": "BTC-31MAY24"}, {"-10delta": 0.54376212, "-15delta": 0.52614249, "-1delta": 0.80577644, "-20delta": 0.51646599, "-25delta": 0.51062772, "-2delta": 0.67876282, "-30delta": 0.50706516, "-35delta": 0.50504775, "-3delta": 0.63083246, "-40delta": 0.50419671, "-45delta": 0.50431114, "-4delta": 0.60392627, "-50delta": 0.50529587, "-5delta": 0.58613378, "10delta": 0.56951465, "15delta": 0.54861154, "1delta": 0.73682614, "20delta": 0.53534906, "25delta": 0.52605849, "27d77delta": NaN, "2delta": 0.67732305, "30delta": 0.51921945, "35delta": 0.51407408, "3delta": 0.64628751, "40delta": 0.51020494, "45delta": 0.50737307, "46delta": NaN, "4delta": 0.62589634, "50delta": 0.50544753, "5delta": 0.61098308, "atm": 0.50486143, "expiry": 0.03835616, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "BTC-31MAY24", "tenor_days": 14, "timestamp": 1716451200000000000, "upper_bordering_expiry": "SYN.BTC-7JUN24"}, {"-10delta": 0.54272425, "-15delta": 0.52530992, "-1delta": 0.83044751, "-20delta": 0.51588538, "-25delta": 0.51029112, "-2delta": 0.68312333, "-30delta": 0.50695767, "-35delta": 0.50515554, "-3delta": 0.63179863, "-40delta": 0.50450838, "-45delta": 0.50481771, "-4delta": 0.60375158, "-50delta": 0.50599034, "-5delta": 0.58546931, "10delta": 0.57151875, "15delta": 0.55055825, "1delta": 0.73476388, "20delta": 0.53715427, "25delta": 0.52769653, "27d77delta": NaN, "2delta": 0.67747674, "30delta": 0.52068126, "35delta": 0.51535592, "3delta": 0.64728776, "40delta": 0.51130447, "45delta": 0.50828776, "46delta": 0.50779551, "4delta": 0.62732382, "50delta": 0.50617381, "5delta": 0.6126536, "atm": 0.50547784, "expiry": 0.04109589, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "SYN.BTC-7JUN24", "tenor_days": 15, "timestamp": 1716451200000000000, "upper_bordering_expiry": "SYN.BTC-7JUN24"}, {"-10delta": 0.56840295, "-15delta": 0.55241275, "-1delta": 0.77744599, "-20delta": 0.54393348, "-25delta": 0.53912142, "-2delta": 0.68682073, "-30delta": 0.53650056, "-35delta": 0.53537997, "-3delta": 0.64682689, "-40delta": 0.53540315, "-45delta": 0.5363837, "-4delta": 0.62318584, "-50delta": 0.53823679, "-5delta": 0.60718043, "10delta": 0.6117066, "15delta": 0.58973403, "1delta": 0.7613456, "20delta": 0.57522093, "25delta": 0.564703, "27d77delta": NaN, "2delta": 0.71257735, "30delta": 0.55669747, "35delta": 0.5504468, "3delta": 0.68537681, "40delta": 0.54552429, "45delta": 0.54167928, "46delta": NaN, "4delta": 0.66675408, "50delta": 0.53876834, "5delta": 0.65272592, "atm": 0.5371872, "expiry": 0.08219178, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "SYN.BTC-7JUN24", "tenor_days": 30, "timestamp": 1716451200000000000, "upper_bordering_expiry": "BTC-28JUN24"}, {"-10delta": 0.57051337, "-15delta": 0.55554761, "-1delta": 0.74633831, "-20delta": 0.54762004, "-25delta": 0.54318556, "-2delta": 0.67500626, "-30delta": 0.54086164, "-35delta": 0.53999579, "-3delta": 0.64118708, "-40delta": 0.54025018, "-45delta": 0.54144858, "-4delta": 0.62046759, "-50delta": 0.54351211, "-5delta": 0.60613882, "10delta": 0.61911472, "15delta": 0.59698244, "1delta": 0.76400723, "20delta": 0.58221497, "25delta": 0.57142512, "27d77delta": NaN, "2delta": 0.71779121, "30delta": 0.56315026, "35delta": 0.55663789, "3delta": 0.69161888, "40delta": 0.55146137, "45delta": 0.54736803, "46delta": 0.54666501, "4delta": 0.67352225, "50delta": 0.54421131, "5delta": 0.65979026, "atm": 0.54226528, "expiry": 0.09863014, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "BTC-28JUN24", "tenor_days": 36, "timestamp": 1716451200000000000, "upper_bordering_expiry": "BTC-28JUN24"}, {"-10delta": 0.5997778, "-15delta": 0.58186959, "-1delta": 0.78418472, "-20delta": 0.57318342, "-25delta": 0.56901709, "-2delta": 0.7195766, "-30delta": 0.56748869, "-35delta": 0.56769983, "-3delta": 0.6840388, "-40delta": 0.56917733, "-45delta": 0.57166181, "-4delta": 0.66051708, "-50delta": 0.57501612, "-5delta": 0.64352779, "10delta": 0.65255732, "15delta": 0.6336944, "1delta": 0.74975721, "20delta": 0.61998829, "25delta": 0.60921908, "27d77delta": NaN, "2delta": 0.72207864, "30delta": 0.60039005, "35delta": 0.59297474, "3delta": 0.70530724, "40delta": 0.58667113, "45delta": 0.58130271, "46delta": NaN, "4delta": 0.69312214, "50delta": 0.57677456, "5delta": 0.68349695, "atm": 0.57251184, "expiry": 0.16438356, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "BTC-28JUN24", "tenor_days": 60, "timestamp": 1716451200000000000, "upper_bordering_expiry": "SYN.BTC-26JUL24"}, {"-10delta": 0.6026966, "-15delta": 0.5841286, "-1delta": 0.78583588, "-20delta": 0.57524431, "-25delta": 0.57111735, "-2delta": 0.72349894, "-30delta": 0.56974403, "-35delta": 0.5701611, "-3delta": 0.68849378, "-40delta": 0.57185929, "-45delta": 0.57455766, "-4delta": 0.6649552, "-50delta": 0.57810491, "-5delta": 0.64774903, "10delta": 0.65473459, "15delta": 0.63663015, "1delta": 0.74584929, "20delta": 0.62333305, "25delta": 0.61277915, "27d77delta": NaN, "2delta": 0.72016984, "30delta": 0.60404001, "35delta": 0.59662502, "3delta": 0.70452487, "40delta": 0.59025339, "45delta": 0.58476246, "46delta": 0.58376136, "4delta": 0.69311, "50delta": 0.58006732, "5delta": 0.68406056, "atm": 0.57538906, "expiry": 0.17534247, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "SYN.BTC-26JUL24", "tenor_days": 64, "timestamp": 1716451200000000000, "upper_bordering_expiry": "SYN.BTC-26JUL24"}, {"-10delta": 0.61780358, "-15delta": 0.59938078, "-1delta": 0.7799616, "-20delta": 0.58995188, "-25delta": 0.58517608, "-2delta": 0.72608786, "-30delta": 0.58321773, "-35delta": 0.58316236, "-3delta": 0.69560653, "-40delta": 0.58451138, "-45delta": 0.5869825, "-4delta": 0.67487283, "-50delta": 0.59042037, "-5delta": 0.65951529, "10delta": 0.67576051, "15delta": 0.65525868, "1delta": 0.78071608, "20delta": 0.6403343, "25delta": 0.62859436, "27d77delta": NaN, "2delta": 0.75093612, "30delta": 0.61896369, "35delta": 0.61087427, "3delta": 0.73285293, "40delta": 0.60400027, "45delta": 0.5981512, "46delta": NaN, "4delta": 0.71969497, "50delta": 0.59322411, "5delta": 0.70928888, "atm": 0.5873968, "expiry": 0.24657534, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "SYN.BTC-26JUL24", "tenor_days": 90, "timestamp": 1716451200000000000, "upper_bordering_expiry": "BTC-27SEP24"}, {"-10delta": 0.62507209, "-15delta": 0.60737404, "-1delta": 0.77497274, "-20delta": 0.59807539, "-25delta": 0.59323034, "-2delta": 0.72543119, "-30delta": 0.59115423, "-35delta": 0.59099906, "-3delta": 0.69741057, "-40delta": 0.59229861, "-45delta": 0.59478827, "-4delta": 0.67830814, "-50delta": 0.59832401, "-5delta": 0.66410916, "10delta": 0.69575363, "15delta": 0.67209514, "1delta": 0.8190498, "20delta": 0.65502079, "25delta": 0.6416974, "27d77delta": NaN, "2delta": 0.78381593, "30delta": 0.63085171, "35delta": 0.62180914, "3delta": 0.76249591, "40delta": 0.61417952, "45delta": 0.60772981, "46delta": NaN, "4delta": 0.74702792, "50delta": 0.60232635, "5delta": 0.73482663, "atm": 0.59488854, "expiry": 0.32876712, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "SYN.BTC-26JUL24", "tenor_days": 120, "timestamp": 1716451200000000000, "upper_bordering_expiry": "BTC-27SEP24"}, {"-10delta": 0.62608638, "-15delta": 0.60854236, "-1delta": 0.77418942, "-20delta": 0.59930065, "-25delta": 0.59447563, "-2delta": 0.72523869, "-30delta": 0.59240777, "-35delta": 0.59226131, "-3delta": 0.69756738, "-40delta": 0.59357681, "-45delta": 0.59609388, "-4delta": 0.67870446, "-50delta": 0.59967156, "-5delta": 0.66468114, "10delta": 0.70016413, "15delta": 0.67572087, "1delta": 0.82803832, "20delta": 0.65811205, "25delta": 0.64439401, "27d77delta": NaN, "2delta": 0.79144123, "30delta": 0.63324368, "35delta": 0.62395954, "3delta": 0.76931246, "40delta": 0.61613504, "45delta": 0.60952623, "46delta": 0.60833647, "4delta": 0.75326764, "50delta": 0.60399145, "5delta": 0.74061839, "atm": 0.5961322, "expiry": 0.34794521, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "BTC-27SEP24", "tenor_days": 127, "timestamp": 1716451200000000000, "upper_bordering_expiry": "BTC-27SEP24"}, {"-10delta": 0.64453576, "-15delta": 0.62448044, "-1delta": 0.80080496, "-20delta": 0.61439184, "-25delta": 0.60977878, "-2delta": 0.75189415, "-30delta": 0.60856028, "-35delta": 0.60962352, "-3delta": 0.72325095, "-40delta": 0.61232955, "-45delta": 0.61630074, "-4delta": 0.70318999, "-50delta": 0.62131719, "-5delta": 0.68795181, "10delta": 0.73227953, "15delta": 0.70839703, "1delta": 0.84898518, "20delta": 0.69059456, "25delta": 0.67625217, "27d77delta": NaN, "2delta": 0.81650161, "30delta": 0.66418571, "35delta": 0.65376514, "3delta": 0.79658307, "40delta": 0.64462544, "45delta": 0.63654962, "46delta": NaN, "4delta": 0.78197675, "50delta": 0.62941617, "5delta": 0.77034687, "atm": 0.61558921, "expiry": 0.49315068, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "BTC-27SEP24", "tenor_days": 180, "timestamp": 1716451200000000000, "upper_bordering_expiry": "BTC-27DEC24"}, {"-10delta": 0.65348788, "-15delta": 0.63124382, "-1delta": 0.81387272, "-20delta": 0.62016768, "-25delta": 0.61549764, "-2delta": 0.76535426, "-30delta": 0.61479091, "-35delta": 0.61664725, "-3delta": 0.73644314, "-40delta": 0.62024165, "-45delta": 0.6250881, "-4delta": 0.71587903, "-50delta": 0.63090699, "-5delta": 0.70003588, "10delta": 0.74661418, "15delta": 0.72326275, "1delta": 0.85801168, "20delta": 0.70562827, "25delta": 0.6912291, "27d77delta": NaN, "2delta": 0.82730403, "30delta": 0.67893863, "35delta": 0.66815353, "3delta": 0.80838505, "40delta": 0.65852086, "45delta": 0.64982778, "46delta": 0.64818988, "4delta": 0.79445832, "50delta": 0.64195232, "5delta": 0.7833318, "atm": 0.62375441, "expiry": 0.59726027, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "BTC-27DEC24", "tenor_days": 218, "timestamp": 1716451200000000000, "upper_bordering_expiry": "BTC-27DEC24"}, {"-10delta": 0.65365458, "-15delta": 0.63248989, "-1delta": 0.79622895, "-20delta": 0.62162046, "-25delta": 0.61704774, "-2delta": 0.7538758, "-30delta": 0.61655722, "-35delta": 0.61875417, "-3delta": 0.72846203, "-40delta": 0.62276125, "-45delta": 0.62804406, "-4delta": 0.71025277, "-50delta": 0.6342905, "-5delta": 0.69611597, "10delta": 0.75779162, "15delta": 0.73375307, "1delta": 0.87170918, "20delta": 0.71553012, "25delta": 0.70058908, "27d77delta": NaN, "2delta": 0.84038801, "30delta": 0.68777716, "35delta": 0.67647458, "3delta": 0.82106705, "40delta": 0.666316, "45delta": 0.65707783, "46delta": NaN, "4delta": 0.80682995, "50delta": 0.64862768, "5delta": 0.79544515, "atm": 0.62603212, "expiry": 0.73972603, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "BTC-27DEC24", "tenor_days": 270, "timestamp": 1716451200000000000, "upper_bordering_expiry": "BTC-28MAR25"}, {"-10delta": 0.65326592, "-15delta": 0.63289163, "-1delta": 0.78558272, "-20delta": 0.62222491, "-25delta": 0.61774238, "-2delta": 0.74665057, "-30delta": 0.61740695, "-35delta": 0.61984198, "-3delta": 0.7232041, "-40delta": 0.6241364, "-45delta": 0.62972166, "-4delta": 0.70634133, "-50delta": 0.63626306, "-5delta": 0.69319868, "10delta": 0.76580753, "15delta": 0.7411881, "1delta": 0.88213921, "20delta": 0.72249121, "25delta": 0.70713071, "27d77delta": NaN, "2delta": 0.85018887, "30delta": 0.69392835, "35delta": 0.68224892, "3delta": 0.83046983, "40delta": 0.67171598, "45delta": 0.66209662, "46delta": 0.66026865, "4delta": 0.8159333, "50delta": 0.65324951, "5delta": 0.80430463, "atm": 0.6272339, "expiry": 0.84657534, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "BTC-28MAR25", "tenor_days": 309, "timestamp": 1716451200000000000, "upper_bordering_expiry": "BTC-28MAR25"}, {"-10delta": 0.6599047, "-15delta": 0.63673772, "-1delta": 0.80339812, "-20delta": 0.62406794, "-25delta": 0.61834159, "-2delta": 0.76163487, "-30delta": 0.6174381, "-35delta": 0.61980295, "-3delta": 0.73637992, "-40delta": 0.62434404, "-45delta": 0.63036181, "-4delta": 0.71814221, "-50delta": 0.63744126, "-5delta": 0.70386707, "10delta": 0.78286622, "15delta": 0.75586446, "1delta": 0.91060156, "20delta": 0.73536087, "25delta": 0.71851303, "27d77delta": NaN, "2delta": 0.87549501, "30delta": 0.70402493, "35delta": 0.6911965, "3delta": 0.85383639, "40delta": 0.67961108, "45delta": 0.66900843, "46delta": NaN, "4delta": 0.83787465, "50delta": 0.65922728, "5delta": 0.82510883, "atm": 0.62723605, "expiry": 1.0, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "BTC-28MAR25", "tenor_days": 365, "timestamp": 1716451200000000000, "upper_bordering_expiry": "Extra"}, {"-10delta": 0.67638076, "-15delta": 0.64719162, "-1delta": 0.84160127, "-20delta": 0.6296681, "-25delta": 0.62052183, "-2delta": 0.79443966, "-30delta": 0.61785845, "-35delta": 0.61986755, "-3delta": 0.76572091, "-40delta": 0.62495953, "-45delta": 0.63202047, "-4delta": 0.74483585, "-50delta": 0.64038242, "-5delta": 0.72836623, "10delta": 0.8313191, "15delta": 0.79829594, "1delta": 0.98811949, "20delta": 0.77324511, "25delta": 0.75266705, "27d77delta": NaN, "2delta": 0.94494444, "30delta": 0.73496306, "35delta": 0.71926512, "3delta": 0.91833359, "40delta": 0.7050505, "45delta": 0.69198427, "46delta": NaN, "4delta": 0.89873725, "50delta": 0.67984637, "5delta": 0.8830748, "atm": 0.62723975, "expiry": 1.49863014, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "BTC-28MAR25", "tenor_days": 547, "timestamp": 1716451200000000000, "upper_bordering_expiry": "Extra"}, {"-10delta": 0.68651224, "-15delta": 0.65457066, "-1delta": 0.85931313, "-20delta": 0.63430501, "-25delta": 0.62274689, "-2delta": 0.81042483, "-30delta": 0.618557, "-35delta": 0.62007894, "-3delta": 0.78056683, "-40delta": 0.62540046, "-45delta": 0.63303614, "-4delta": 0.75878628, "-50delta": 0.64209145, "-5delta": 0.74155268, "10delta": 0.86927299, "15delta": 0.8324734, "1delta": 1.04456592, "20delta": 0.80459311, "25delta": 0.78171136, "27d77delta": NaN, "2delta": 0.99623093, "30delta": 0.76203523, "35delta": 0.74458853, "3delta": 0.96646006, "40delta": 0.72877945, "45delta": 0.71422318, "46delta": NaN, "4delta": 0.94454919, "50delta": 0.70065849, "5delta": 0.92704566, "atm": 0.62724429, "expiry": 2.0, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "BTC-28MAR25", "tenor_days": 730, "timestamp": 1716451200000000000, "upper_bordering_expiry": "Extra"}]