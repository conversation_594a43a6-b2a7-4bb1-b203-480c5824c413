[{"-10delta": 0.62244986, "-15delta": 0.58627961, "-1delta": 0.94324077, "-20delta": 0.57009811, "-25delta": 0.56333976, "-2delta": 0.8438472, "-30delta": 0.561651, "-35delta": 0.56301268, "-3delta": 0.78428652, "-40delta": 0.56640885, "-45delta": 0.57130588, "-4delta": 0.74220764, "-50delta": 0.57742866, "-5delta": 0.71026918, "10delta": 0.68575034, "15delta": 0.66033374, "1delta": 0.81060891, "20delta": 0.6414374, "25delta": 0.62625609, "27d77delta": 0.61895245, "2delta": 0.77577903, "30delta": 0.6135265, "35delta": 0.60258239, "3delta": 0.75444509, "40delta": 0.59304632, "45delta": 0.58470742, "46delta": 0.58317314, "4delta": 0.73881494, "50delta": 0.5774704, "5delta": 0.72637969, "atm": 0.57665178, "expiry": 0.00273973, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "BTC-24MAY24", "tenor_days": 1, "timestamp": 1716451200000000000, "upper_bordering_expiry": "BTC-24MAY24"}, {"-10delta": 0.5586751, "-15delta": 0.52752837, "-1delta": 0.78272241, "-20delta": 0.5121737, "-25delta": 0.50543935, "-2delta": 0.71593326, "-30delta": 0.50371578, "-35delta": 0.50506619, "-3delta": 0.67566531, "-40delta": 0.50843248, "-45delta": 0.51323415, "-4delta": 0.64681641, "-50delta": 0.51916155, "-5delta": 0.62448761, "10delta": 0.61765269, "15delta": 0.5951929, "1delta": 0.72570295, "20delta": 0.57831698, "25delta": 0.56461708, "27d77delta": NaN, "2delta": 0.69582111, "30delta": 0.5530076, "35delta": 0.54291659, "3delta": 0.67743926, "40delta": 0.53402238, "45delta": 0.52615018, "46delta": 0.52469061, "4delta": 0.66392529, "50delta": 0.51923084, "5delta": 0.65314096, "atm": 0.51822208, "expiry": 0.00547945, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "SYN.BTC-25MAY24", "tenor_days": 2, "timestamp": 1716451200000000000, "upper_bordering_expiry": "SYN.BTC-25MAY24"}, {"-10delta": 0.55389387, "-15delta": 0.52912396, "-1delta": 0.74627852, "-20delta": 0.51559783, "-25delta": 0.50801437, "-2delta": 0.68541215, "-30delta": 0.50406337, "-35delta": 0.5025702, "-3delta": 0.64993925, "-40delta": 0.50288459, "-45delta": 0.50463331, "-4delta": 0.62524527, "-50delta": 0.50760748, "-5delta": 0.60659614, "10delta": 0.58862845, "15delta": 0.5676003, "1delta": 0.69795464, "20delta": 0.55246153, "25delta": 0.54071056, "27d77delta": NaN, "2delta": 0.66679348, "30delta": 0.53123055, "35delta": 0.52343971, "3delta": 0.64790505, "40delta": 0.51701461, "45delta": 0.51178004, "46delta": NaN, "4delta": 0.63418552, "50delta": 0.50766184, "5delta": 0.62335448, "atm": 0.5070012, "expiry": 0.00821918, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "SYN.BTC-25MAY24", "tenor_days": 3, "timestamp": 1716451200000000000, "upper_bordering_expiry": "BTC-31MAY24"}, {"-10delta": 0.54963896, "-15delta": 0.52755683, "-1delta": 0.73242705, "-20delta": 0.51511361, "-25delta": 0.50766468, "-2delta": 0.67171483, "-30delta": 0.50327179, "-35delta": 0.50097621, "-3delta": 0.63748977, "-40delta": 0.5002582, "-45delta": 0.50082675, "-4delta": 0.6142475, "-50delta": 0.50252718, "-5delta": 0.59702163, "10delta": 0.57426206, "15delta": 0.55374674, "1delta": 0.69069369, "20delta": 0.53956767, "25delta": 0.52898353, "27d77delta": NaN, "2delta": 0.65628323, "30delta": 0.5207781, "35delta": 0.51431797, "3delta": 0.63581744, "40delta": 0.50924703, "45delta": 0.50536459, "46delta": NaN, "4delta": 0.62117544, "50delta": 0.50257177, "5delta": 0.60976677, "atm": 0.50210068, "expiry": 0.0109589, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "SYN.BTC-25MAY24", "tenor_days": 4, "timestamp": 1716451200000000000, "upper_bordering_expiry": "BTC-31MAY24"}, {"-10delta": 0.5467886, "-15delta": 0.52576164, "-1delta": 0.73067173, "-20delta": 0.5137692, "-25delta": 0.50639832, "-2delta": 0.66708264, "-30delta": 0.50184511, "-35delta": 0.49922878, "-3delta": 0.63232151, "-40delta": 0.49807696, "-45delta": 0.49812914, "-4delta": 0.60920315, "-50delta": 0.49925147, "-5delta": 0.59231974, "10delta": 0.56693411, "15delta": 0.54629458, "1delta": 0.69409949, "20delta": 0.53247789, "25delta": 0.52245111, "27d77delta": NaN, "2delta": 0.65513411, "30delta": 0.51488555, "35delta": 0.50909491, "3delta": 0.6324283, "40delta": 0.50469418, "45delta": 0.50146413, "46delta": NaN, "4delta": 0.61643762, "50delta": 0.4992916, "5delta": 0.6041417, "atm": 0.49891477, "expiry": 0.01369863, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "SYN.BTC-25MAY24", "tenor_days": 5, "timestamp": 1716451200000000000, "upper_bordering_expiry": "BTC-31MAY24"}, {"-10delta": 0.54507507, "-15delta": 0.52429103, "-1delta": 0.73682088, "-20delta": 0.51239558, "-25delta": 0.50499927, "-2delta": 0.66805585, "-30delta": 0.50033311, "-35delta": 0.4975409, "-3delta": 0.63156018, "-40delta": 0.49616667, "-45delta": 0.49596129, "-4delta": 0.60774551, "-50delta": 0.49679949, "-5delta": 0.5905775, "10delta": 0.56352685, "15delta": 0.5422812, "1delta": 0.70458142, "20delta": 0.52840235, "25delta": 0.51853093, "27d77delta": NaN, "2delta": 0.65983714, "30delta": 0.51121769, "35delta": 0.50572229, "3delta": 0.63430682, "40delta": 0.5016321, "45delta": 0.49871206, "46delta": NaN, "4delta": 0.61660693, "50delta": 0.49683863, "5delta": 0.60316923, "atm": 0.49650661, "expiry": 0.01643836, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "SYN.BTC-25MAY24", "tenor_days": 6, "timestamp": 1716451200000000000, "upper_bordering_expiry": "BTC-31MAY24"}, {"-10delta": 0.54420555, "-15delta": 0.52321228, "-1delta": 0.74917128, "-20delta": 0.51121029, "-25delta": 0.50371399, "-2delta": 0.6729756, "-30delta": 0.49893755, "-35delta": 0.49602307, "-3delta": 0.63375555, "-40delta": 0.49451639, "-45delta": 0.49417111, "-4delta": 0.60864523, "-50delta": 0.49486527, "-5delta": 0.59077165, "10delta": 0.56251024, "15delta": 0.54030016, "1delta": 0.72069635, "20delta": 0.52607231, "25delta": 0.51610339, "27d77delta": NaN, "2delta": 0.66878609, "30delta": 0.50881251, "35delta": 0.50340112, "3delta": 0.63981115, "40delta": 0.49942733, "45delta": 0.49663996, "46delta": NaN, "4delta": 0.62004026, "50delta": 0.4949058, "5delta": 0.60521754, "atm": 0.4945909, "expiry": 0.01917808, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "SYN.BTC-25MAY24", "tenor_days": 7, "timestamp": 1716451200000000000, "upper_bordering_expiry": "BTC-31MAY24"}, {"-10delta": 0.54399276, "-15delta": 0.52248266, "-1delta": 0.76726015, "-20delta": 0.510232, "-25delta": 0.50257555, "-2delta": 0.68114723, "-30delta": 0.49767704, "-35delta": 0.49466058, "-3delta": 0.63825446, "-40delta": 0.493065, "-45delta": 0.49264167, "-4delta": 0.61133148, "-50delta": 0.49326912, "-5delta": 0.59241523, "10delta": 0.56312246, "15delta": 0.53965446, "1delta": 0.74198035, "20delta": 0.52486316, "25delta": 0.51461982, "27d77delta": NaN, "2delta": 0.68127625, "30delta": 0.50719842, "35delta": 0.50173638, "3delta": 0.64816754, "40delta": 0.4977597, "45delta": 0.49499966, "46delta": 0.49457932, "4delta": 0.62594137, "50delta": 0.49331294, "5delta": 0.60948526, "atm": 0.49299704, "expiry": 0.02191781, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "BTC-31MAY24", "tenor_days": 8, "timestamp": 1716451200000000000, "upper_bordering_expiry": "BTC-31MAY24"}, {"-10delta": 0.54366746, "-15delta": 0.52607026, "-1delta": 0.80530675, "-20delta": 0.51640942, "-25delta": 0.5105843, "-2delta": 0.67850172, "-30delta": 0.5070341, "-35delta": 0.50502919, "-3delta": 0.63063604, "-40delta": 0.50419152, "-45delta": 0.50432092, "-4delta": 0.6037628, "-50delta": 0.50532305, "-5delta": 0.58599101, "10delta": 0.56940992, "15delta": 0.54851868, "1delta": 0.7366501, "20delta": 0.53526531, "25delta": 0.52598281, "27d77delta": NaN, "2delta": 0.6771698, "30delta": 0.51915158, "35delta": 0.51401432, "3delta": 0.64614716, "40delta": 0.51015406, "45delta": 0.50733237, "46delta": NaN, "4delta": 0.62576484, "50delta": 0.50541897, "5delta": 0.61085827, "atm": 0.50486143, "expiry": 0.03835616, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "BTC-31MAY24", "tenor_days": 14, "timestamp": 1716451200000000000, "upper_bordering_expiry": "SYN.BTC-7JUN24"}, {"-10delta": 0.54262333, "-15delta": 0.52523406, "-1delta": 0.82982432, "-20delta": 0.51582683, "-25delta": 0.51024704, "-2delta": 0.68281813, "-30delta": 0.50692712, "-35delta": 0.50513866, "-3delta": 0.63157729, "-40delta": 0.50450611, "-45delta": 0.50483174, "-4delta": 0.60357063, "-50delta": 0.50602328, "-5delta": 0.585313, "10delta": 0.5714067, "15delta": 0.55045804, "1delta": 0.73458341, "20delta": 0.53706324, "25delta": 0.52761366, "27d77delta": NaN, "2delta": 0.67731762, "30delta": 0.52060634, "35delta": 0.51528928, "3delta": 0.64714092, "40delta": 0.51124694, "45delta": 0.5082407, "46delta": 0.50775077, "4delta": 0.6271855, "50delta": 0.50613929, "5delta": 0.61252178, "atm": 0.50547784, "expiry": 0.04109589, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "SYN.BTC-7JUN24", "tenor_days": 15, "timestamp": 1716451200000000000, "upper_bordering_expiry": "SYN.BTC-7JUN24"}, {"-10delta": 0.56821729, "-15delta": 0.55227462, "-1delta": 0.77681517, "-20delta": 0.54383032, "-25delta": 0.53904859, "-2delta": 0.68637169, "-30delta": 0.53645675, "-35delta": 0.53536583, "-3delta": 0.64646333, "-40delta": 0.5354209, "-45delta": 0.53643708, "-4delta": 0.62287351, "-50delta": 0.5383314, "-5delta": 0.60690328, "10delta": 0.61147586, "15delta": 0.58951999, "1delta": 0.76104898, "20delta": 0.57502096, "25delta": 0.56451625, "27d77delta": NaN, "2delta": 0.7122964, "30delta": 0.55652413, "35delta": 0.55028784, "3delta": 0.68510669, "40delta": 0.54538149, "45delta": 0.54155535, "46delta": NaN, "4delta": 0.66649236, "50delta": 0.53866728, "5delta": 0.65247112, "atm": 0.5371872, "expiry": 0.08219178, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "SYN.BTC-7JUN24", "tenor_days": 30, "timestamp": 1716451200000000000, "upper_bordering_expiry": "BTC-28JUN24"}, {"-10delta": 0.57030561, "-15delta": 0.55539231, "-1delta": 0.74576919, "-20delta": 0.547505, "-25delta": 0.54310632, "-2delta": 0.67456089, "-30delta": 0.54081719, "-35delta": 0.53998724, "-3delta": 0.6408097, "-40delta": 0.54028043, "-45delta": 0.5415223, "-4delta": 0.62013486, "-50delta": 0.54363615, "-5delta": 0.60583864, "10delta": 0.6188374, "15delta": 0.59672219, "1delta": 0.76367298, "20delta": 0.58196971, "25delta": 0.57119433, "27d77delta": NaN, "2delta": 0.71746863, "30delta": 0.56293441, "35delta": 0.55643828, "3delta": 0.69130517, "40delta": 0.55128016, "45delta": 0.54720845, "46delta": 0.54651025, "4delta": 0.67321579, "50delta": 0.544078, "5delta": 0.65948999, "atm": 0.54226528, "expiry": 0.09863014, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "BTC-28JUN24", "tenor_days": 36, "timestamp": 1716451200000000000, "upper_bordering_expiry": "BTC-28JUN24"}, {"-10delta": 0.59935367, "-15delta": 0.581573, "-1delta": 0.78340286, "-20delta": 0.57298839, "-25delta": 0.56891014, "-2delta": 0.71883442, "-30delta": 0.56746331, "-35delta": 0.56775396, "-3delta": 0.68334469, "-40delta": 0.56931234, "-45delta": 0.57188216, "-4delta": 0.65987039, "-50delta": 0.57532958, "-5delta": 0.64292569, "10delta": 0.65218059, "15delta": 0.6333063, "1delta": 0.74943581, "20delta": 0.61959335, "25delta": 0.60882106, "27d77delta": NaN, "2delta": 0.72174312, "30delta": 0.59999274, "35delta": 0.59258248, "3delta": 0.70496249, "40delta": 0.58628926, "45delta": 0.58093823, "46delta": NaN, "4delta": 0.69277036, "50delta": 0.57643698, "5delta": 0.68313942, "atm": 0.57251184, "expiry": 0.16438356, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "BTC-28JUN24", "tenor_days": 60, "timestamp": 1716451200000000000, "upper_bordering_expiry": "SYN.BTC-26JUL24"}, {"-10delta": 0.60222692, "-15delta": 0.58380243, "-1delta": 0.78503934, "-20delta": 0.57503469, "-25delta": 0.57100842, "-2delta": 0.72272565, "-30delta": 0.56972711, "-35delta": 0.57023246, "-3delta": 0.6877577, "-40delta": 0.57201898, "-45delta": 0.57480915, "-4delta": 0.66426006, "-50delta": 0.57845517, "-5delta": 0.6470952, "10delta": 0.65435091, "15delta": 0.63623044, "1delta": 0.74553215, "20delta": 0.6229221, "25delta": 0.61236086, "27d77delta": NaN, "2delta": 0.71983672, "30delta": 0.60361825, "35delta": 0.59620424, "3delta": 0.70418101, "40delta": 0.58983911, "45delta": 0.58436201, "46delta": 0.58336477, "4delta": 0.69275776, "50delta": 0.57969078, "5delta": 0.68370136, "atm": 0.57538906, "expiry": 0.17534247, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "SYN.BTC-26JUL24", "tenor_days": 64, "timestamp": 1716451200000000000, "upper_bordering_expiry": "SYN.BTC-26JUL24"}, {"-10delta": 0.61716721, "-15delta": 0.5989091, "-1delta": 0.77899469, "-20delta": 0.58962619, "-25delta": 0.58498512, "-2delta": 0.72514515, "-30delta": 0.58315598, "-35delta": 0.58322901, "-3delta": 0.69470009, "-40delta": 0.5847098, "-45delta": 0.58732031, "-4delta": 0.67400601, "-50delta": 0.59091, "-5delta": 0.65868881, "10delta": 0.67514688, "15delta": 0.65462513, "1delta": 0.78019796, "20delta": 0.6396887, "25delta": 0.62794323, "27d77delta": NaN, "2delta": 0.75039385, "30delta": 0.61831359, "35delta": 0.61023265, "3delta": 0.73229479, "40delta": 0.60337626, "45delta": 0.59755659, "46delta": NaN, "4delta": 0.71912469, "50delta": 0.59267474, "5delta": 0.70870866, "atm": 0.5873968, "expiry": 0.24657534, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "SYN.BTC-26JUL24", "tenor_days": 90, "timestamp": 1716451200000000000, "upper_bordering_expiry": "BTC-27SEP24"}, {"-10delta": 0.62426692, "-15delta": 0.6067619, "-1delta": 0.77378561, "-20delta": 0.59764186, "-25delta": 0.59296835, "-2delta": 0.72427637, "-30delta": 0.59106258, "-35delta": 0.59108167, "-3delta": 0.69629877, "-40delta": 0.59256441, "-45delta": 0.59525162, "-4delta": 0.6772418, "-50delta": 0.59900579, "-5delta": 0.66308849, "10delta": 0.69480545, "15delta": 0.67112494, "1delta": 0.81823091, "20delta": 0.65404033, "25delta": 0.64071637, "27d77delta": NaN, "2delta": 0.78296215, "30delta": 0.62987977, "35delta": 0.62085706, "3delta": 0.76161985, "40delta": 0.61326022, "45delta": 0.60685961, "46delta": NaN, "4delta": 0.74613522, "50delta": 0.60152665, "5delta": 0.73392062, "atm": 0.59488854, "expiry": 0.32876712, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "SYN.BTC-26JUL24", "tenor_days": 120, "timestamp": 1716451200000000000, "upper_bordering_expiry": "BTC-27SEP24"}, {"-10delta": 0.62524283, "-15delta": 0.60789943, "-1delta": 0.77294748, "-20delta": 0.59884439, "-25delta": 0.59419975, "-2delta": 0.72403162, "-30delta": 0.59231204, "-35delta": 0.59235085, "-3delta": 0.69640557, "-40delta": 0.59386197, "-45delta": 0.59659079, "-4delta": 0.67759009, "-50delta": 0.60040334, "-5delta": 0.66361424, "10delta": 0.69912642, "15delta": 0.67466109, "1delta": 0.82713779, "20delta": 0.65704289, "25delta": 0.64332593, "27d77delta": NaN, "2delta": 0.79050306, "30delta": 0.63218702, "35delta": 0.62292582, "3delta": 0.76835043, "40delta": 0.615138, "45delta": 0.60858314, "46delta": 0.60740666, "4delta": 0.75228792, "50delta": 0.60312486, "5delta": 0.73962459, "atm": 0.5961322, "expiry": 0.34794521, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "BTC-27SEP24", "tenor_days": 127, "timestamp": 1716451200000000000, "upper_bordering_expiry": "BTC-27SEP24"}, {"-10delta": 0.64316636, "-15delta": 0.62345973, "-1delta": 0.79907734, "-20delta": 0.61373083, "-25delta": 0.60947046, "-2delta": 0.75014766, "-30delta": 0.60859378, "-35delta": 0.60999121, "-3delta": 0.72151909, "-40delta": 0.6130304, "-45delta": 0.61734227, "-4delta": 0.70148918, "-50delta": 0.62271719, "-5delta": 0.6862925, "10delta": 0.73086439, "15delta": 0.70690042, "1delta": 0.84786014, "20delta": 0.68903304, "25delta": 0.67463821, "27d77delta": NaN, "2delta": 0.81531105, "30delta": 0.66253118, "35delta": 0.65208327, "3delta": 0.79534729, "40delta": 0.64293263, "45delta": 0.63486763, "46delta": NaN, "4delta": 0.78070501, "50delta": 0.62777516, "5delta": 0.76904461, "atm": 0.61558921, "expiry": 0.49315068, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "BTC-27SEP24", "tenor_days": 180, "timestamp": 1716451200000000000, "upper_bordering_expiry": "BTC-27DEC24"}, {"-10delta": 0.65166458, "-15delta": 0.62987374, "-1delta": 0.81181346, "-20delta": 0.61932255, "-25delta": 0.615179, "-2delta": 0.76323435, "-30delta": 0.61496793, "-35delta": 0.6172851, "-3delta": 0.73430695, "-40delta": 0.62131532, "-45delta": 0.62658734, "-4delta": 0.7137501, "-50delta": 0.63283785, "-5delta": 0.69793067, "10delta": 0.74494737, "15delta": 0.72147671, "1delta": 0.85672646, "20delta": 0.70373986, "25delta": 0.68924959, "27d77delta": NaN, "2delta": 0.8259367, "30delta": 0.67687782, "35delta": 0.66602207, "3delta": 0.8069599, "40delta": 0.65633236, "45delta": 0.64760146, "46delta": 0.64595905, "4delta": 0.7929864, "50delta": 0.63971691, "5delta": 0.78181962, "atm": 0.62375441, "expiry": 0.59726027, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "BTC-27DEC24", "tenor_days": 218, "timestamp": 1716451200000000000, "upper_bordering_expiry": "BTC-27DEC24"}, {"-10delta": 0.65154421, "-15delta": 0.63084573, "-1delta": 0.79400833, "-20delta": 0.62059515, "-25delta": 0.61669427, "-2delta": 0.75157453, "-30delta": 0.61685691, "-35delta": 0.61966209, "-3delta": 0.72612651, "-40delta": 0.62423705, "-45delta": 0.63006513, "-4delta": 0.70790767, "-50delta": 0.63685603, "-5delta": 0.69377879, "10delta": 0.7556687, "15delta": 0.73146944, "1delta": 0.87008613, "20delta": 0.71310571, "25delta": 0.69803627, "27d77delta": NaN, "2delta": 0.83865877, "30delta": 0.68510592, "35delta": 0.67369527, "3delta": 0.81926264, "40delta": 0.66344197, "45delta": 0.65412849, "46delta": NaN, "4delta": 0.80496449, "50delta": 0.64563318, "5delta": 0.79352693, "atm": 0.62603212, "expiry": 0.73972603, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "BTC-27DEC24", "tenor_days": 270, "timestamp": 1716451200000000000, "upper_bordering_expiry": "BTC-28MAR25"}, {"-10delta": 0.65096491, "-15delta": 0.6310607, "-1delta": 0.78324974, "-20delta": 0.62107535, "-25delta": 0.61737403, "-2delta": 0.74422422, "-30delta": 0.61781643, "-35delta": 0.62097716, "-3delta": 0.72073256, "-40delta": 0.62594327, "-45delta": 0.63216623, "-4delta": 0.70385014, "-50delta": 0.63933791, "-5delta": 0.69070597, "10delta": 0.76331996, "15delta": 0.73850731, "1delta": 0.88024445, "20delta": 0.71963944, "25delta": 0.70412103, "27d77delta": NaN, "2delta": 0.84816893, "30delta": 0.69077059, "35delta": 0.67895285, "3delta": 0.82836108, "40delta": 0.66829408, "45delta": 0.65856749, "46delta": 0.65672117, "4delta": 0.81375227, "50delta": 0.64964295, "5delta": 0.80206102, "atm": 0.6272339, "expiry": 0.84657534, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "BTC-28MAR25", "tenor_days": 309, "timestamp": 1716451200000000000, "upper_bordering_expiry": "BTC-28MAR25"}, {"-10delta": 0.65686212, "-15delta": 0.63422539, "-1delta": 0.80044647, "-20delta": 0.62240776, "-25delta": 0.61772068, "-2delta": 0.75855222, "-30delta": 0.61787693, "-35delta": 0.62122618, "-3delta": 0.73322717, "-40delta": 0.62666255, "-45delta": 0.63351384, "-4delta": 0.71495109, "-50delta": 0.64140358, "-5delta": 0.70065982, "10delta": 0.77964095, "15delta": 0.75238913, "1delta": 0.90814103, "20delta": 0.73166327, "25delta": 0.7146088, "27d77delta": NaN, "2delta": 0.87287293, "30delta": 0.69992515, "35delta": 0.68691165, "3delta": 0.85109979, "40delta": 0.67515434, "45delta": 0.66439982, "46delta": NaN, "4delta": 0.83504486, "50delta": 0.65449976, "5delta": 0.82219837, "atm": 0.62723605, "expiry": 1.0, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "BTC-28MAR25", "tenor_days": 365, "timestamp": 1716451200000000000, "upper_bordering_expiry": "Extra"}, {"-10delta": 0.67081193, "-15delta": 0.64222768, "-1delta": 0.83661641, "-20delta": 0.62601832, "-25delta": 0.6188151, "-2delta": 0.78919633, "-30delta": 0.61832124, "-35delta": 0.62234163, "-3delta": 0.7603209, "-40delta": 0.6291741, "-45delta": 0.63776583, "-4delta": 0.7393301, "-50delta": 0.64754869, "-5delta": 0.72278908, "10delta": 0.8253919, "15delta": 0.79191486, "1delta": 0.98357774, "20delta": 0.76645721, "25delta": 0.74549595, "27d77delta": NaN, "2delta": 0.94010878, "30delta": 0.72742208, "35delta": 0.71136351, "3delta": 0.91329023, "40delta": 0.69679805, "45delta": 0.68339643, "46delta": NaN, "4delta": 0.89352519, "50delta": 0.67095186, "5delta": 0.87771684, "atm": 0.62723975, "expiry": 1.49863014, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "BTC-28MAR25", "tenor_days": 547, "timestamp": 1716451200000000000, "upper_bordering_expiry": "Extra"}, {"-10delta": 0.6785085, "-15delta": 0.64712551, "-1delta": 0.85241867, "-20delta": 0.62846371, "-25delta": 0.61971562, "-2delta": 0.80315081, "-30delta": 0.61897729, "-35delta": 0.6237224, "-3delta": 0.77305229, "-40delta": 0.63171313, "-45delta": 0.64158052, "-4delta": 0.75109915, "-50delta": 0.6526297, "-5delta": 0.73373747, "10delta": 0.86043489, "15delta": 0.82296822, "1delta": 1.03777115, "20delta": 0.79448848, "25delta": 0.77103893, "27d77delta": NaN, "2delta": 0.98900057, "30delta": 0.75080953, "35delta": 0.7328152, "3delta": 0.95892275, "40delta": 0.71646049, "45delta": 0.70136183, "46delta": NaN, "4delta": 0.93676303, "50delta": 0.68726593, "5delta": 0.91904455, "atm": 0.62724429, "expiry": 2.0, "isodate": "2024-05-23T08:00:00.000000Z", "lower_bordering_expiry": "BTC-28MAR25", "tenor_days": 730, "timestamp": 1716451200000000000, "upper_bordering_expiry": "Extra"}]