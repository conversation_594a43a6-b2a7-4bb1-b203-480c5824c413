import numpy as np
from utils.extrapolate import get_strikes_and_vols_to_extrapolate

from .test_types import StrikesVolsExtrapolateTestType


class TestExtrapolate:
    def test_get_strikes_and_vols_to_extrapolate(self) -> None:
        test_input: StrikesVolsExtrapolateTestType = {
            "expiry": 0.002739726027,
            "forward": 3903.5897035522976,
            "spot": 3899.4060215,
            # fmt: off
            "strikes_to_vol": {390.35897035522976: 6.512357810385432, 780.7179407104595: 4.978458628662255, 1171.0769110656893: 4.029695585194383, 1561.435881420919: 3.3225852857934073, 1951.7948517761488: 2.7467980911827987, 2342.1538221313785: 2.251705081319038, 2732.5127924866083: 1.8090785667063198, 3122.871762841838: 1.401514542536706, 3513.230733197068: 1.024066745738263, 3552.266630232591: 0.989076553128772, 3591.302527268114: 0.9550479425156903, 3630.338424303637: 0.9222386377482955, 3669.3743213391595: 0.890991161545549, 3708.4102183746827: 0.8617544058925675, 3747.4461154102055: 0.8351021601527912, 3786.4820124457287: 0.811737212040798, 3825.5179094812515: 0.792461505831596, 3864.553806516775: 0.7780894854184881, 3903.5897035522976: 0.769297513865072, 3942.6256005878204: 0.7664466906689601, 3981.6614976233436: 0.7694635557400629, 4020.6973946588664: 0.7778529501452941, 4059.7332916943897: 0.7908357321752222, 4098.7691887299125: 0.8075295616311768, 4137.805085765435: 0.8270924028534654, 4176.840982800959: 0.8487993320911692, 4215.876879836482: 0.8720644807256571, 4254.9127768720045: 0.8964319769629598, 4293.948673907527: 0.921554713693838, 4684.307644262757: 1.1794534213241559, 5074.666614617987: 1.418091720932089, 5465.025584973217: 1.632694958983252, 5855.384555328446: 1.826819000262396, 6245.743525683676: 2.003942555076043, 6636.102496038906: 2.1668279826234382, 7026.461466394136: 2.317627633050286, 7416.820436749365: 2.458036435993268, 7807.179407104595: 2.5894104573481975, 8197.538377459825: 2.712851780874204, 8587.897347815055: 2.8292688811526756, 8978.256318170284: 2.9394200971453017, 9368.615288525514: 3.043945479748767, 9758.974258880744: 3.1433905387439927, 10149.333229235974: 3.238224248253361, 10539.692199591203: 3.3288529099605326, 10930.051169946433: 3.4156309765793553, 11320.410140301663: 3.498869609019625, 11710.769110656893: 3.578843519154015},
            # fmt: on
            "r_d": 0,
        }

        # fmt: off
        expected_strikes = [3513.230733197068, 3552.266630232591, 3591.302527268114, 3630.338424303637, 3669.3743213391595, 3708.4102183746827, 3747.4461154102055, 3786.4820124457287, 3825.5179094812515, 3864.553806516775, 3903.5897035522976, 3942.6256005878204, 3981.6614976233436, 4020.6973946588664, 4059.7332916943897, 4098.7691887299125, 4137.805085765435, 4176.840982800959, 4215.876879836482, 4254.9127768720045, 4293.948673907527]
        expected_vols = [test_input['strikes_to_vol'][strike] for strike in expected_strikes]
        expected_deltas = [-0.02318816157651545, -0.03236570148787533, -0.045278411671777305, -0.06338618287105716, -0.08859101190604755, -0.12321382984863004, -0.16977945439184347, -0.2304649506383831, -0.30612683746353275, -0.39510551497211693, 0.5085766045595698, 0.4102745294223791, 0.31896919158022086, 0.2404633587406693, 0.17720639552444042, 0.12870933714078986, 0.09280032783180046, 0.06679301972379709, 0.0481857956952082, 0.03493955363746076, 0.02550949582408423]
        # fmt: on

        strikes, vols, deltas = get_strikes_and_vols_to_extrapolate(
            expiry=test_input["expiry"],
            forward=test_input["forward"],
            spot=test_input["spot"],
            strikes_to_vol=test_input["strikes_to_vol"],
            r_d=test_input["r_d"],
        )
        assert np.all(np.isclose(expected_strikes, strikes, rtol=1e-6))
        assert np.all(np.isclose(expected_vols, vols, rtol=1e-6))
        assert np.all(np.isclose(expected_deltas, deltas, rtol=1e-6))
