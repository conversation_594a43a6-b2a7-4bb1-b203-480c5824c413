import json
import pathlib
import platform
import sys
from typing import Any, cast
from unittest.mock import patch

import pandas as pd
import pytest
import utils_calc
from lambda_types import (
    ExpiryArbFields,
    ListedTenorParamsSVI,
    TenorExpiryArbFields,
)
from svi_params_surface_calc import svi_tenor_params

from .helpers import (
    compare_dataframes_row_by_row,
    set_snapshot_path_to_parent,
)
from .test_types import ParamsCalcTestType


def is_x86_architecture() -> bool:
    """Check if the current architecture is x86-based."""
    arch = platform.machine().lower()
    return "x86" in arch or "amd64" in arch or "i386" in arch


def is_snapshot_update() -> bool:
    """Check if the test is being run with --snapshot-update flag."""
    return "--snapshot-update" in sys.argv


@pytest.fixture
def snapshot(snapshot: Any) -> Any:
    return set_snapshot_path_to_parent(snapshot)


def load_test_data(test_data_path: pathlib.Path) -> dict[str, Any]:
    with open(test_data_path / "expiry_iv_info.json") as f:
        expiry_iv_info_serialized = json.load(f)
    with open(test_data_path / "snap.json") as f:
        snap = json.load(f)

    expiry_iv_info = TenorExpiryArbFields()
    for tenor in expiry_iv_info_serialized.keys():
        expiry_iv_info[float(tenor)] = ExpiryArbFields(
            strikes=set(expiry_iv_info_serialized[tenor]["strikes"]),
            moneyness=set(expiry_iv_info_serialized[tenor]["moneyness"]),
            deltas=set(expiry_iv_info_serialized[tenor]["deltas"]),
            listed_expiry=expiry_iv_info_serialized[tenor]["listed_expiry"],
            arbitrary_expiry=expiry_iv_info_serialized[tenor][
                "arbitrary_expiry"
            ],
        )

    return {
        "snap": snap,
        "expiry_iv_info": expiry_iv_info,
    }


@pytest.fixture
def sample_data_values() -> list[dict[str, Any]]:
    test_data_root = (
        pathlib.Path(__file__).parent.resolve()
        / "input_data/params_surface_calc"
    )

    test_cases = []

    for test_data_dir in test_data_root.iterdir():
        if test_data_dir.is_dir():
            test_case = load_test_data(test_data_dir)
            test_case["name"] = test_data_dir.name
            test_cases.append(test_case)

    return test_cases


def compare_with_fallback(
    actual_df: pd.DataFrame,
    snapshot_dir: pathlib.Path,
    filename: str,
    **kwargs: Any,
) -> None:
    """
    Compare actual dataframe with expected dataframes from primary and fallback files.
    Test passes if either comparison succeeds.

    This is necessary due to platform-specific differences which can be quite large
    for certain values even if not impactful for the overall result/purpose of a test.
    As such these cannot be accounted for using rtol alone without either completely
    diluting value of tests introducing targeted tolerances for specific values
    which would be very brittle / confusing / time consuming.
    """
    primary_path = snapshot_dir / filename
    fallback_path = (
        snapshot_dir / f"{primary_path.stem}_FALLBACK{primary_path.suffix}"
    )

    primary_exists = primary_path.exists()
    fallback_exists = fallback_path.exists()

    if not primary_exists and not fallback_exists:
        raise FileNotFoundError(
            f"Neither {primary_path} nor {fallback_path} exists"
        )

    first_error = None

    # Try comparing with primary file first
    if primary_exists:
        try:
            expected_df = pd.read_json(primary_path, **kwargs)
            compare_dataframes_row_by_row(expected_df, actual_df, rtol=1e-2)
            return  # Success with primary file
        except (AssertionError, ValueError) as e:
            first_error = e

    # Try comparing with fallback file if primary failed or doesn't exist
    if fallback_exists:
        try:
            expected_df = pd.read_json(fallback_path, **kwargs)
            compare_dataframes_row_by_row(expected_df, actual_df, rtol=1e-2)
            return  # Success with fallback file
        except (AssertionError, ValueError):
            # If both failed, raise the first error
            if first_error:
                raise first_error from first_error
            raise  # Raise current error if primary file didn't exist

    # If we only tried the primary file and it failed
    if first_error:
        raise first_error


def test_params_surface_calc(
    snapshot: Any, sample_data_values: list[ParamsCalcTestType]
) -> None:
    """Test the params surface calculation with architecture-specific snapshot handling."""

    for test_case in sample_data_values:
        if test_case["name"] == "SVI_left_extrap_calib":
            params_result = svi_tenor_params(
                snap=cast(list[ListedTenorParamsSVI], test_case["snap"]),
                exchange="v2composite",
                currency="ETH",
                expiry_iv_info=test_case["expiry_iv_info"],
            )

            expected_base_result = {
                "model": "SVI",
                "exchange": "v2composite",
                "currency": "ETH",
                "timestamp": 1723937680000000000,
            }

        elif test_case["name"] == "SVI":
            params_result = svi_tenor_params(
                snap=cast(list[ListedTenorParamsSVI], test_case["snap"]),
                exchange="v2composite",
                currency="BTC",
                expiry_iv_info=test_case["expiry_iv_info"],
            )

            expected_base_result = {
                "model": "SVI",
                "exchange": "v2composite",
                "currency": "BTC",
                "timestamp": 1716996360000000000,
            }

        for expected_key, expected_result in expected_base_result.items():
            assert params_result[expected_key] == expected_result  # type: ignore

        params_result["dfs"]["params"].drop(columns=["runtime"], inplace=True)
        params_result["dfs"]["params"]["timestamp"] = params_result["dfs"][
            "params"
        ]["timestamp"].astype(int)

        params_result["df_vol_matrix_pre"].index.name = "tenor_days"
        df_vol_matrix_pre_with_index = params_result[
            "df_vol_matrix_pre"
        ].reset_index()

        # If updating snapshots, save them in architecture-specific directory
        if is_snapshot_update():
            snapshot.assert_match(
                json.dumps(
                    params_result["dfs"]["params"].to_dict(orient="records"),
                    indent=2,
                ),
                snapshot.snapshot_dir
                / f"{test_case['name']}_params_result.json",
            )
            snapshot.assert_match(
                json.dumps(
                    df_vol_matrix_pre_with_index.to_dict(orient="records"),
                    indent=2,
                ),
                snapshot.snapshot_dir
                / f"{test_case['name']}_vol_skeleton.json",
            )
            continue

        # Compare with primary or fallback snapshots
        compare_with_fallback(
            params_result["dfs"]["params"],
            snapshot.snapshot_dir,
            f"{test_case['name']}_params_result.json",
            dtype={"timestamp": int, "rd": float},
        )

        compare_with_fallback(
            df_vol_matrix_pre_with_index,
            snapshot.snapshot_dir,
            f"{test_case['name']}_vol_skeleton.json",
            dtype={
                str(delta): float for delta in utils_calc.DEFAULT_SURFACE_DELTAS
            },
        )


def test_params_surface_calc_with_interest_rates(
    snapshot: Any,
    sample_data_values: list[ParamsCalcTestType],
) -> None:
    """
    Test the params surface calculation with interest rates and architecture-specific snapshot handling.

    Since the parameters for the calibration are passed from model_params,
    and the target vols are the values on which the calibration occurs, the only change would be in
    the interest rate assumption, safely passing through the calibration process.
    """
    with patch(
        "svi_params_surface_calc.load_domestic_rates",
        return_value={
            "28": 0.05,
            "56": 0.05,
            "91": 0.05,
            "182": 0.05,
            "365": 0.05,
        },
    ):
        for test_case in sample_data_values:
            if test_case["name"] == "SVI_left_extrap_calib":
                params_result = svi_tenor_params(
                    snap=cast(list[ListedTenorParamsSVI], test_case["snap"]),
                    exchange="v2composite",
                    currency="ETH",
                    expiry_iv_info=test_case["expiry_iv_info"],
                )

                expected_base_result = {
                    "model": "SVI",
                    "exchange": "v2composite",
                    "currency": "ETH",
                    "timestamp": 1723937680000000000,
                }

            elif test_case["name"] == "SVI":
                params_result = svi_tenor_params(
                    snap=cast(list[ListedTenorParamsSVI], test_case["snap"]),
                    exchange="v2composite",
                    currency="BTC",
                    expiry_iv_info=test_case["expiry_iv_info"],
                )

                expected_base_result = {
                    "model": "SVI",
                    "exchange": "v2composite",
                    "currency": "BTC",
                    "timestamp": 1716996360000000000,
                }

            for expected_key, expected_result in expected_base_result.items():
                assert params_result[expected_key] == expected_result  # type: ignore

            params_result["dfs"]["params"].drop(
                columns=["runtime"], inplace=True
            )
            params_result["dfs"]["params"]["timestamp"] = params_result["dfs"][
                "params"
            ]["timestamp"].astype(int)

            if is_snapshot_update():
                snapshot.assert_match(
                    json.dumps(
                        params_result["dfs"]["params"].to_dict(
                            orient="records"
                        ),
                        indent=2,
                    ),
                    snapshot.snapshot_dir
                    / f"{test_case['name']}_params_result_with_rd.json",
                )
                continue

            # Compare with primary or fallback snapshots
            compare_with_fallback(
                params_result["dfs"]["params"],
                snapshot.snapshot_dir,
                f"{test_case['name']}_params_result_with_rd.json",
                dtype={"timestamp": int, "rd": float},
            )
