import json
import pathlib
from typing import Any

import pandas as pd
import pytest
import utils_general
from utils.common import set_qualified_name_timestamp_index
from utils.smoothing import construct_smoothing_config, estimate_new_params

from .helpers import set_snapshot_path_to_parent
from .test_types import EstimateNewParamsTestType


@pytest.fixture
def snapshot(snapshot: Any) -> Any:
    return set_snapshot_path_to_parent(snapshot)


def load_test_data(
    test_data_path: pathlib.Path, data_types: dict[str, Any]
) -> dict[str, Any]:
    return {
        "qn_grouped_params_df": pd.read_json(
            test_data_path / "params.json", dtype=data_types
        ),
        "qn_grouped_moneyness_df": pd.read_json(
            test_data_path / "moneyness.json", dtype=data_types
        ),
        "qn_grouped_smile_df": pd.read_json(
            test_data_path / "smile.json", dtype=data_types
        ),
        "smoothing_config": construct_smoothing_config(
            exchange="bybit", model="SVI", freq="1h"
        ),
    }


@pytest.fixture
def sample_data_values() -> list[dict[str, Any]]:
    test_data_root = (
        pathlib.Path(__file__).parent.resolve()
        / "input_data/estimate_new_params_test_data"
    )
    data_types = {"timestamp": int}

    test_cases = []

    for test_data_dir in test_data_root.iterdir():
        if test_data_dir.is_dir():
            test_case = load_test_data(test_data_dir, data_types)
            test_case["name"] = test_data_dir.name
            test_case["start"] = utils_general.to_iso(
                test_case["qn_grouped_params_df"].iloc[0]["timestamp"]
            )
            test_cases.append(test_case)

    return test_cases


def test_estimate_new_params(
    snapshot: Any, sample_data_values: list[EstimateNewParamsTestType]
) -> None:
    for test_case in sample_data_values:
        test_case["qn_grouped_params_df"] = set_qualified_name_timestamp_index(
            df=test_case["qn_grouped_params_df"]
        )
        test_case["qn_grouped_moneyness_df"] = (
            set_qualified_name_timestamp_index(
                df=test_case["qn_grouped_moneyness_df"]
            )
        )
        test_case["qn_grouped_smile_df"] = set_qualified_name_timestamp_index(
            df=test_case["qn_grouped_smile_df"]
        )

        result_df = (
            estimate_new_params(
                qn_grouped_params_df=test_case["qn_grouped_params_df"],
                moneyness_vol_df=test_case["qn_grouped_moneyness_df"],
                delta_vol_df=test_case["qn_grouped_smile_df"],
                smooth_config=test_case["smoothing_config"],
            )
            .reset_index()
            .sort_index(axis=1)
        )

        result_df = result_df.round(5)

        snapshot_name = f"{test_case['name']}_result.json"
        snapshot.assert_match(
            json.dumps(result_df.to_dict(orient="row"), indent=2),  # type: ignore
            snapshot_name,
        )
