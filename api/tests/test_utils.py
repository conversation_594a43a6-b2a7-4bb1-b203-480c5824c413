import json
import logging
import pathlib
from typing import Any, Literal, cast
from unittest.mock import call, patch

import numpy as np
import pandas as pd
import pytest
import utils_general
from calc_helpers import get_atm_deltas_from_boundaries
from constants import LOOKBACK_COLUMN_NAME
from lambda_types import (
    AuxCalcType,
    NDArrayFloat64,
    SingleParams,
)
from pandas._testing import assert_series_equal
from process_data import get_frequency_details
from utils.calibrate import recalibrate_params_post_smoothing
from utils.derived_surface import smile_derived_data
from utils.interpolate import (
    get_delta_implied_strike_grid_for_expiry,
    get_strikes_to_interpolate,
)
from utils.outputs import create_output_col, undo_create_output_col
from utils.smoothing import (
    _add_spiked_params_flag,
    _df_has_valid_non_lookback_data,
    _is_expired_option,
    construct_smoothing_config,
    get_relevant_moneys_and_deltas,
    get_strikes_and_vols_from_vol_space_mappings,
)
from utils.surface import (
    get_min_max_strike_from_deltas,
    validate_estimated_strikes,
)
from utils_calc import DEFAULT_SURFACE_MONEYNESS as MONEYNESS
from utils_calc import Model

from .helpers import NumpyEncoder, convert_df
from .test_types import CalcHelpersTestDataType, SmileDerivedDataTestType


def finalize_result_df() -> None:
    # test no overlap between standard/arbitrary/listed -> no marks at all
    # test overlap between standard/listed no arbitrary -> listed should be flagged
    # test overlap between arbitrary/listed no arbitrary -> arbitrary should not be flagged
    # test overlap between standard/arbitrary/listed ->
    return


@pytest.fixture
def sample_svi_bordering_interpolation_data() -> (
    dict[str, float | dict[str, float] | Model | str]
):
    return {
        "upper_expiry": {
            "expiry": 0.006393,
            "svi_a": -0.000465,
            "svi_b": 0.025518,
            "svi_rho": 0.23608,
            "svi_m": 0.011534,
            "svi_sigma": 0.045956,
            "forward": 52218.250166,
        },
        "lower_expiry": {
            "expiry": 0.003653,
            "svi_a": -0.000218,
            "svi_b": 0.017374,
            "svi_rho": -0.106836,
            "svi_m": -0.005048,
            "svi_sigma": 0.028563,
            "forward": 52195.026053,
        },
        "spot": 52164.076635,
        "model": "SVI",
        "interpolated_forward": 52210.50764693566,
        "exchange": "deribit",
    }


@pytest.fixture
def sample_sabr_bordering_interpolation_data() -> (
    dict[str, float | dict[str, float] | Model | str]
):
    return {
        "upper_expiry": {
            "expiry": 0.006393,
            "sabr_alpha": 0.301195,
            "sabr_rho": 0.047141,
            "sabr_volvol": 10.018029,
            "forward": 52218.250166,
        },
        "lower_expiry": {
            "expiry": 0.003653,
            "sabr_alpha": 0.250633,
            "sabr_rho": 0.031258,
            "sabr_volvol": 15.549214,
            "forward": 52195.026053,
        },
        "spot": 52164.076635,
        "model": "SABR",
        "interpolated_forward": 52210.50764693566,
        "exchange": "deribit",
    }


@pytest.fixture
def sample_sabr_calc_helpers_data() -> CalcHelpersTestDataType:
    return {
        "params": {
            "sabr_alpha": 0.440503,
            "sabr_rho": -0.074262,
            "sabr_volvol": 5.868784,
        },
        "spot": 52164.076635,
        "forward": 52311.25,
        "expiry": 0.017351598174,  # 6.333333333510001 days
        "model": cast(Model, "SABR"),
        "exchange": "deribit",
        "min_delta": -0.1,
        "max_delta": 0.1,
    }


@pytest.fixture
def sample_svi_calc_helpers_data() -> CalcHelpersTestDataType:
    return {
        "params": {
            "svi_a": -0.001672,
            "svi_b": 0.05031,
            "svi_rho": -0.260443,
            "svi_m": -0.046692,
            "svi_sigma": 0.111694,
        },
        "spot": 52164.076635,
        "forward": 52311.25,
        "expiry": 0.017351598174,  # 6.333333333510001 days
        "model": cast(Model, "SVI"),
        "exchange": "deribit",
        "min_delta": -0.1,
        "max_delta": 0.1,
    }


# fmt: off
@pytest.mark.parametrize(
    "model_params_data_fixture, expected_result",
    [
        ("sample_sabr_calc_helpers_data",
         [41001.99740987873, 43142.15741747893, 44340.248278705934, 45166.94964556697, 45795.64251858114,
          47690.855429632, 48776.93078898234, 49553.30422006538, 50170.51914813981, 50694.43831477252,
          51160.292948174174, 51589.788496617286, 51998.01323867433, 52396.78893834289, 52419.22575467952,
          52819.442780199846, 53232.06828360871, 53669.67167799226, 54148.86817429962, 54694.31190463084,
          55347.20925734259, 56187.04597435875, 57402.789259312376, 59661.41367898574, 60454.78749826341,
          61535.29201350418, 63182.486967681885, 66394.77162096812]),
        ("sample_svi_calc_helpers_data",
         [40230.814137589485, 42891.57701017714, 44356.83233848808, 45326.01776821987, 46030.428321201245,
          47967.9020374893, 48981.207954261656, 49688.063705345514, 50251.58182896576, 50737.09539844673,
          51177.650620091365, 51592.995792762114, 51996.76197173886, 52399.78765207828, 52422.7103633893,
          52835.7671533912, 53269.182366603294, 53735.73959975301, 54252.70495454487, 54845.93377202304,
          55558.556842879014, 56472.78122671753, 57779.482005308426, 60122.453505121826, 60914.592644892284,
          61966.1838022259, 63508.86512378834, 66316.00021645139]),
    ],
)
def test_get_delta_implied_strike_grid_for_expiry(model_params_data_fixture: str, expected_result: list[float],
                                                  request: pytest.FixtureRequest) -> None:
    model_calc_helpers_fixture = request.getfixturevalue(model_params_data_fixture)
    result = get_delta_implied_strike_grid_for_expiry(
        params=model_calc_helpers_fixture["params"],
        spot=model_calc_helpers_fixture["spot"],
        forward=model_calc_helpers_fixture["forward"],
        expiry=model_calc_helpers_fixture["expiry"],
        model=model_calc_helpers_fixture["model"],
        exchange=model_calc_helpers_fixture["exchange"],
    )
    assert np.all(
        np.isclose(np.asarray(result), np.asarray(expected_result), rtol=1e-4)
    )


# fmt: on


@pytest.fixture
def log_capture(caplog: pytest.LogCaptureFixture) -> pytest.LogCaptureFixture:
    caplog.set_level(logging.WARNING)
    return caplog


def test_get_atm_deltas_from_boundaries() -> None:
    min_delta = -0.1
    max_delta = 0.1
    expected = [
        -0.1,
        -0.15,
        -0.2,
        -0.25,
        -0.3,
        -0.35,
        -0.4,
        -0.45,
        -0.5,
        0.5,
        0.45,
        0.4,
        0.35,
        0.3,
        0.25,
        0.2,
        0.15,
        0.1,
    ]
    assert (
        get_atm_deltas_from_boundaries(
            put_wing_delta=min_delta, call_wing_delta=max_delta
        )
        == expected
    )

    min_delta = -0.3
    max_delta = 0.2
    expected = [
        -0.3,
        -0.35,
        -0.4,
        -0.45,
        -0.5,
        0.5,
        0.45,
        0.4,
        0.35,
        0.3,
        0.25,
        0.2,
    ]
    assert (
        get_atm_deltas_from_boundaries(
            put_wing_delta=min_delta, call_wing_delta=max_delta
        )
        == expected
    )


@pytest.mark.parametrize(
    "model_data_fixtures, expected_min_strike, expected_max_strike",
    [
        ("sample_sabr_calc_helpers_data", 47690.855429632, 57402.789259312376),
        ("sample_svi_calc_helpers_data", 47967.9020374893, 57779.482005308426),
    ],
)
def test_get_min_max_strike_from_deltas(
    model_data_fixtures: str,
    expected_min_strike: float,
    expected_max_strike: float,
    request: pytest.FixtureRequest,
) -> None:
    model_data_fixture = request.getfixturevalue(model_data_fixtures)

    result = get_min_max_strike_from_deltas(
        params=model_data_fixture["params"],
        spot=model_data_fixture["spot"],
        forward=model_data_fixture["forward"],
        expiry=model_data_fixture["expiry"],
        model=model_data_fixture["model"],
        exchange=model_data_fixture["exchange"],
        min_delta=model_data_fixture["min_delta"],
        max_delta=model_data_fixture["max_delta"],
    )
    assert np.isclose(result["min_strike"], expected_min_strike, rtol=1e-4)
    assert np.isclose(result["max_strike"], expected_max_strike, rtol=1e-4)


# fmt: off
@pytest.mark.parametrize(
    "model_data_fixtures, expected_results",
    [
        ("sample_svi_bordering_interpolation_data",
         (
                 [47094.571964340656, 48074.17500518147, 48627.986822455554, 49010.28624252349, 49300.18425379407,
                  50163.48519145431, 50647.579741534166, 50988.36129453971, 51256.483441441516, 51482.479387628904,
                  51682.467946050725, 51866.262366565046, 52040.62960512528, 52210.87010770313, 52214.39654763429,
                  52385.34929813633, 52562.05639637475, 52750.610561748785, 52959.51661804481, 53202.27912549363,
                  53503.40387276675, 53915.11137471615, 54576.73141262424, 56026.91183063142, 56584.574904928515,
                  57360.37317662442, 58545.36667912664, 60765.096813448865],
                 [47108.54070992544, 48088.434311335775, 48642.410395022205, 49024.82320912078, 49314.80720719187,
                  50178.364208994084, 50662.602346611246, 51003.4849790112, 51271.68665376302, 51497.74963272603,
                  51697.7975098586, 51881.646445731974, 52056.065403449895, 52226.35640115212, 52229.883887062475,
                  52400.88734394912, 52577.64685537223, 52766.25694789572, 52975.22496792621, 53218.05948128914,
                  53519.27354533081, 53931.103164057364, 54592.91944543043, 56043.530002208725, 56601.35848524152,
                  57377.386866867935, 58562.731850911776, 60783.120380548375],
                 [47115.526636532515, 48095.56555054815, 48649.62378571294, 49032.09330944023, 49322.12031047633,
                  50185.80537283284, 50670.115320190635, 51011.04850353136, 51279.28995105448, 51505.3864538617,
                  51705.46399694797, 51889.34019656489, 52063.78501961464, 52234.101270495885, 52237.629279512184,
                  52408.65809523146, 52585.443819077096, 52774.081881396385, 52983.08089018663, 53225.95141451622,
                  53527.21014687731, 53939.10083757612, 54601.015262510824, 56051.84093652121, 56609.75214232111,
                  57385.89560450908, 58571.41636842082, 60792.134168951256]
         )
         ),
        ("sample_sabr_bordering_interpolation_data",
         (
                 [47257.70020831695, 48261.1271250185, 48804.69891764736, 49172.51482526734, 49448.40844956281,
                  50261.429987121606, 50716.24340177774, 51037.6056088386, 51291.63052325238, 51506.819460776525,
                  51698.27300153293, 51875.24648429697, 52044.17152350371, 52210.11303694659, 52213.560785662106,
                  52381.152606667936, 52555.110272891376, 52740.98027970425, 52946.14799380933, 53181.62444610724,
                  53465.85534572838, 53834.42358542324, 54371.72734161581, 55373.751190001174, 55725.25634205837,
                  56202.73990329639, 56926.939962991506, 58323.01011828309],
                 [47271.71733945537, 48275.441883138716, 48819.17490488085, 49187.099910569275, 49463.07536781673,
                  50276.33805611837, 50731.28637321894, 51052.74389969187, 51306.84416056817, 51522.09692539545,
                  51713.607253286566, 51890.63322824775, 52059.608372398594, 52225.59910584043, 52229.0478771945,
                  52396.68940769788, 52570.69867159554, 52756.62380940821, 52961.852378416115, 53197.39867550752,
                  53481.71388100422, 53850.39144191714, 54387.85456805743, 55390.17562725976, 55741.78503944367,
                  56219.410227330154, 56943.82509238318, 58340.3093367726],
                 [47278.72746422144, 48282.60085450224, 48826.41450873535, 49194.39407559353, 49470.41045841968,
                  50283.79374891717, 50738.80953224585, 51060.31472902767, 51314.4526715164, 51529.73735709509,
                  51721.276084870275, 51898.32831176901, 52067.32851396533, 52233.343862881644, 52236.79314566873,
                  52404.45953645033, 52578.49460492458, 52764.44731436964, 52969.70631759815, 53205.2875448555,
                  53489.64491266776, 53858.37714635006, 54395.91997519175, 55398.38967286283, 55750.05122670748,
                  56227.74724367204, 56952.26953529792, 58348.9608702975]
         )
         )
    ],
)
def test_get_strikes_to_interpolate(model_data_fixtures: str,
                                    expected_results: tuple[list[float], list[float], list[float]],
                                    request: pytest.FixtureRequest) -> None:
    model_data_fixture = request.getfixturevalue(model_data_fixtures)

    expected_lower_bordering_strikes = expected_results[0]
    expected_interpolated_tenor_strikes = expected_results[1]
    expected_upper_bordering_strikes = expected_results[2]

    (
        resulting_lower_bordering_strikes,
        resulting_interpolated_tenor_strikes,
        resulting_upper_bordering_strikes,
    ) = get_strikes_to_interpolate(
        upper_expiry=model_data_fixture["upper_expiry"],
        lower_expiry=model_data_fixture["lower_expiry"],
        model=model_data_fixture["model"],
        spot=model_data_fixture["spot"],
        interpolated_forward=model_data_fixture["interpolated_forward"],
        exchange=model_data_fixture["exchange"],
    )

    assert np.all(
        np.isclose(
            resulting_lower_bordering_strikes,
            expected_lower_bordering_strikes,
            rtol=1e-4,
        )
    )
    assert np.all(
        np.isclose(
            resulting_interpolated_tenor_strikes,
            expected_interpolated_tenor_strikes,
            rtol=1e-4,
        )
    )
    assert np.all(
        np.isclose(
            resulting_upper_bordering_strikes,
            expected_upper_bordering_strikes,
            rtol=1e-4,
        )
    )


# fmt: on


class TestGetRelevantMoneysAndDeltas:
    def test_normal_conditions(self) -> None:
        test: CalcHelpersTestDataType = {
            "forward": 3517.488520617148,
            "spot": 3412.7584355,
            "timestamp": 1709304420000000000,
            "params": {
                "sabr_alpha": 0.6116325780443809,
                "sabr_rho": 0.10339969873705107,
                "sabr_volvol": 1.7840301212423182,
            },
            "atm_vol": 0.6407077554972249,
            "qualified_name": "deribit.option.ETH.SABR.60d.1m.params",
            "expiry": 0.1643835616438356,  # 60d tenor
            "model": "SABR",
            "exchange": "deribit",
        }

        # fmt: off
        expected_deltas = [-0.01, -0.02, -0.03, -0.04, -0.05, -0.1, -0.15, -0.2, -0.25, -0.3, -0.35, -0.4, -0.45, -0.5,
                           0.5, 0.45, 0.4, 0.35, 0.3, 0.25, 0.2, 0.15, 0.1, 0.05, 0.04, 0.03, 0.02, 0.01]
        expected_monies = [0.5, 0.6, 0.7, 0.8, 0.9, 0.91, 0.92, 0.93, 0.94, 0.95, 0.96, 0.97, 0.98, 0.99, 1, 1.01, 1.02,
                           1.03, 1.04, 1.05, 1.06, 1.07, 1.08, 1.09, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9, 2,
                           2.1, 2.2, 2.3, 2.4, 2.5, 2.6, 2.7, 2.8, 2.9, 3]
        # fmt: on

        result_moneyness, result_deltas = get_relevant_moneys_and_deltas(
            params=test["params"],
            spot=test["spot"],
            forward=test["forward"],
            expiry=test["expiry"],
            model=test["model"],
            exchange=test["exchange"],
        )
        assert np.all(np.isclose(result_deltas, expected_deltas, atol=0.1))
        assert np.all(np.isclose(result_moneyness, expected_monies, atol=0.1))

    def test_short_expiry_strikes(self) -> None:
        """Test the function when there are less than 3 relevant moneyness strikes"""
        test: CalcHelpersTestDataType = {
            "forward": 3415.023115884238,
            "spot": 3412.7584355,
            "timestamp": 1709304420000000000,
            "params": {
                "sabr_alpha": 0.5806189649918646,
                "sabr_rho": 0.025871338669332694,
                "sabr_volvol": 12.43169900356713,
            },
            "model": "SABR",
            "exchange": "deribit",
            "atm_vol": 0.6011596468061438,
            "qualified_name": "deribit.option.ETH.SABR.1d.1m.params",
            "expiry": 0.0027397260273972603,  # 1d Tenor
        }
        # fmt: off
        expected_deltas = [-0.01, -0.02, -0.03, -0.04, -0.05, -0.1, -0.15, -0.2, -0.25, -0.3, -0.35, -0.4, -0.45, -0.5,
                           0.5, 0.45, 0.4, 0.35, 0.3, 0.25, 0.2, 0.15, 0.1, 0.05, 0.04, 0.03, 0.02, 0.01]
        expected_monies = [0.91, 0.92, 0.93, 0.94, 0.95, 0.96, 0.97, 0.98, 0.99, 1, 1.01, 1.02, 1.03, 1.04, 1.05, 1.06,
                           1.07, 1.08, 1.09, 1.1]
        # fmt: on

        result_moneyness, result_deltas = get_relevant_moneys_and_deltas(
            params=test["params"],
            spot=test["spot"],
            forward=test["forward"],
            expiry=test["expiry"],
            model=test["model"],
            exchange=test["exchange"],
        )
        assert np.all(np.isclose(result_deltas, expected_deltas, atol=0.1))
        assert np.all(np.isclose(result_moneyness, expected_monies, atol=0.1))

    # def test_insufficient_moneyness_srtrikes_for_svi(self) -> None:
    #     """Test the function when there are less than 3 relevant moneyness strikes"""
    #     test = {
    #         "tenor_days": 0.1,
    #         "svi_jw_atm_total_var": 0.00015917239107210435,
    #         "spot": 3899.4060215,
    #         "arbitrary_expiry": True,
    #         "qualified_name": "deribit.option.ETH.SVI.2024-03-10T23:13:00Z.1m.params",
    #         "forward": 3899.824187852123,
    #         "timestamp": 1710103740000000000,
    #         "params": {
    #             "svi_a": -43.7346338579775,
    #             "svi_b": 1.0,
    #             "svi_m": 0.0,
    #             "svi_rho": 0.00020861472555517386,
    #             "svi_sigma": 43.73479303036857,
    #         },
    #         "model": "SVI",
    #         "svi_jw_min_imp_var": 0.577505626347283,
    #         "svi_jw_slope_right": 79.27873661988193,
    #         "atm_vol": 0.7622199333927515,
    #         "expiry": 0.000273972603,  # 0.1d arbitrary subscribed tenor
    #         "exchange": "deribit",
    #     }
    #
    #     # fmt: off
    #     expected_deltas = [-0.02, -0.03, -0.04, -0.05, -0.1, -0.15, -0.2, -0.25, -0.3, -0.35, -0.4, -0.45, -0.5, 0.5, 0.45, 0.4, 0.35, 0.3, 0.25, 0.2, 0.15, 0.1, 0.05, 0.04, 0.03, 0.02]
    #     expected_monies = [0.5, 0.6, 0.7, 0.8, 0.9, 0.91, 0.92, 0.93, 0.94, 0.95, 0.96, 0.97, 0.98, 0.99, 1, 1.01, 1.02, 1.03, 1.04, 1.05, 1.06, 1.07, 1.08, 1.09, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9, 2, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6, 2.7, 2.8, 2.9, 3]
    #     # fmt: on
    #     expected_log_message = "Insufficient relevant monies derived, adding extra monies to left and right wing, spot=3899.4060215 forward=3899.824187852123 expiry=0.000273972603 model='SVI' exchange='deribit'"
    #
    #     with patch("logging.log") as mock_log:
    #
    #         result_moneyness, result_deltas = get_relevant_moneys_and_deltas(
    #             params=test["params"],
    #             spot=test["spot"],
    #             forward=test["forward"],
    #             expiry=test["expiry"],
    #             model=test["model"],
    #             exchange=test["exchange"],
    #         )
    #         mock_log.assert_has_calls(
    #             [
    #                 call(15, expected_log_message),
    #             ],
    #             any_order=False,
    #         )
    #     assert np.all(np.isclose(result_deltas, expected_deltas, atol=0.1))
    #     assert np.all(np.isclose(result_moneyness, expected_monies, atol=0.1))

    def test_invalid_delta_ranges(self) -> None:
        """Test that we fall back to the MONEYNESS grid when delta estimation fails"""
        test: CalcHelpersTestDataType = {
            "forward": 3594.7373519485136,
            "spot": 3407.3672440000005,
            "timestamp": 1709304360000000000,
            "params": {
                "sabr_alpha": 10.130066590395625,
                "sabr_rho": 0.022420420930458942,
                "sabr_volvol": -4.7227916660319575,
            },
            "atm_vol": 15.422684086166369,
            "qualified_name": "deribit.option.ETH.SABR.120d.1m.params",
            "expiry": 0.3287671232876712,  # 120 day tenor
            "exchange": "deribit",
            "model": "SABR",
        }
        expected_call = "Invalid min and max strikes derived from delta estimation, falling back to standard moneyness grid, min_money=1.955, max_money=1.896"
        # fmt: off
        expected_deltas = [-0.01, -0.02, -0.03, -0.04, -0.05, -0.1, -0.15, -0.2, -0.25, -0.3, -0.35, -0.4, -0.45, -0.5,
                           0.5, 0.45, 0.4, 0.35, 0.3, 0.25, 0.2, 0.15, 0.1, 0.05, 0.04, 0.03, 0.02, 0.01]
        # fmt: on
        with patch("logging.warning") as mock_warning:
            result_moneyness, result_deltas = get_relevant_moneys_and_deltas(
                params=test["params"],
                spot=test["spot"],
                forward=test["forward"],
                expiry=test["expiry"],
                model=test["model"],
                exchange=test["exchange"],
            )
            mock_warning.assert_called_once()
            mock_warning.assert_has_calls(
                [call(expected_call)],
                any_order=False,
            )
        assert np.all(np.isclose(result_deltas, expected_deltas, atol=0.1))
        assert np.all(np.isclose(result_moneyness, MONEYNESS, atol=0.1))


@pytest.mark.parametrize("calc_type", ["skew", "butterfly"])
def test_smile_derived_data(
    snapshot: Any,
    calc_type: AuxCalcType,
    sample_smile_derived_data_values: SmileDerivedDataTestType,
) -> None:
    result = smile_derived_data(
        params_surface=sample_smile_derived_data_values["params_surface"],
        delta_vol_matrix_df=sample_smile_derived_data_values[
            "delta_vol_matrix_df"
        ],
        calc_types=[calc_type],
        model="SVI",
    )

    for key in result["dfs"]:
        key = cast(AuxCalcType, key)
        result["dfs"][key].drop(["runtime"], axis="columns", inplace=True)

    flattened_result = result.copy()
    for key in flattened_result["dfs"].keys():
        key = cast(AuxCalcType, key)
        # we convert to json so that we can use pytest-snapshot
        # we ignore the types as it expects a dataframe
        flattened_result["dfs"][key] = flattened_result["dfs"][key].round(8)
        flattened_result["dfs"][key] = convert_df(  # type: ignore
            flattened_result["dfs"][key]
        ).to_dict(orient="records")

    snapshot.assert_match(
        json.dumps(result, indent=2, cls=NumpyEncoder),
        "aux_calc_type_output.json",
    )


@pytest.mark.parametrize(
    "forward, strikes_list, deltas, expected",
    [
        # fmt: off
        # v-00003.bybit.option.BTC.SVI.365d.1h.params
        # 2023-10-03T09:00:00.000000Z 1h
        (
                29039.303419956777,
                [8403.854366110763, 10488.705774227481, 11910.990596072685, 13024.1626741173, 13953.890385680987,
                 17287.216307254155, 19647.54364352113, 21589.575301076842, 23310.47992100871, 24908.94822222173,
                 26446.121901681276, 27967.296562893098, 29512.317230665554, 31122.56689551521, 32925.62240416611,
                 34845.76234774784, 37066.10144749127, 39792.065905706266, 43488.16119769194, 49742.832254062996,
                 3.599535219743336e+32, 9.083994166023739e+48, 1.473796836668978e+74, 1.1076062142751102e+121,
                 7.062876471891051e+136, 4.08878987869505e+157, 3.0967477144066553e+187, 7.464951191735402e+239],
                [-0.01, -0.02, -0.03, -0.04, -0.05, -0.1, -0.15, -0.2, -0.25, -0.3, -0.35, -0.4, -0.45, -0.5, 0.5, 0.45,
                 0.4, 0.35, 0.3, 0.25, 0.2, 0.15, 0.1, 0.05, 0.04, 0.03, 0.02, 0.01],
                {
                    "strikes": np.asarray([8403.854366110763, 10488.705774227481, 11910.990596072685, 13024.1626741173,
                     13953.890385680987, 17287.216307254155, 19647.54364352113, 21589.575301076842,
                     23310.47992100871, 24908.94822222173, 26446.121901681276, 27967.296562893098,
                     29512.317230665554, 31122.56689551521, 32925.62240416611, 34845.76234774784, 37066.10144749127,
                     39792.065905706266, 43488.16119769194, 49742.832254062996]),
                    "deltas": np.asarray([-0.01, -0.02, -0.03, -0.04, -0.05, -0.1, -0.15, -0.2, -0.25, -0.3, -0.35, -0.4, -0.45, -0.5,
                     0.5, 0.45, 0.4, 0.35, 0.3, 0.25]),
                },
        ),
        # v-00003.bybit.option.BTC.SVI.365d.1h.params
        # 2023-10-03T04:00:00.000000Z 1h
        (
                29033.46337529632,
                [7888.456412840641, 10040.293286451059, 11515.508132292824, 12671.699359748522, 13637.52021628674,
                 17094.1177263177, 19529.81755200855, 21522.87150006315, 23278.45922528777, 24898.259036571457,
                 26443.92857655307, 27959.464011072632, 29481.453755568236, 31045.233232576094, 32753.058567132,
                 34534.44347042756, 36519.33835126815, 38819.63723932439, 41635.67996207587, 45393.87205837923,
                 51325.67180291835, 70526.66177893951, 6.090031557110661e+49, 9.979055503339521e+81,
                 7.081452407195009e+92, 1.264160528647169e+107, 4.046335756498019e+127, 3.537717911759788e+163],
                [-0.01, -0.02, -0.03, -0.04, -0.05, -0.1, -0.15, -0.2, -0.25, -0.3, -0.35, -0.4, -0.45, -0.5, 0.5, 0.45,
                 0.4, 0.35, 0.3, 0.25, 0.2, 0.15, 0.1, 0.05, 0.04, 0.03, 0.02, 0.01],
                {
                    "strikes": np.asarray([7888.456412840641, 10040.293286451059, 11515.508132292824, 12671.699359748522,
                     13637.52021628674, 17094.1177263177, 19529.81755200855, 21522.87150006315, 23278.45922528777,
                     24898.259036571457, 26443.92857655307, 27959.464011072632, 29481.453755568236,
                     31045.233232576094, 32753.058567132, 34534.44347042756, 36519.33835126815, 38819.63723932439,
                     41635.67996207587, 45393.87205837923, 51325.67180291835, 70526.66177893951]),
                    "deltas": np.asarray([-0.01, -0.02, -0.03, -0.04, -0.05, -0.1, -0.15, -0.2, -0.25, -0.3, -0.35, -0.4, -0.45, -0.5,
                     0.5, 0.45, 0.4, 0.35, 0.3, 0.25, 0.2, 0.15])
                },
        ),
        # v-00003.bybit.option.BTC.SVI.365d.1h.params
        # 2023-10-02T20:00:00.000000Z 1h
        (
                29430.450596993058,
                [9652.877449089583, 11549.309939481484, 12833.603151746132, 13840.093008216723, 14684.229616333405,
                 17759.700499470153, 20001.9402088233, 21893.99363491506, 23606.746701615197, 25226.27947607586,
                 26806.604022029354, 28388.294945225374, 30007.282025954835, 31700.508831809882, 33587.28261937632,
                 35580.28271087355, 37832.10706878632, 40473.27585901012, 43735.35866041864, 48098.79692785398,
                 54868.122327370846, 71650.74578844635, 8.172948598504774e+113, 5.690942037687052e+188,
                 9.630806692034879e+213, 1.3326045809973788e+247, 6.53827792415009e+294, 90899.44987365989],
                [-0.01, -0.02, -0.03, -0.04, -0.05, -0.1, -0.15, -0.2, -0.25, -0.3, -0.35, -0.4, -0.45, -0.5, 0.5, 0.45,
                 0.4, 0.35, 0.3, 0.25, 0.2, 0.15, 0.1, 0.05, 0.04, 0.03, 0.02, 0.01],
                {
                    "strikes": np.asarray([9652.877449089583, 11549.309939481484, 12833.603151746132, 13840.093008216723,
                     14684.229616333405, 17759.700499470153, 20001.9402088233, 21893.99363491506,
                     23606.746701615197, 25226.27947607586, 26806.604022029354, 28388.294945225374,
                     30007.282025954835, 31700.508831809882, 33587.28261937632, 35580.28271087355,
                     37832.10706878632, 40473.27585901012, 43735.35866041864, 48098.79692785398, 54868.122327370846,
                     71650.74578844635]),
                    "deltas": np.asarray([-0.01, -0.02, -0.03, -0.04, -0.05, -0.1, -0.15, -0.2, -0.25, -0.3, -0.35, -0.4, -0.45, -0.5,
                     0.5, 0.45, 0.4, 0.35, 0.3, 0.25, 0.2, 0.15])
                },
        )
        # fmt: on
    ],
)
def test_validate_estimated_strikes(
    forward: float,
    strikes_list: list[float],
    deltas: list[float],
    expected: dict[str, NDArrayFloat64],
) -> None:
    result = validate_estimated_strikes(
        forward=forward, strikes_list=strikes_list, deltas=deltas
    )
    assert result
    assert sorted(result.keys()) == sorted(expected.keys())

    for key in result.keys():
        assert np.array_equal(result[key], expected[key])


@pytest.mark.parametrize(
    "input_col, suffix, scaling_factor, expected_output",
    [
        ("50delta", "delta", 100, 0.5),
        ("50d51delta", "delta", 100, 0.5051),
        ("-50delta", "delta", 100, -0.5),
        ("1delta", "delta", 100, 0.01),
        ("-1delta", "delta", 100, -0.01),
        ("100money", "money", 100, 1.0),
        ("10money", "money", 100, 0.1),
        ("10money", "cashmoni", 100, None),
    ],
)
def test_undo_create_output_col(
    input_col: str,
    suffix: str,
    scaling_factor: int,
    expected_output: int | None,
) -> None:
    if expected_output is not None:
        result = undo_create_output_col(input_col, suffix, scaling_factor)
        assert (
            result == expected_output
        ), "The function should return the correct original value"
    else:
        with pytest.raises(ValueError):
            undo_create_output_col(input_col, suffix, scaling_factor)


@pytest.mark.parametrize(
    "input, suffix, scaling_factor, expected_output",
    [
        ("0.5", "delta", 100, "50delta"),
        ("0.5051", "delta", 100, "50d51delta"),
        ("0.035", "delta", 100, "3d5delta"),
        ("-0.5", "delta", 100, "-50delta"),
        ("0.01", "delta", 100, "1delta"),
        ("-0.01", "delta", 100, "-1delta"),
        ("1.0", "money", 100, "100money"),
        ("0.1", "money", 100, "10money"),
        ("0.1", "cashmoni", 10, "1cashmoni"),
    ],
)
def test_create_output_col(
    input: str,
    suffix: str,
    scaling_factor: int,
    expected_output: str,
) -> None:
    if expected_output is not None:
        result = create_output_col(
            suffix=suffix, col_str=input, scaling_factor=scaling_factor
        )
        assert (
            result == expected_output
        ), "The function should return the correct original value"


@pytest.mark.parametrize(
    "mapping_object, vol_space, exchange, expected_output",
    [
        (
            {
                "forward": 2.0,
                "spot": 2.0,
                "50money": 0.8,
                "100money": 0.5,
                "150money": 0.9,
            },
            "money",
            "deribit",
            {"vols": [0.8, 0.5, 0.9], "strikes": [1, 2, 3]},
        ),
        (
            pd.Series(
                {
                    "forward": 2.0,
                    "spot": 2.0,
                    "50money": 0.8,
                    "100money": 0.5,
                    "150money": 0.9,
                }
            ),
            "money",
            "deribit",
            {"vols": [0.8, 0.5, 0.9], "strikes": [1, 2, 3]},
        ),
        (
            {
                "forward": 29428.982482839125,
                "spot": 28001.555,
                "expiry": 1.0,
                "-1delta": 0.5111700026949335,
                "-2delta": 0.49456070015602804,
                "-3delta": 0.48535650421884846,
                "3delta": 0.7648032324576493,
                "2delta": 0.7645145481162753,
                "1delta": 0.7670878023789247,
            },
            "delta",
            "deribit",
            {
                "vols": [
                    0.5111700026949335,
                    0.49456070015602804,
                    0.48535650421884846,
                    0.7648032324576493,
                    0.7645145481162753,
                    0.7670878023789247,
                ],
                "strikes": [
                    10114.420102171081,
                    11922.799608181589,
                    13148.596093101565,
                    168945.17004856633,
                    192478.55987775305,
                    238643.32700342155,
                ],
            },
        ),
        (
            pd.Series(
                {
                    "forward": 29428.982482839125,
                    "spot": 28001.555,
                    "expiry": 1.0,
                    "-1delta": 0.5111700026949335,
                    "-2delta": 0.49456070015602804,
                    "-3delta": 0.48535650421884846,
                    "3delta": 0.7648032324576493,
                    "2delta": 0.7645145481162753,
                    "1delta": 0.7670878023789247,
                }
            ),
            "delta",
            "deribit",
            {
                "vols": [
                    0.5111700026949335,
                    0.49456070015602804,
                    0.48535650421884846,
                    0.7648032324576493,
                    0.7645145481162753,
                    0.7670878023789247,
                ],
                "strikes": [
                    10114.420102171081,
                    11922.799608181589,
                    13148.596093101565,
                    168945.17004856633,
                    192478.55987775305,
                    238643.32700342155,
                ],
            },
        ),
    ],
)
def test_get_strikes_and_vols_from_vol_space_mappings(
    mapping_object: SingleParams,
    vol_space: Literal["delta", "money"],
    exchange: str,
    expected_output: dict[str, list[float]],
) -> None:
    result = get_strikes_and_vols_from_vol_space_mappings(
        mapping_object=mapping_object, vol_space=vol_space, exchange=exchange
    )
    assert result == expected_output


# fmt: off
@pytest.mark.parametrize(
    "test_case, expected_result",
    [
        (
            # SVI - moneyss vol space
            {
                'strikes': [31084.27324406567, 25903.561036721392, 23313.204933049252, 33674.62934773781, 20722.848829377115, 28493.917140393532, 23572.24054341647, 23831.27615378368, 24090.311764150894, 24349.347374518107, 24608.38298488532, 24867.418595252537, 25126.45420561975, 25385.489815986963, 25644.52542635418, 26162.596647088605, 26421.63225745582, 26680.667867823035, 26939.703478190248, 27198.739088557464, 27457.774698924677, 27716.81030929189, 27975.845919659107, 28234.88153002632],
                'vols': [0.5020907729589615, 0.25789124196293994, 0.46717031875892234, 0.593565106680457, 0.6451003428290497, 0.3812672430830272, 0.44714479188775713, 0.4265410647647387, 0.40531244906574604, 0.38342596250042604, 0.3608867473194277, 0.3377942207937868, 0.31447295801913183, 0.29177373832949177, 0.2716780412037415, 0.25429641292391875, 0.26075698386068086, 0.27345728841327055, 0.28894738129139136, 0.3053420809475614, 0.32176998364378956, 0.3378519490596266, 0.3534341534824254, 0.36846600380393313],
                'forward': 25903.561036721392,
                'spot': 25866.99,
                'timestamp': 1694307600000000000,
                'qualified_name': 'bybit.option.BTC.SVI.14d.1m.moneyness',
                'vol_space': 'money',
                'expiry': 0.038356164384
             },
            {
                'svi_a': 0.0010525132008110034,
                'svi_b': 0.056575442597967124,
                'svi_rho': -0.15247212173355892,
                'svi_m': 0.004322431316898409,
                'svi_sigma': 0.025531172617852066,
                'atm_vol': 0.25808342036109455,
                'expiry': 0.038356164384,
                'forward': 25903.561036721392,
                'spot': 25866.99,
                'vol_space': 'money',
                'strikes': [31084.27324406567, 25903.561036721392, 23313.204933049252, 33674.62934773781, 20722.848829377115, 28493.917140393532, 23572.24054341647, 23831.27615378368, 24090.311764150894, 24349.347374518107, 24608.38298488532, 24867.418595252537, 25126.45420561975, 25385.489815986963, 25644.52542635418, 26162.596647088605, 26421.63225745582, 26680.667867823035, 26939.703478190248, 27198.739088557464, 27457.774698924677, 27716.81030929189, 27975.845919659107, 28234.88153002632],
                'vols': [0.5020907729589615, 0.25789124196293994, 0.46717031875892234, 0.593565106680457, 0.6451003428290497, 0.3812672430830272, 0.44714479188775713, 0.4265410647647387, 0.40531244906574604, 0.38342596250042604, 0.3608867473194277, 0.3377942207937868, 0.31447295801913183, 0.29177373832949177, 0.2716780412037415, 0.25429641292391875, 0.26075698386068086, 0.27345728841327055, 0.28894738129139136, 0.3053420809475614, 0.32176998364378956, 0.3378519490596266, 0.3534341534824254, 0.36846600380393313],
                'svi_jw_atm_var': 0.06660705186528143,
                'svi_jw_atm_total_var': 0.002554791030478348,
                'svi_jw_atm_skew': -0.17875203952810856,
                'svi_jw_slope_left': 1.2899732290608326,
                'svi_jw_slope_right': 0.9486461782710225,
                'svi_jw_min_imp_var': 0.06465875668645571
            }
        ),
        (
            # SVI - delta delta space
            {
                'strikes': [27753.557156146224, 22048.36928507438, 22972.064856606867, 25482.500421786215, 15885.535396700909, 28254.674667376898, 25072.4031931067, 33345.1332729847, 18588.64644197394, 14021.502951419781, 27289.26973495161, 17909.03749048186, 38308.83167939044, 48023.71722123032, 17051.749839113792, 37001.91616973172, 26900.74524788862, 30270.104584727364, 20757.51646171827, 29131.42035552005, 26176.041405349573, 42807.187630906636, 26555.029014091655, 25842.683986509477, 40093.241014294945, 26226.96129032268, 23981.774737923974, 24611.56937085511],
                'vols': [0.26470706810613037, 0.43292390132745806, 0.40762546783765863, 0.270307576510042, 0.6333381684179468, 0.2704870707330052, 0.28650010725427144, 0.441655831573966, 0.5418979285891272, 0.6990897372619976, 0.25474445600731294, 0.5645123869270274, 0.5111972001128641, 0.614241566485508, 0.5933941305086377, 0.4941426164976713, 0.24945284769360004, 0.3326520661602149, 0.4718596873099179, 0.3019602730927222, 0.2515585346243025, 0.5635594166251041, 0.24841669751631926, 0.2587863173703846, 0.5330834981618239, 0.25145008257418633, 0.33554330127733284, 0.30128212253161357],
                'forward': 26065.626576438615,
                'spot': 25866.99,
                'timestamp': 1694307600000000000,
                'qualified_name': 'bybit.option.BTC.SVI.60d.1m.smile',
                'vol_space': 'delta',
                'expiry': 0.164383561644
            },
            {
                'svi_a': 0.00742163588013167,
                'svi_b': 0.10457179521670218,
                'svi_rho': -0.08921844053207105,
                'svi_m': 0.021355910681988253,
                'svi_sigma': 0.019290366417480154,
                'atm_vol': 0.2542981739389972,
                'expiry': 0.164383561644,
                'forward': 26065.626576438615,
                'spot': 25866.99,
                'vol_space': 'delta',
                'strikes': [27753.557156146224, 22048.36928507438, 22972.064856606867, 25482.500421786215, 15885.535396700909, 28254.674667376898, 25072.4031931067, 33345.1332729847, 18588.64644197394, 14021.502951419781, 27289.26973495161, 17909.03749048186, 38308.83167939044, 48023.71722123032, 17051.749839113792, 37001.91616973172, 26900.74524788862, 30270.104584727364, 20757.51646171827, 29131.42035552005, 26176.041405349573, 42807.187630906636, 26555.029014091655, 25842.683986509477, 40093.241014294945, 26226.96129032268, 23981.774737923974, 24611.56937085511],
                'vols': [0.26470706810613037, 0.43292390132745806, 0.40762546783765863, 0.270307576510042, 0.6333381684179468, 0.2704870707330052, 0.28650010725427144, 0.441655831573966, 0.5418979285891272, 0.6990897372619976, 0.25474445600731294, 0.5645123869270274, 0.5111972001128641, 0.614241566485508, 0.5933941305086377, 0.4941426164976713, 0.24945284769360004, 0.3326520661602149, 0.4718596873099179, 0.3019602730927222, 0.2515585346243025, 0.5635594166251041, 0.24841669751631926, 0.2587863173703846, 0.5330834981618239, 0.25145008257418633, 0.33554330127733284, 0.30128212253161357],
                'svi_jw_atm_var': 0.06466756126870846,
                'svi_jw_atm_total_var': 0.010630284044181884,
                'svi_jw_atm_skew': -0.4215707619858746,
                'svi_jw_slope_left': 1.1047324590004852,
                'svi_jw_slope_right': 0.9237540555334328,
                'svi_jw_min_imp_var': 0.05737081916252513
            }
        ),
        (
            # SABR - delta vol space
            {
                'strikes': [26612.63197566624, 24470.620087431143, 24866.508222102886, 25655.95273225611, 19649.268025919977, 26899.472162760892, 25510.413330486557, 29142.207411253978, 22322.73428296898, 18115.110575189487, 26394.194747621477, 21746.422769168294, 29436.026589457095, 33907.283781325714, 20931.30882428828, 28977.165659056016, 26217.049545584432, 27945.915762237786, 23804.68480721348, 27305.9881733428, 25923.16101592121, 31247.96975412513, 26064.94765232648, 25790.73659115813, 30109.255991198836, 25926.908855435522, 25138.111778853512, 25343.25835498442],
                'vols': [0.2507666025579378, 0.28786539587987514, 0.2550661474332465, 0.20898168650507398, 0.7108974617266417, 0.2743278968732471, 0.21330831417639345, 0.4534363698570793, 0.47502693029130605, 0.8125347094535557, 0.23409977653045214, 0.52540606030224, 0.36529411070432555, 0.5768594503797863, 0.5969407327287821, 0.341049514905409, 0.22234883873902908, 0.36129948729630346, 0.3455121132721336, 0.30852383938580014, 0.2095723900622499, 0.4563233041332449, 0.21440670805129253, 0.2078249829091025, 0.40002627384879397, 0.2096625751163253, 0.23457019489921488, 0.2214328042060411],
                'forward': 25903.197286134615,
                'spot': 25866.99,
                'timestamp': 1694307600000000000,
                'qualified_name': 'bybit.option.BTC.SABR.14d.1m.smile',
                'vol_space': 'delta',
                'expiry': 0.038356164383561646
            },
            {
                'sabr_alpha': 0.19508468285554093,
                'sabr_rho': -0.13810840628558074,
                'sabr_volvol': 6.085421534107392,
                'atm_vol': 0.21720912588549174,
                'expiry': 0.038356164383561646,
                'forward': 25903.197286134615,
                'spot': 25866.99,
                'vol_space': 'delta',
                'strikes': [26612.63197566624, 24470.620087431143, 24866.508222102886, 25655.95273225611, 19649.268025919977, 26899.472162760892, 25510.413330486557, 29142.207411253978, 22322.73428296898, 18115.110575189487, 26394.194747621477, 21746.422769168294, 29436.026589457095, 33907.283781325714, 20931.30882428828, 28977.165659056016, 26217.049545584432, 27945.915762237786, 23804.68480721348, 27305.9881733428, 25923.16101592121, 31247.96975412513, 26064.94765232648, 25790.73659115813, 30109.255991198836, 25926.908855435522, 25138.111778853512, 25343.25835498442],
                'vols': [0.2507666025579378, 0.28786539587987514, 0.2550661474332465, 0.20898168650507398, 0.7108974617266417, 0.2743278968732471, 0.21330831417639345, 0.4534363698570793, 0.47502693029130605, 0.8125347094535557, 0.23409977653045214, 0.52540606030224, 0.36529411070432555, 0.5768594503797863, 0.5969407327287821, 0.341049514905409, 0.22234883873902908, 0.36129948729630346, 0.3455121132721336, 0.30852383938580014, 0.2095723900622499, 0.4563233041332449, 0.21440670805129253, 0.2078249829091025, 0.40002627384879397, 0.2096625751163253, 0.23457019489921488, 0.2214328042060411]
            }
        ),
        (
            # SABR - moneyness vol space
            {
                'strikes': [33184.83696705283, 60838.867772930185, 52542.65853116697, 74665.88317586886, 82962.09241763207, 47011.8523699915, 80196.68933704433, 55308.06161175471, 38715.643128228294, 13827.015402938678, 66369.67393410565, 41481.046208816035, 69135.0770146934, 19357.821564114147, 5530.806161175471, 27654.030805877355, 11061.612322350942, 24888.62772528962, 35950.24004764056, 16592.418483526413, 77431.28625645659, 49777.25545057924, 2765.4030805877355, 8296.209241763207, 71900.48009528112, 58073.46469234245, 22123.224644701884, 44246.44928940377, 30419.433886465093, 63604.27085351791, 25165.168033348393, 25441.708341407168, 25718.248649465942, 25994.788957524714, 26271.329265583485, 26547.86957364226, 26824.409881701034, 27100.95018975981, 27377.49049781858, 27930.57111393613, 28207.1114219949, 28483.651730053676, 28760.19203811245, 29036.732346171226, 29313.272654229997, 29589.81296228877, 29866.353270347547, 30142.893578406318],
                'vols': [0.41802421171827786, 0.6372771942861725, 0.5802786811532514, 0.715934255182324, 0.7557918935509794, 0.5370655132971782, 0.7430149418295946, 0.6002539464354, 0.46427629136313936, 0.6173458644112793, 0.6708725944913875, 0.48940049075163783, 0.6865480310394009, 0.4860807718494369, 0.9664656360748378, 0.3949991133608111, 0.7055174709477199, 0.40755822839060935, 0.4410250307328835, 0.5457829480939856, 0.7297415387231254, 0.5592338220624966, 1.2103609433072366, 0.8162256498146855, 0.7015516915800175, 0.6192290801374438, 0.4390849509680835, 0.5137606778464684, 0.40057017691390906, 0.6544694515884815, 0.40275311636613276, 0.40077272908938494, 0.3989938699943895, 0.3974180593789359, 0.3960460649054765, 0.3948778449924109, 0.3939125084291732, 0.39314829242714844, 0.39258256048208895, 0.39203176224419967, 0.3920373134859807, 0.39222271077627835, 0.3925815833636235, 0.39310704575938554, 0.3937917956029442, 0.3946282131989123, 0.39560845944783446, 0.39672456935235995],
                'forward': 27654.030805877355,
                'spot': 25866.99,
                'timestamp': 1694307600000000000,
                'qualified_name': 'bybit.option.BTC.SABR.547d.1m.moneyness',
                'vol_space': 'money',
                'expiry': 1.4986301369863013
            },
            {
                'sabr_alpha': 0.33032718246787196,
                'sabr_rho': -0.037507951255625824,
                'sabr_volvol': 1.2507386801516505,
                'atm_vol': 0.3928074677615421,
                'expiry': 1.4986301369863013,
                'forward': 27654.030805877355,
                'spot': 25866.99,
                'vol_space': 'money',
                'strikes': [33184.83696705283, 60838.867772930185, 52542.65853116697, 74665.88317586886, 82962.09241763207, 47011.8523699915, 80196.68933704433, 55308.06161175471, 38715.643128228294, 13827.015402938678, 66369.67393410565, 41481.046208816035, 69135.0770146934, 19357.821564114147, 5530.806161175471, 27654.030805877355, 11061.612322350942, 24888.62772528962, 35950.24004764056, 16592.418483526413, 77431.28625645659, 49777.25545057924, 2765.4030805877355, 8296.209241763207, 71900.48009528112, 58073.46469234245, 22123.224644701884, 44246.44928940377, 30419.433886465093, 63604.27085351791, 25165.168033348393, 25441.708341407168, 25718.248649465942, 25994.788957524714, 26271.329265583485, 26547.86957364226, 26824.409881701034, 27100.95018975981, 27377.49049781858, 27930.57111393613, 28207.1114219949, 28483.651730053676, 28760.19203811245, 29036.732346171226, 29313.272654229997, 29589.81296228877, 29866.353270347547, 30142.893578406318],
                'vols': [0.41802421171827786, 0.6372771942861725, 0.5802786811532514, 0.715934255182324, 0.7557918935509794, 0.5370655132971782, 0.7430149418295946, 0.6002539464354, 0.46427629136313936, 0.6173458644112793, 0.6708725944913875, 0.48940049075163783, 0.6865480310394009, 0.4860807718494369, 0.9664656360748378, 0.3949991133608111, 0.7055174709477199, 0.40755822839060935, 0.4410250307328835, 0.5457829480939856, 0.7297415387231254, 0.5592338220624966, 1.2103609433072366, 0.8162256498146855, 0.7015516915800175, 0.6192290801374438, 0.4390849509680835, 0.5137606778464684, 0.40057017691390906, 0.6544694515884815, 0.40275311636613276, 0.40077272908938494, 0.3989938699943895, 0.3974180593789359, 0.3960460649054765, 0.3948778449924109, 0.3939125084291732, 0.39314829242714844, 0.39258256048208895, 0.39203176224419967, 0.3920373134859807, 0.39222271077627835, 0.3925815833636235, 0.39310704575938554, 0.3937917956029442, 0.3946282131989123, 0.39560845944783446, 0.39672456935235995]
            }
        )
    ],
)
# fmt: on
def test_recalibrate_params_post_smoothing(
    test_case: dict[str, float | list[float]],
    expected_result: dict[str, float | list[float]],
) -> None:
    _, qn_tokens = utils_general.get_qfn_and_version(
        cast(str, test_case["qualified_name"])
    )

    test_input = pd.Series(test_case, name=cast(int, test_case["timestamp"]))
    result = recalibrate_params_post_smoothing(
        row=test_input, model=cast(Model, qn_tokens[3]), exchange=qn_tokens[0]
    )
    assert result is not None
    assert_series_equal(
        result,
        pd.Series(expected_result, name=cast(int, test_case["timestamp"])),
    )


@pytest.fixture
def add_spiked_params_flag_sample_data(
    request: pytest.FixtureRequest,
) -> dict[str, Any]:
    test_case_path = (
        pathlib.Path(request.node.fspath).parent
        / "input_data/add_spiked_params_flag"
    )

    recalibrated_params = pd.read_json(
        test_case_path / "recalibrated_params.json", dtype={"timestamp": int}
    )
    moneyness_vol_df = pd.read_json(
        test_case_path / "moneyness_vol_df.json", dtype={"timestamp": int}
    )
    delta_vol_df = pd.read_json(
        test_case_path / "delta_vol_df.json", dtype={"timestamp": int}
    )
    with open(test_case_path / "parameters.json") as f:
        params = json.load(f)

    smooth_config = construct_smoothing_config(
        exchange=params["exchange"], model=params["model"], freq=params["freq"]
    )

    return {
        "recalibrated_params": recalibrated_params,
        "relevant_moneys": params["relevant_moneys"],
        "relevant_deltas": params["relevant_deltas"],
        "smooth_config": smooth_config,
        "moneyness_vol_df": moneyness_vol_df,
        "delta_vol_df": delta_vol_df,
        "case_path": test_case_path,
    }


def test_add_spiked_params_flag(
    snapshot: Any,
    add_spiked_params_flag_sample_data: dict[str, Any],
) -> None:
    result = _add_spiked_params_flag(
        recalibrated_params=add_spiked_params_flag_sample_data[
            "recalibrated_params"
        ].set_index("timestamp"),
        relevant_moneys=add_spiked_params_flag_sample_data["relevant_moneys"],
        relevant_deltas=add_spiked_params_flag_sample_data["relevant_deltas"],
        smooth_config=add_spiked_params_flag_sample_data["smooth_config"],
        moneyness_vol_df=add_spiked_params_flag_sample_data[
            "moneyness_vol_df"
        ].set_index("timestamp"),
        delta_vol_df=add_spiked_params_flag_sample_data[
            "delta_vol_df"
        ].set_index("timestamp"),
    )
    snapshot.assert_match(
        json.dumps([result.iloc[0].to_dict()], indent=2),
        "result.json",
    )


# Test cases for calculate_frequencies
@pytest.mark.parametrize(
    "periods, interval, expected_freq, expected_historic_freq, expected_round_periods, expected_round_interval",
    [
        # all frequencies exists in SMOOTH_PARAMS
        (1, "hour", "1h", "1h", 1, "hour"),
        (1, "minute", "1m", "1m", 1, "minute"),
        (1, "hour", "1h", "1h", 1, "hour"),
        (
            20,
            "second",
            "20s",
            "1m",
            1,
            "minute",
        ),
    ],
)
def test_get_frequency_details(
    periods: int,
    interval: str,
    expected_freq: str,
    expected_historic_freq: str,
    expected_round_periods: int,
    expected_round_interval: str,
) -> None:
    freq, historic_freq, round_periods, round_interval = get_frequency_details(
        periods, interval
    )
    assert freq == expected_freq
    assert historic_freq == expected_historic_freq
    assert round_periods == expected_round_periods
    assert round_interval == expected_round_interval


@pytest.mark.parametrize(
    "_qn_prefix, _timestamp_iso, expected",
    [
        ("deribit.option.BTC.SVI.30d.1m", "2021-06-01T12:00:00Z", False),
        ("deribit.option.BTC.SVI.7d.1m", "2021-06-01T12:00:00Z", False),
        (
            "deribit.option.BTC.SVI.2021-06-01T00:00:00Z.1m",
            "2021-06-01T12:00:00Z",
            True,
        ),
    ],
)
def test_is_expired_option(
    _qn_prefix: str, _timestamp_iso: str, expected: bool
) -> None:
    assert _is_expired_option(_qn_prefix, _timestamp_iso) == expected


@pytest.mark.parametrize(
    "df_records, qualified_name_prefix, start, expected_result, log_message",
    [
        # fmt: off
        # Case 1: DataFrame has rows where LOOKBACK_COLUMN_NAME is False
        (
            [
                {LOOKBACK_COLUMN_NAME: True, "qualified_name": "deribit.option.BTC.SVI.30d.1m.smile"},
                {LOOKBACK_COLUMN_NAME: True, "qualified_name": "deribit.option.BTC.SVI.30d.1m.moneyness"},
                {LOOKBACK_COLUMN_NAME: True, "qualified_name": "deribit.option.BTC.SVI.30d.1m.params"},
                {LOOKBACK_COLUMN_NAME: False, "qualified_name": "deribit.option.BTC.SVI.30d.1m.smile"},
                {LOOKBACK_COLUMN_NAME: False, "qualified_name": "deribit.option.BTC.SVI.30d.1m.moneyness"},
                {LOOKBACK_COLUMN_NAME: False, "qualified_name": "deribit.option.BTC.SVI.30d.1m.params"}
            ],
            "deribit.option.BTC.SVI.30d.1m",
            "2024-01-01T00:00:00Z",
            True,
            None
        ),
        (
            [
                {LOOKBACK_COLUMN_NAME: True, "qualified_name": "deribit.option.BTC.SVI.2024-01-30T00:00:00Z.1m.smile"},
                {LOOKBACK_COLUMN_NAME: True, "qualified_name": "deribit.option.BTC.SVI.2024-01-30T00:00:00Z.1m.moneyness"},
                {LOOKBACK_COLUMN_NAME: True, "qualified_name": "deribit.option.BTC.SVI.2024-01-30T00:00:00Z.1m.params"},
                {LOOKBACK_COLUMN_NAME: True, "qualified_name": "deribit.option.BTC.SVI.2024-01-30T00:00:00Z.1m.smile"},
                {LOOKBACK_COLUMN_NAME: True, "qualified_name": "deribit.option.BTC.SVI.2024-01-30T00:00:00Z.1m.moneyness"},
                {LOOKBACK_COLUMN_NAME: True, "qualified_name": "deribit.option.BTC.SVI.2024-01-30T00:00:00Z.1m.params"}
            ],
            "deribit.option.BTC.SVI.2024-01-30T00:00:00Z.1m",
            "2024-01-02T00:00:00Z", # option is not expired, but missing non_lookback data
            False,
            "Encountered qualified name with no modelparameters present in interval. Likely removed from modelParamsCalc qualified_name_prefix='deribit.option.BTC.SVI.2024-01-30T00:00:00Z.1m'. Investigate"
        ),
        # Case 3: Option is expired
        (
            [
                {LOOKBACK_COLUMN_NAME: True, "qualified_name": "deribit.option.BTC.SVI.2024-01-01T00:00:00Z.1m.smile"},
                {LOOKBACK_COLUMN_NAME: True, "qualified_name": "deribit.option.BTC.SVI.2024-01-01T00:00:00Z.1m.moneyness"},
                {LOOKBACK_COLUMN_NAME: True, "qualified_name": "deribit.option.BTC.SVI.2024-01-01T00:00:00Z.1m.params"},
                {LOOKBACK_COLUMN_NAME: True, "qualified_name": "deribit.option.BTC.SVI.2024-01-01T00:00:00Z.1m.smile"},
                {LOOKBACK_COLUMN_NAME: True, "qualified_name": "deribit.option.BTC.SVI.2024-01-01T00:00:00Z.1m.moneyness"},
                {LOOKBACK_COLUMN_NAME: True, "qualified_name": "deribit.option.BTC.SVI.2024-01-01T00:00:00Z.1m.params"}
            ],
            "deribit.option.BTC.SVI.2024-01-01T00:00:00Z.1m",
            "2024-01-02T00:00:00Z",
            False,
            "Expired option encountered, skipping qualified_name_prefix='deribit.option.BTC.SVI.2024-01-01T00:00:00Z.1m', chunk_start=2024-01-02T00:00:00Z"
        ),
        # fmt: on
    ],
)
def test_df_has_valid_non_lookback_data(
    df_records: list[dict[str, Any]],
    qualified_name_prefix: str,
    start: str,
    log_message: str,
    expected_result: bool,
    log_capture: pytest.LogCaptureFixture,
) -> None:
    df = pd.DataFrame.from_records(df_records)
    result = _df_has_valid_non_lookback_data(df, qualified_name_prefix, start)
    assert result == expected_result

    if log_message:
        assert log_message in log_capture.text
    else:
        assert log_capture.text == ""
