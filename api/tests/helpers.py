import json
import pathlib
from typing import Any, Optional, cast

import numpy as np
import pandas as pd
import utils_general
from lambda_types import (
    CurrencyToTimestampTenorExpiryArbFields,
    ExchangeCurrencyToTimestampTenorExpiryArbFields,
    ExpiryArbFields,
    TenorExpiryArbFields,
    TimestampToTenorExpiryArbFields,
)
from stream_types import (
    TYPES,
    StreamCalcTypeToMessageType,
    SubscriberMessageValues,
)
from utils.common import set_qualified_name_timestamp_index
from utils.smoothing import construct_smoothing_config
from utils_calc import Model

from .test_types import SmoothZscoreTestType, TestMessageTypes


def _get_arb_output_for_flex(
    overrides: Optional[
        dict[str, list[float] | bool | float | int | str]
    ] = None,
) -> ExpiryArbFields:
    res = ExpiryArbFields(arbitrary_expiry=True)
    if overrides:
        res.update(overrides)
    return res


def sub_types_cast_helper(
    sub_type: TestMessageTypes, values: list[float | int]
) -> SubscriberMessageValues:
    values_casted = cast(
        SubscriberMessageValues,
        {sub_type_to_message_value_helper(sub_type): values},
    )

    return values_casted


def sub_type_to_message_value_helper(sub_type: TestMessageTypes) -> TYPES:
    mapping: StreamCalcTypeToMessageType = {
        "strike": "strikes",
        "smile": "deltas",
        "moneyness": "moneyness",
    }
    return mapping[sub_type]


class NumpyEncoder(json.JSONEncoder):
    """Custom encoder for handling NumPy and pandas data types."""

    def default(self, obj: Any) -> Any:
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        else:
            return super().default(obj)


def list_files(
    directory: pathlib.Path, pattern: str = "*.json"
) -> list[pathlib.Path]:
    """Utility function to list files in a directory matching a pattern."""
    return list(directory.glob(pattern))


def serialize_expiry_arb_fields(
    data: (
        dict[Any, Any]
        | ExchangeCurrencyToTimestampTenorExpiryArbFields
        | ExpiryArbFields
    ),
) -> str:
    def serialize(obj: Any) -> Any:
        if isinstance(
            obj,
            (
                ExchangeCurrencyToTimestampTenorExpiryArbFields,
                CurrencyToTimestampTenorExpiryArbFields,
                TimestampToTenorExpiryArbFields,
                TenorExpiryArbFields,
            ),
        ):
            return {k: serialize(v) for k, v in obj.data.items()}
        elif isinstance(obj, ExpiryArbFields):
            return {
                "strikes": list(obj.strikes),
                "deltas": list(obj.deltas),
                "moneyness": list(obj.moneyness),
                "listed_expiry": obj.listed_expiry,
                "arbitrary_expiry": obj.arbitrary_expiry,
                "standard_constant_maturity": obj.standard_constant_maturity,
                "arbitrary_constant_maturity": obj.arbitrary_constant_maturity,
            }
        elif isinstance(obj, set):
            return list(obj)
        elif isinstance(obj, dict):
            return {k: serialize(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [serialize(item) for item in obj]
        else:
            return obj

    return json.dumps(serialize(data), indent=2)


def set_snapshot_path_to_parent(snapshot: Any) -> Any:
    snapshot.snapshot_dir = pathlib.Path(str(snapshot.snapshot_dir.parent))
    return snapshot


def convert_df(df: pd.DataFrame) -> pd.DataFrame:
    """Convert DataFrame values to appropriate Python types.

    Args:
        df: Input DataFrame with potentially NumPy data types

    Returns:
        DataFrame with NumPy types converted to Python native types
    """
    # Using a more type-safe approach than deprecated applymap
    result = df.copy()
    for col in result.columns:
        result[col] = result[col].apply(
            lambda x: (
                int(x)
                if isinstance(x, np.integer)
                else float(x) if isinstance(x, np.floating) else x
            )
        )
    return result


def construct_smoothing_test_data_from_df(
    df: pd.DataFrame,
) -> SmoothZscoreTestType:
    """Construct test data for smoothing tests from a DataFrame.

    Args:
        df: Input DataFrame with qualified name information

    Returns:
        Dictionary containing test data and smoothing configuration
    """
    _, qn_tokens = utils_general.get_qfn_and_version(
        df.iloc[0]["qualified_name"]
    )
    e, m, freq = qn_tokens[0], cast(Model, qn_tokens[3]), qn_tokens[-2]
    smooth_config = construct_smoothing_config(exchange=e, model=m, freq=freq)

    return {
        "df": set_qualified_name_timestamp_index(df),
        "smooth_config": smooth_config,
    }


def compare_dataframes_row_by_row(
    expected_df: pd.DataFrame, actual_df: pd.DataFrame, rtol: float = 1e-3
) -> None:
    """Compare two dataframes row by row with tolerance for floating point values."""
    assert (
        expected_df.shape == actual_df.shape
    ), f"DataFrame shapes don't match: {expected_df.shape} vs {actual_df.shape}"

    # Ensure column order is the same
    assert list(expected_df.columns) == list(
        actual_df.columns
    ), "Column names don't match"

    for idx in range(len(expected_df)):
        expected_row = expected_df.iloc[idx]
        actual_row = actual_df.iloc[idx]

        for col_name in expected_df.columns:
            expected_val = expected_row[col_name]
            actual_val = actual_row[col_name]

            # Prepare row information for error messages
            row_info = (
                f"Row {idx} comparison failed:\n"
                f"Expected row: {expected_row.to_dict()}\n"
                f"Actual row: {actual_row.to_dict()}\n"
                f"Difference at column '{col_name}': "
            )

            # Handle NaN comparisons first
            if pd.isna(expected_val) or pd.isna(actual_val):
                # If both are NaN, they're considered equal
                if pd.isna(expected_val) and pd.isna(actual_val):
                    continue
                # If only one is NaN, they're not equal
                raise AssertionError(
                    f"{row_info}Expected "
                    f"{'NaN' if pd.isna(expected_val) else expected_val} but got "
                    f"{'NaN' if pd.isna(actual_val) else actual_val}"
                )

            # Now we know neither value is NaN
            if pd.api.types.is_numeric_dtype(
                type(expected_val)
            ) and pd.api.types.is_numeric_dtype(type(actual_val)):
                if not np.isclose(expected_val, actual_val, rtol=rtol):
                    rel_diff = (
                        abs(actual_val - expected_val) / abs(expected_val)
                        if expected_val != 0
                        else float("inf")
                    )
                    raise AssertionError(
                        f"{row_info}{expected_val} != {actual_val} "
                        f"(rtol: {rtol*100:.2f}%, diff: {rel_diff*100:.2f}%)"
                    )
            else:
                assert (
                    expected_val == actual_val
                ), f"{row_info}{expected_val} != {actual_val}"
