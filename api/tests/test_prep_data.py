import datetime
from collections import defaultdict
from datetime import timezone
from typing import Any, Optional, cast

import pytest
from lambda_types import (
    CalcType,
    CalcTypeInputs,
    ExpiryArbFields,
    OptionContractDetails,
    SubscribedArbitraryInfo,
    TenorMode,
    TimestampToExchangeStrikeExpiryMap,
)
from prep_data import (
    _get_exchange_strike_map,
    _prepare_calc_types,
    _should_include_instrument,
    get_calculated_data_from_raw_data,
    prep_data,
)

from tests.conftest import SampleData
from tests.helpers import serialize_expiry_arb_fields


def test_exchange_strike_map_structure(sample_data: SampleData) -> None:
    instruments, exchange_map, timestamps = sample_data
    result = _get_exchange_strike_map(
        instruments=instruments,
        exchange_map=exchange_map,
        timestamps=timestamps,
    )

    assert isinstance(result, dict), "Result should be a dictionary"
    for timestamp, exchange_items in result.items():
        assert isinstance(timestamp, int), "Timestamp should be a int"
        for exchange, assets in exchange_items.items():
            assert isinstance(
                assets, defaultdict
            ), f"Assets for {exchange} should be stored in a defaultdict"
            for baseAsset, expiries in assets.items():
                assert isinstance(
                    expiries, defaultdict
                ), f"Expiries for {baseAsset} should be stored in a defaultdict"
                for expiry, strikes in expiries.items():
                    assert isinstance(
                        strikes, set
                    ), f"Strikes for {expiry} should be stored in a set"


def test_exchange_strike_map_content(sample_data: SampleData) -> None:
    instruments, exchange_map, timestamps = sample_data
    included_tstamp = next(iter(timestamps))
    result = _get_exchange_strike_map(
        instruments=instruments,
        exchange_map=exchange_map,
        timestamps=timestamps,
    )

    assert "deribit" in result[included_tstamp]
    assert "v2lyra-supercomposite" in result[included_tstamp]
    assert "v2composite" in result[included_tstamp]
    assert (
        1 in result[included_tstamp]["deribit"]["BTC"]["2023-01-01T00:00:00Z"]
    )
    assert (
        2500
        in result[included_tstamp]["deribit"]["ETH"]["2023-01-01T00:00:00Z"]
    )
    assert (
        155
        in result[included_tstamp]["v2composite"]["BTC"]["2023-01-02T00:00:00Z"]
    )
    assert (
        155
        in result[included_tstamp]["v2lyra-supercomposite"]["BTC"][
            "2023-01-02T00:00:00Z"
        ]
    )

    assert (
        len(
            result[included_tstamp]["v2lyra-supercomposite"]["BTC"][
                "2023-01-02T00:00:00Z"
            ]
        )
        == 1
    )
    assert (
        len(
            result[included_tstamp]["v2composite"]["BTC"][
                "2023-01-02T00:00:00Z"
            ]
        )
        == 1
    )
    assert (
        len(result[included_tstamp]["deribit"]["BTC"]["2023-01-01T00:00:00Z"])
        == 2
    )


@pytest.mark.parametrize(
    "raw_params_data, lookback_data, instruments_exchange_map_fixture, freq,subscribed_info, include_listed_expiry, kwargs",
    [
        (
            [
                {
                    "timestamp": 1672444800000000000,  # "2022-12-31T00:00:00Z
                    "qualified_name": "deribit.option.BTC.SVI.1m.params",
                    "params": """[{"expiry": 0.0027397260273972603, "timestamp": 1672444800000000000, "underlying_index": "index1", "atm_vol": 0.5}, {"expiry": 0.2, "timestamp": 1672444800000000000, "underlying_index": "index2", "atm_vol": 0.52}, {"expiry": 0.3, "timestamp": 1672444800000000000, "underlying_index": "index3", "atm_vol": 0.525}, {"expiry": 0.4, "timestamp": 1672444800000000000, "underlying_index": "index3", "atm_vol": 0.58},{"expiry": 0.5, "timestamp": 1672444800000000000, "underlying_index": "index4", "atm_vol": null}]""",
                },
            ],
            [
                {
                    "timestamp": 1672444740000000000,  # 2022-12-30T23:59:00Z
                    "qualified_name": "deribit.option.BTC.SVI.1m.params",
                    "params": """
                        [
                            {"expiry": 0.0027397260273972603, "timestamp": 1672444740000000000, "atm_vol": 0.54},
                            {"expiry": 0.2, "timestamp": 1672444740000000000, "atm_vol": 0.51}
                        ]""",
                },
            ],
            "sample_data",
            "1m",
            None,
            True,
            {"start": "2022-12-30T23:59:00Z", "end": "2022-12-31T00:59:59Z"},
        ),
        (
            [
                {
                    "timestamp": 1672444800000000000,
                    "qualified_name": "deribit.option.BTC.SVI.1m.params",
                    "params": """[{"expiry": 0.0027397260273972603, "timestamp": 1672444800000000000, "underlying_index": "index1", "atm_vol": 0.5}, {"expiry": 0.2, "timestamp": 1672444800000000000, "underlying_index": "index2", "atm_vol": 0.53}, {"expiry": 0.3, "timestamp": 1672444800000000000,  "underlying_index": "index3", "atm_vol": 0.54}, {"expiry": 0.4, "timestamp": 1672444800000000000, "underlying_index": "index4", "atm_vol": 0.58}]""",
                }
            ],
            [
                {
                    "timestamp": 1672444740000000000,
                    "qualified_name": "deribit.option.BTC.SVI.1m.params",
                    "params": """
                    [
                        {"expiry": 0.0027397260273972603, "timestamp": 1672444740000000000, "underlying_index": "index1", "atm_vol": 0.5},
                        {"expiry": 0.2, "timestamp": 1672444740000000000, "underlying_index": "index2", "atm_vol": 0.4}
                    ]""",
                },
            ],
            "sample_data",
            "live",
            {
                "deribit": {
                    "BTC": {
                        "2023-01-06T12:00:00Z": ExpiryArbFields(
                            strikes=set(),
                            deltas=set(),
                            moneyness=set(),
                            listed_expiry=False,
                            arbitrary_expiry=True,
                        )
                    }
                }
            },
            True,
            {"snapshot_ts": 1672444800000000000},
        ),
        (
            [
                {
                    "timestamp": 1672444800000000000,
                    "qualified_name": "v2composite.option.BTC.SVI.1m.params",
                    "params": """[{"expiry": 0.0027397260273972603, "timestamp": 1672444800000000000, "underlying_index": "index1", "atm_vol": 0.5}, {"expiry": 0.2, "timestamp": 1672444800000000000, "underlying_index": "index2", "atm_vol": 0.53}, {"expiry": 0.3, "timestamp": 1672444800000000000,  "underlying_index": "index3", "atm_vol": 0.54}, {"expiry": 0.4, "timestamp": 1672444800000000000, "underlying_index": "index4", "atm_vol": 0.58}]""",
                }
            ],
            [
                {
                    "timestamp": 1672444740000000000,
                    "qualified_name": "v2composite.option.BTC.SVI.1m.params",
                    "params": """
                    [
                        {"expiry": 0.0027397260273972603, "timestamp": 1672444740000000000, "underlying_index": "index1", "atm_vol": 0.5},
                        {"expiry": 0.2, "timestamp": 1672444740000000000, "underlying_index": "index2", "atm_vol": 0.4}
                    ]""",
                },
            ],
            "sample_data",
            "live",
            {
                "v2composite": {
                    "BTC": {
                        "2023-01-06T12:00:00Z": ExpiryArbFields(
                            strikes=set(),
                            deltas=set(),
                            moneyness=set(),
                            listed_expiry=False,
                            arbitrary_expiry=True,
                        )
                    }
                }
            },
            True,
            {"snapshot_ts": 1672444800000000000},
        ),
        (
            [
                {
                    "timestamp": 1672444800000000000,
                    "qualified_name": "deribit.option.BTC.SVI.1m.params",
                    "params": """[{"expiry": 0.0027397260273972603, "timestamp": 1672444800000000000, "underlying_index": "index1", "atm_vol": 0.5}, {"expiry": 0.2, "timestamp": 1672444800000000000, "underlying_index": "index2", "atm_vol": 0.53}, {"expiry": 0.3, "timestamp": 1672444800000000000,"underlying_index": "index3", "atm_vol": 0.54}, {"expiry": 0.4, "timestamp": 1672444800000000000, "underlying_index": "index4", "atm_vol": 0.6}]""",
                }
            ],
            [
                {
                    "timestamp": 1672444740000000000,
                    "qualified_name": "deribit.option.BTC.SVI.1m.params",
                    "params": """
                    [
                        {"expiry": 0.0027397260273972603, "timestamp": 1672444740000000000, "atm_vol": 0.5},
                        {"expiry": 0.2, "timestamp": 1672444740000000000, "atm_vol": 0.54}
                    ]""",
                }
            ],
            "sample_data",
            "live",
            {
                "deribit": {
                    "BTC": {
                        "6d5d": ExpiryArbFields(
                            strikes={5000.0},
                            deltas=set(),
                            moneyness=set(),
                            listed_expiry=False,
                            arbitrary_expiry=False,
                        ),
                        "2023-01-06T12:00:00Z": ExpiryArbFields(
                            strikes={15000.0},
                            deltas=set(),
                            moneyness=set(),
                            listed_expiry=False,
                            arbitrary_expiry=True,
                        ),
                    },
                }
            },
            True,
            {"snapshot_ts": 1672444800000000000},
        ),
        (
            [
                {
                    "timestamp": 1672444800000000000,
                    "qualified_name": "v2composite.option.BTC.SVI.1m.params",
                    "params": """[{"expiry": 0.0027397260273972603, "timestamp": 1672444800000000000, "underlying_index": "index1", "atm_vol": 0.5}, {"expiry": 0.2, "timestamp": 1672444800000000000, "underlying_index": "index2", "atm_vol": 0.53}, {"expiry": 0.3, "timestamp": 1672444800000000000,"underlying_index": "index3", "atm_vol": 0.54}, {"expiry": 0.4, "timestamp": 1672444800000000000, "underlying_index": "index4", "atm_vol": 0.6}]""",
                }
            ],
            [
                {
                    "timestamp": 1672444740000000000,
                    "qualified_name": "v2composite.option.BTC.SVI.1m.params",
                    "params": """
            [
                {"expiry": 0.0027397260273972603, "timestamp": 1672444740000000000, "atm_vol": 0.5},
                {"expiry": 0.2, "timestamp": 1672444740000000000, "atm_vol": 0.54}
            ]""",
                }
            ],
            "sample_data",
            "live",
            {
                "v2composite": {
                    "BTC": {
                        "6d5d": ExpiryArbFields(
                            strikes={5000.0},
                            deltas=set(),
                            moneyness=set(),
                            listed_expiry=False,
                            arbitrary_expiry=False,
                        ),
                        "2023-01-06T12:00:00Z": ExpiryArbFields(
                            strikes={15000.0},
                            deltas=set(),
                            moneyness=set(),
                            listed_expiry=False,
                            arbitrary_expiry=True,
                        ),
                    },
                }
            },
            True,
            {"snapshot_ts": 1672444800000000000},
        ),
    ],
)
def test_prep_data(
    raw_params_data: Any,
    lookback_data: Any,
    instruments_exchange_map_fixture: str,
    include_listed_expiry: bool,
    subscribed_info: Optional[SubscribedArbitraryInfo],
    freq: str,
    request: pytest.FixtureRequest,
    kwargs: dict[str, Any],
    snapshot: Any,
) -> None:
    instruments, exchange_map, timeststamps = request.getfixturevalue(
        instruments_exchange_map_fixture
    )
    calculated_data = get_calculated_data_from_raw_data(
        raw_params_data=raw_params_data, lookback_data=lookback_data
    )
    snap = prep_data(
        calculated_data=calculated_data,
        calc_types=[
            "params",
            "smile",
            "moneyness",
            "butterfly",
            "skew",
            "strike",
        ],
        instruments=instruments,
        estimate_params=True,
        exchange_map=exchange_map,
        freq=freq,
        tenor_mode=cast(TenorMode, "standard"),
        include_listed_expiries=True,
        subscribed_arbitrary_info=subscribed_info,
        **kwargs,
    )

    expected_calc_type_inputs = {
        "params_calc_types": ["params"],
        "vol_surface_calc_types": [
            "smile",
            "moneyness",
            "strike",
        ],
        "smoothing_calc_types": ["params", "smile", "moneyness", "strike"],
        "aux_calc_types": ["skew", "butterfly"],
    }
    sorted_calc_type_inputs = {
        k: sorted(v) for k, v in snap.calc_type_inputs.items()  # type: ignore
    }
    sorted_expected_calc_type_inputs = {
        k: sorted(v) for k, v in expected_calc_type_inputs.items()
    }

    # test this as well before serialisation which may undo the sorting of the keys of TenorExpiryArbFields
    for (
        curr_timestamp_expiry_iv_info
    ) in snap.exchange_curr_timestamp_to_expiry_info.values():
        for (
            timestamp_tenor_expiry_arb_fields
        ) in curr_timestamp_expiry_iv_info.values():
            for (
                timestamp,
                tenor_expiry_arb_fields,
            ) in timestamp_tenor_expiry_arb_fields.items():
                assert list(tenor_expiry_arb_fields.data.keys()) == sorted(
                    tenor_expiry_arb_fields.data.keys()
                ), f"TenorExpiryArbFields for timestamp {timestamp} is not sorted"

    serialized_result = serialize_expiry_arb_fields(
        snap.exchange_curr_timestamp_to_expiry_info
    )
    assert sorted_calc_type_inputs == sorted_expected_calc_type_inputs
    snapshot.assert_match(serialized_result, "prep_data.json")


@pytest.mark.parametrize(
    "calc_types, timestamp_exchange_ccy_expiry_strike_map, subscribed_arbitrary_info, expected, should_fail",
    [
        (
            ["params", "smile", "moneyness", "skew"],
            {},
            None,
            {
                "params_calc_types": ["params"],
                "vol_surface_calc_types": ["smile", "moneyness"],
                "smoothing_calc_types": ["params", "smile", "moneyness"],
                "aux_calc_types": ["skew"],
            },
            False,
        ),
        (
            ["params", "moneyness", "butterfly"],
            {},
            None,
            {
                "params_calc_types": ["params"],
                "vol_surface_calc_types": ["moneyness"],
                "smoothing_calc_types": ["params", "moneyness"],
                "aux_calc_types": ["butterfly"],
            },
            True,
        ),
        (
            ["params", "smile", "strike"],
            {},
            None,
            {
                "params_calc_types": ["params"],
                "vol_surface_calc_types": [
                    "smile",
                ],
                "smoothing_calc_types": [
                    "params",
                    "smile",
                ],
                "aux_calc_types": [],
            },
            False,
        ),
        (
            ["params", "smile", "strike", "moneyness", "skew"],
            {
                1716274800000000000: {
                    "v2compsoite": {
                        "BTC": {
                            "2024-05-22T08:00:00Z": {80_000, 10_000, 200_000}
                        }
                    }
                }
            },
            None,
            {
                "params_calc_types": ["params"],
                "vol_surface_calc_types": ["smile", "strike", "moneyness"],
                "smoothing_calc_types": [
                    "params",
                    "strike",
                    "smile",
                    "moneyness",
                ],
                "aux_calc_types": ["skew"],
            },
            False,
        ),
        (
            ["params", "smile", "strike", "moneyness", "skew"],
            {},
            None,
            {
                "params_calc_types": ["params"],
                "vol_surface_calc_types": ["smile", "moneyness"],
                "smoothing_calc_types": ["params", "smile", "moneyness"],
                "aux_calc_types": ["skew"],
            },
            False,
        ),
        (
            ["params", "smile", "strike", "moneyness", "skew"],
            {},
            {
                "deribit": {
                    "BTC": {
                        "2024-05-22T08:00:00Z": ExpiryArbFields(
                            strikes={80_000, 10_000, 200_000},
                            deltas=set(),
                            moneyness=set(),
                            listed_expiry=False,
                            arbitrary_expiry=True,
                        )
                    }
                }
            },
            {
                "params_calc_types": ["params"],
                "vol_surface_calc_types": ["smile", "moneyness", "strike"],
                "smoothing_calc_types": [
                    "params",
                    "smile",
                    "moneyness",
                    "strike",
                ],
                "aux_calc_types": ["skew"],
            },
            False,
        ),
    ],
)
def test_prepare_calc_types(
    calc_types: list[CalcType],
    timestamp_exchange_ccy_expiry_strike_map: TimestampToExchangeStrikeExpiryMap,
    subscribed_arbitrary_info: Optional[SubscribedArbitraryInfo],
    expected: CalcTypeInputs,
    should_fail: bool,
) -> None:
    if should_fail:
        with pytest.raises(
            AssertionError,
            match="Smile calc type is required to calculate butterfly or skew",
        ):
            _prepare_calc_types(
                calc_types=calc_types,
                timestamp_exchange_ccy_expiry_strike_map=timestamp_exchange_ccy_expiry_strike_map,
                subscribed_arbitrary_info=subscribed_arbitrary_info,
            )
    else:
        result = _prepare_calc_types(
            calc_types=calc_types,
            timestamp_exchange_ccy_expiry_strike_map=timestamp_exchange_ccy_expiry_strike_map,
            subscribed_arbitrary_info=subscribed_arbitrary_info,
        )
        sorted_result = {k: sorted(v) for k, v in result.items()}  # type: ignore
        sorted_expected_result = {k: sorted(v) for k, v in expected.items()}  # type: ignore
        assert sorted_result == sorted_expected_result


@pytest.mark.parametrize(
    "instrument, current_ts, expected",
    [
        # Case 1: Instrument with a future listing date should be excluded.
        (
            {
                "listing": "2025-04-08T00:00:00",
                "expiry": "2025-04-07T00:00:00",
                "qualified_name": "EXCHANGE.asset",
                "baseAsset": "asset",
                "strike": 100,
            },
            int(
                datetime.datetime(
                    2025, 4, 7, 0, 0, 0, tzinfo=timezone.utc
                ).timestamp()
                * 1e9
            ),
            False,
        ),
        # Case 2: Past listing date but expiry is in the past (tenor < 0) should be excluded.
        (
            {
                "listing": "2025-03-05T00:00:00",
                "expiry": "2025-04-08T00:00:00",
                "qualified_name": "EXCHANGE.asset",
                "baseAsset": "asset",
                "strike": 100,
            },
            int(
                datetime.datetime(
                    2025, 4, 9, 0, 0, 0, tzinfo=timezone.utc
                ).timestamp()
                * 1e9
            ),
            False,
        ),
        # Case 3: Past listing date and expiry is in the future (tenor > 0) should be included.
        (
            {
                "listing": "2025-04-06T00:00:00",
                "expiry": "2025-04-07T00:00:00",
                "qualified_name": "EXCHANGE.asset",
                "baseAsset": "asset",
                "strike": 100,
            },
            int(
                datetime.datetime(
                    2025, 4, 6, 0, 0, 0, tzinfo=timezone.utc
                ).timestamp()
                * 1e9
            ),
            True,
        ),
        # Case 4: No listing (or availableSince) field provided, expiry in the future should be included.
        (
            {
                "expiry": "2025-04-08T00:00:00",
                "qualified_name": "EXCHANGE.asset",
                "baseAsset": "asset",
                "strike": 100,
            },
            int(
                datetime.datetime(
                    2025, 4, 7, 0, 0, 0, tzinfo=timezone.utc
                ).timestamp()
                * 1e9
            ),
            True,
        ),
        # Case 5: No listing field and expiry in the future should be included.
        (
            {
                "expiry": "2025-04-08T00:00:00",
                "qualified_name": "EXCHANGE.asset",
                "baseAsset": "asset",
                "strike": 100,
            },
            int(
                datetime.datetime(
                    2025, 4, 7, 0, 0, 0, tzinfo=timezone.utc
                ).timestamp()
                * 1e9
            ),
            True,
        ),
    ],
)
def test_should_include_instrument(
    monkeypatch: pytest.MonkeyPatch,
    instrument: dict[str, Any],
    current_ts: int,
    expected: bool,
) -> None:
    """
    Test the _should_include_instrument function with various instrument cases.
    """
    # Import the module where _should_include_instrument is defined.
    # Replace 'mymodule' with the appropriate module name.

    # Run the function and assert the expected outcome.
    result = _should_include_instrument(
        cast(OptionContractDetails, instrument), current_ts
    )
    assert result == expected
