import pandas as pd
from svi_params_surface_calc import handle_sparse_strikes


def test_handle_sparse_strikes() -> None:
    strikes = [
        10233.3093,
        20466.6186,
        30699.9279,
        40933.2372,
        51166.5465,
        61399.8558,
        71633.1651,
        81866.4744,
        92099.7837,
        93123.1147,
        94146.4456,
        95169.7765,
        96193.1075,
        97216.4384,
        98239.7693,
        99263.1002,
        100286.4312,
        101309.7621,
        102333.0930,
        103356.4240,
        104379.7549,
        105403.0858,
        106426.4168,
        107449.7477,
        108473.0786,
        109496.4096,
        110519.7405,
        111543.0714,
        112566.4023,
        122799.7116,
        133033.0210,
        143266.3303,
        153499.6396,
        163732.9489,
        173966.2582,
        184199.5675,
        194432.8768,
        204666.1861,
        214899.4954,
        225132.8047,
        235366.1140,
        245599.4233,
        255832.7326,
        266066.0419,
        276299.3512,
        286532.6605,
        296765.9698,
        306999.2791,
    ]

    vols = [
        3.1477,
        2.6367,
        2.2855,
        1.9991,
        1.7450,
        1.5058,
        1.2689,
        1.0202,
        0.7339,
        0.7011,
        0.6670,
        0.6316,
        0.5944,
        0.5552,
        0.5135,
        0.4686,
        0.4196,
        0.3652,
        0.3102,
        0.4174,
        0.5311,
        0.6241,
        0.7043,
        0.7757,
        0.8405,
        0.9000,
        0.9554,
        1.0073,
        1.0562,
        1.4410,
        1.7207,
        1.9441,
        2.1311,
        2.2923,
        2.4340,
        2.5604,
        2.6745,
        2.7784,
        2.8737,
        2.9618,
        3.0436,
        3.1199,
        3.1913,
        3.2585,
        3.3218,
        3.3818,
        3.4386,
        3.4926,
    ]
    forward = 102333.09304042587
    spot = 102000.0
    exp = 0.0416667
    rd = 0.01
    t = 0.0416667
    tenors_params_svi = pd.DataFrame(
        [
            {
                "svi_a": 1.430842038167946e-05,
                "svi_b": 0.0013400010239970716,
                "svi_rho": 0.44297702068221967,
                "svi_m": 0.0017477886273277565,
                "svi_sigma": 0.0020075042089140334,
                "expiry": 0.00017503805175,
            }
        ]
    )

    expected_added_strikes = [
        101514.42829610246,
        101719.09448218331,
        101923.76066826416,
        102128.42685434502,
        102537.75922650672,
        102742.42541258757,
        102947.09159866843,
        103151.75778474928,
    ]

    new_strikes, new_vols, _ = handle_sparse_strikes(
        strikes=strikes,
        vols=vols,
        forward=forward,
        spot=spot,
        exp=exp,
        rd=rd,
        t=t,
        tenors_params=tenors_params_svi,
    )

    for s in expected_added_strikes:
        assert (
            s in new_strikes
        ), f"Expected strike {s} not found in augmented list"
