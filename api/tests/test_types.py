from typing import Literal, NotRequired, TypedDict

import pandas as pd
from lambda_types import (
    DerivedCalcResult,
    ExchangeCurrencyToTimestampTenorExpiryArbFields,
    ListedTenorParamsSABR,
    ListedTenorParamsSVI,
    ParamsCalcResult,
    PreviousResults,
    SingleParams,
    SmoothConfig,
    SmoothingCalcType,
    SnapshotDates,
    TenorExpiryArbFields,
)
from stream_types import StreamCalcTypes, SubscriberMessageValues
from utils_calc import Model

TestMessageTypes = Literal["strike", "smile", "moneyness"]


class SubOverides(TypedDict, total=False):
    type: StreamCalcTypes
    command: str
    values: SubscriberMessageValues
    subscriber_id: str
    expiry: str
    exchange: str
    base_asset: str
    model: str


class EstimateNewParamsTestType(TypedDict):
    name: str
    qn_grouped_params_df: pd.DataFrame
    qn_grouped_moneyness_df: pd.DataFrame
    qn_grouped_smile_df: pd.DataFrame
    smoothing_config: SmoothConfig
    start: str


class ParamsCalcTestType(TypedDict):
    name: str
    snap: list[ListedTenorParamsSVI | ListedTenorParamsSABR]
    expiry_iv_info: TenorExpiryArbFields


class StrikesVolsExtrapolateTestType(TypedDict):
    expiry: float
    forward: float
    spot: float
    strikes_to_vol: dict[float, float]
    r_d: float


class CalcHelpersTestDataType(TypedDict):
    params: SingleParams
    spot: float
    forward: float
    expiry: float
    model: Model
    exchange: str
    min_delta: NotRequired[float]
    max_delta: NotRequired[float]
    timestamp: NotRequired[int]
    atm_vol: NotRequired[float]
    qualified_name: NotRequired[str]


class LookbackAndAppendInputData(TypedDict):
    previous_results: PreviousResults
    end_str: str
    interval: str
    periods: int
    freq: str
    lookback_window_size: int
    debug: bool
    consistent_read: bool
    exchange_curr_timestamp_to_expiry_info: (
        ExchangeCurrencyToTimestampTenorExpiryArbFields
    )
    include_listed_expiries: bool
    calc_types: list[SmoothingCalcType]
    scheduled_version: str


class GetColdDbTestData(TypedDict):
    snapshot_dates: SnapshotDates
    expected_qualified_name_prefixs: list[str]
    historic_qualified_names: list[str]
    test_qualified_names: list[str]


class SmileDerivedDataTestType(TypedDict):
    delta_vol_matrix_df: pd.DataFrame
    expiry_iv_info: TenorExpiryArbFields
    params_surface: pd.DataFrame


class HandleTenorIntersectionTestData(TypedDict):
    calculation_results: list[ParamsCalcResult | DerivedCalcResult]
    exchange_curr_timestamp_to_expiry_info: (
        ExchangeCurrencyToTimestampTenorExpiryArbFields
    )


class SmoothZscoreTestType(TypedDict):
    df: pd.DataFrame
    smooth_config: SmoothConfig
