import json
import pathlib
from typing import Any, cast
from unittest.mock import patch

import pandas as pd
import pytest
import utils_general
from lambda_types import VolSurfaceCalcType
from utils.surface import create_volatility_surfaces
from utils_calc import Model

from tests.helpers import NumpyEncoder, convert_df
from tests.test_types import SmileDerivedDataTestType


def test_create_volatility_surfaces(
    sample_smile_derived_data_values: SmileDerivedDataTestType,
    snapshot: Any,
    request: pytest.FixtureRequest,
) -> None:
    test_case_path = (
        pathlib.Path(request.node.fspath).parent
        / "input_data/smile_derived_data"
    )
    df_vol_matrix_pre = pd.read_json(
        test_case_path / "df_vol_matrix_pre.json"
    ).set_index("tenor_days")

    version, qn_tokens = utils_general.get_qfn_and_version(
        sample_smile_derived_data_values["params_surface"].iloc[0][
            "qualified_name"
        ]
    )

    result = create_volatility_surfaces(
        params_surface=sample_smile_derived_data_values["params_surface"],
        df_vol_matrix_pre=df_vol_matrix_pre,
        calc_types=["strike", "moneyness", "smile"],
        currency=qn_tokens[2],
        exchange=qn_tokens[0],
        timestamp=sample_smile_derived_data_values["params_surface"].iloc[0][
            "timestamp"
        ],
        model=cast(Model, qn_tokens[3]),
        expiry_iv_info=sample_smile_derived_data_values["expiry_iv_info"],
    )

    for key in result["dfs"]:
        key = cast(VolSurfaceCalcType, key)
        result["dfs"][key].drop(["runtime"], axis="columns", inplace=True)

    flattened_result = result.copy()
    for key in flattened_result["dfs"].keys():
        key = cast(VolSurfaceCalcType, key)
        # we convert to json so that we can use pytest-snapshot
        # we ignore the types as it expects a dataframe
        flattened_result["dfs"][key] = flattened_result["dfs"][key].round(8)
        flattened_result["dfs"][key] = (
            convert_df(flattened_result["dfs"][key])  # type: ignore
            .sort_index(axis=1)
            .to_dict(orient="records")
        )

    assert result["model"] == qn_tokens[3]
    assert result["exchange"] == qn_tokens[0]
    assert result["currency"] == qn_tokens[2]
    assert (
        result["timestamp"]
        == sample_smile_derived_data_values["params_surface"].iloc[0][
            "timestamp"
        ]
    )

    for key in result["dfs"]:
        key = cast(VolSurfaceCalcType, key)
        snapshot.assert_match(
            json.dumps(result["dfs"][key], indent=2, cls=NumpyEncoder),
            f"{key}_result.json",
        )


def test_create_volatility_surfaces_with_interest_rates(
    sample_smile_derived_data_values: SmileDerivedDataTestType,
    snapshot: Any,
    request: pytest.FixtureRequest,
) -> None:
    """
    option_delta: with all else constant (here parameters will be too), introducing interest rates
    will cause the delta for the same strike to be lesser,(the starred equation below)
    essentially causing the strike corresponding to that delta to be lower for call and higher for put.
    for example, a Call strike 'K' at 25 delta, after using interest rates will be at 20 delta
    so the strike for the 25 delta will be nearer to the money
    and consequently the vol levels lower, and thats what we observe here

    d_p = (math.log(f / K) + ((vol**2) / 2) * t) / (vol * math.sqrt(t))
    * r_f = r_d - (1 / t) * math.log(f / s)
    delta = phi * math.exp(-r_f * t) * norm.cdf(phi * d_p)

    with all else constant, vol levels at moneyness or strike will remain the same
    """
    with patch(
        "utils.surface.load_domestic_rates",
        return_value={
            "28": 0.05,
            "56": 0.05,
            "91": 0.05,
            "182": 0.05,
            "365": 0.05,
        },
    ):
        test_case_path = (
            pathlib.Path(request.node.fspath).parent
            / "input_data/smile_derived_data"
        )
        df_vol_matrix_pre = pd.read_json(
            test_case_path / "df_vol_matrix_pre.json"
        ).set_index("tenor_days")

        version, qn_tokens = utils_general.get_qfn_and_version(
            sample_smile_derived_data_values["params_surface"].iloc[0][
                "qualified_name"
            ]
        )

        result = create_volatility_surfaces(
            params_surface=sample_smile_derived_data_values["params_surface"],
            df_vol_matrix_pre=df_vol_matrix_pre,
            calc_types=["strike", "moneyness", "smile"],
            currency=qn_tokens[2],
            exchange=qn_tokens[0],
            timestamp=sample_smile_derived_data_values["params_surface"].iloc[
                0
            ]["timestamp"],
            model=cast(Model, qn_tokens[3]),
            expiry_iv_info=sample_smile_derived_data_values["expiry_iv_info"],
        )

        for key in result["dfs"]:
            key = cast(VolSurfaceCalcType, key)
            result["dfs"][key].drop(["runtime"], axis="columns", inplace=True)

        flattened_result = result.copy()
        for key in flattened_result["dfs"].keys():
            key = cast(VolSurfaceCalcType, key)
            # we convert to json so that we can use pytest-snapshot
            # we ignore the types as it expects a dataframe
            flattened_result["dfs"][key] = flattened_result["dfs"][key].round(8)
            flattened_result["dfs"][key] = (
                convert_df(flattened_result["dfs"][key])  # type: ignore
                .sort_index(axis=1)
                .to_dict(orient="records")
            )

        assert result["model"] == qn_tokens[3]
        assert result["exchange"] == qn_tokens[0]
        assert result["currency"] == qn_tokens[2]
        assert (
            result["timestamp"]
            == sample_smile_derived_data_values["params_surface"].iloc[0][
                "timestamp"
            ]
        )

        for key in result["dfs"]:
            key = cast(VolSurfaceCalcType, key)
            snapshot.assert_match(
                json.dumps(result["dfs"][key], indent=2, cls=NumpyEncoder),
                f"{key}_result_with_rd.json",
            )
