from typing import Generator
from unittest.mock import patch

import numpy as np
import pandas as pd
import pytest
from constants import EXPIRY_PRECISION
from utils.calibrate import calibrate_sabr_with_recalibrations
from utils.common import round_value
from utils_calc import BOUNDS_SABR, CalibrationQuery

from .helpers import compare_dataframes_row_by_row


class TestSabrCalibrations:
    """
    tested with staging data
    "exchanges": ["deribit"],
    "currencies": ["BTC"],
    "models": ["SABR"],
    "types": ["params", "smile"],
    "start": "2024-02-26T03:00:00.000000Z",
    "end": "2024-02-26T03:01:00.000000Z"
    """

    # fmt: off
    @pytest.fixture(autouse=True)
    def setup(self) -> Generator[None, None, None]:
        self.tenor = 547
        self.query: CalibrationQuery = {
                'expiry': round_value(self.tenor / 365, EXPIRY_PRECISION),
                'forward': 60213.291548777845,
                'spot': 51435.46050549999,
                'domestic_rate': 0,
                'test_type':'delta',
                'vol_test_type': 'vol_lognormal',
                'model': 'SABR',
                'LNvols': [0.8951196238325869, 0.7708381146939738, 0.6995611525428448, 0.6519362849847138, 0.6187979744997523, 0.5960759870189362, 0.5814709267680457, 0.5733434022026765, 0.5703090404388818, 0.5711375434275572, 0.5747699781545699, 0.5803480171859439, 0.5872147732122995, 0.5948895346835065, 0.6030315628630591, 0.6114046749065168, 0.6198478958122229, 0.6282531697881686, 0.6365492259804083, 0.6446901996733156, 0.6526477126531558, 0.6604053918803404, 0.6679550801315927, 0.6752942130113226, 0.6824239990311768, 0.6893481536877006, 0.6960720171292395, 0.7026019386304854, 0.7089448475384537, 0.7151079551461421],
                'strikes': [6021.329154877785, 12042.65830975557, 18063.987464633352, 24085.31661951114, 30106.645774388922, 36127.974929266704, 42149.30408414449, 48170.63323902228, 54191.96239390006, 60213.291548777845, 66234.62070365563, 72255.94985853341, 78277.2790134112, 84298.60816828898, 90319.93732316677, 96341.26647804456, 102362.59563292233, 108383.92478780012, 114405.2539426779, 120426.58309755569, 126447.91225243348, 132469.24140731126, 138490.57056218904, 144511.89971706682, 150533.22887194462, 156554.5580268224, 162575.88718170018, 168597.21633657796, 174618.54549145573, 180639.87464633354],
                'biv': [0.8951196238325869, 0.7708381146939738, 0.6995611525428448, 0.6519362849847138, 0.6187979744997523, 0.5960759870189362, 0.5814709267680457, 0.5733434022026765, 0.5703090404388818, 0.5711375434275572, 0.5747699781545699, 0.5803480171859439, 0.5872147732122995, 0.5948895346835065, 0.6030315628630591, 0.6114046749065168, 0.6198478958122229, 0.6282531697881686, 0.6365492259804083, 0.6446901996733156, 0.6526477126531558, 0.6604053918803404, 0.6679550801315927, 0.6752942130113226, 0.6824239990311768, 0.6893481536877006, 0.6960720171292395, 0.7026019386304854, 0.7089448475384537, 0.7151079551461421],
                'aiv': [0.8951196238325869, 0.7708381146939738, 0.6995611525428448, 0.6519362849847138, 0.6187979744997523, 0.5960759870189362, 0.5814709267680457, 0.5733434022026765, 0.5703090404388818, 0.5711375434275572, 0.5747699781545699, 0.5803480171859439, 0.5872147732122995, 0.5948895346835065, 0.6030315628630591, 0.6114046749065168, 0.6198478958122229, 0.6282531697881686, 0.6365492259804083, 0.6446901996733156, 0.6526477126531558, 0.6604053918803404, 0.6679550801315927, 0.6752942130113226, 0.6824239990311768, 0.6893481536877006, 0.6960720171292395, 0.7026019386304854, 0.7089448475384537, 0.7151079551461421]
            }

        self.bounds_SABR = BOUNDS_SABR
        self.x0_SABR = np.array([0.5711375434275574, 0.06586160670775827, 0.7210599515911369])
        yield
        del self.query
        del self.bounds_SABR
        del self.x0_SABR
    # fmt: on

    def test_calibrate_sabr_with_recalibrations_long_expiry(self) -> None:
        expected_params = {
            "expiry": 1.498630136986,
            "sabr_alpha": 0.5374043631849984,
            "sabr_rho": 0.06810694442250179,
            "sabr_volvol": 0.6660389893841939,
        }
        with patch("logging.error") as mock_log:
            test_result = calibrate_sabr_with_recalibrations(
                query=self.query,
                x0_SABR=self.x0_SABR,
                bounds_SABR=self.bounds_SABR,
            )

        mock_log.assert_not_called()
        compare_dataframes_row_by_row(
            pd.DataFrame([expected_params]),
            pd.DataFrame([test_result]),
        )
