from typing import Generator

import numpy as np
import pandas as pd
import pytest
from calc_helpers import DELTAS_STR_OUTPUT, MONEYNESS_STR_OUTPUT
from utils.surface import (
    calculate_delta_from_params,
    calculate_moneyness_from_params,
)
from utils_calc import DEFAULT_SURFACE_DELTAS as DELTAS
from utils_calc import DEFAULT_SURFACE_MONEYNESS as MONEYNESS


class TestCalculateMoniesAndDeltasFromParams:
    @pytest.fixture(autouse=True)
    def setup(self) -> Generator[None, None, None]:
        self.sabr_data = pd.Series(
            {
                "forward": 35466.89779583021,
                "smoothed": False,
                "sabr_volvol": 2.946231046347318,
                "tenor_days": 30.0,
                "runtime": "2023-11-06T00:06:44.000Z",
                "spot": 35037.25012110256,
                "timestamp": 1699228800000000000,
                "qualified_name": "bybit.option.BTC.SABR.30d.1h.params",
                "expiry": 0.0821917808219178,
                "sabr_alpha": 0.486019269583719,
                "sabr_rho": -0.0537062252126497,
                "svi_rho": np.nan,
                "svi_jw_atm_total_var": np.nan,
                "svi_a": np.nan,
                "svi_b": np.nan,
                "svi_jw_min_imp_var": np.nan,
                "svi_sigma": np.nan,
                "svi_jw_atm_var": np.nan,
                "svi_jw_slope_right": np.nan,
                "svi_jw_slope_left": np.nan,
                "atm_vol": np.nan,
                "svi_m": np.nan,
                "svi_jw_atm_skew": np.nan,
                "spline_info": np.nan,
                "from_lookback": True,
                "R2_calib": np.nan,
                "duplicate_listed_exp": np.nan,
                "listed_expiry": np.nan,
                "isodate": np.nan,
                "api_version": np.nan,
            }
        )
        self.svi_data = pd.Series(
            {
                "forward": 35466.89779583021,
                "smoothed": False,
                "sabr_volvol": np.nan,
                "tenor_days": 30.0,
                "runtime": "2023-11-06T00:06:52.000Z",
                "spot": 35037.25012110256,
                "timestamp": 1699228800000000000,
                "qualified_name": "bybit.option.BTC.SVI.30d.1h.params",
                "expiry": 0.082191780822,
                "sabr_alpha": np.nan,
                "sabr_rho": np.nan,
                "svi_rho": -0.4150868339393854,
                "svi_jw_atm_total_var": 0.0233137354489328,
                "svi_a": 0.0164417863566831,
                "svi_b": 0.0862223505664132,
                "svi_jw_min_imp_var": 0.2642449161804857,
                "svi_sigma": 0.0672709935951311,
                "svi_jw_atm_var": 0.283650447961733,
                "svi_jw_slope_right": 0.3302975280611959,
                "svi_jw_slope_left": 0.7990924300611252,
                "atm_vol": 0.5325884414458626,
                "svi_m": -0.1016501314424345,
                "svi_jw_atm_skew": 0.118257248271386,
                "spline_info": np.nan,
                "from_lookback": True,
                "R2_calib": np.nan,
                "duplicate_listed_exp": np.nan,
                "listed_expiry": np.nan,
                "isodate": np.nan,
                "api_version": np.nan,
            }
        )
        self.spline_data = pd.Series(
            {
                "forward": 96296.5415337047,
                "smoothed": False,
                "sabr_volvol": np.nan,
                "tenor_days": 30.0,
                "runtime": "2024-12-26T10:00:00.000Z",
                "spot": 95340.2509652,
                "timestamp": 1735207200000000000,
                "qualified_name": "deribit.option.BTC.spline.30d.1h.params",
                "expiry": 0.0821917808219178,
                "sabr_alpha": np.nan,
                "sabr_rho": np.nan,
                "svi_rho": np.nan,
                "svi_jw_atm_total_var": np.nan,
                "svi_a": np.nan,
                "svi_b": np.nan,
                "svi_jw_min_imp_var": np.nan,
                "svi_sigma": np.nan,
                "svi_jw_atm_var": np.nan,
                "svi_jw_slope_right": np.nan,
                "svi_jw_slope_left": np.nan,
                "atm_vol": np.nan,
                "svi_m": np.nan,
                "svi_jw_atm_skew": np.nan,
                "spline_info": [
                    {
                        "x_left": 0.0,
                        "x_right": 47100.69510818854,
                        "constant_coef": 2.3807658694131044,
                        "linear_coef": -1.3468478307591676,
                        "square_coef": 0.0,
                        "cube_coef": 0.0,
                    },
                    {
                        "x_left": 47100.69510818854,
                        "x_right": 55593.44213303105,
                        "constant_coef": 1.0339180386539373,
                        "linear_coef": -0.2428507239929653,
                        "square_coef": 0.0,
                        "cube_coef": 0.018208470122202548,
                    },
                    {
                        "x_left": 55593.44213303105,
                        "x_right": 60157.77805007913,
                        "constant_coef": 0.8092757847831745,
                        "linear_coef": -0.10115967860214872,
                        "square_coef": 0.01577805753553817,
                        "cube_coef": -0.005661720840422144,
                    },
                    {
                        "x_left": 60157.77805007913,
                        "x_right": 63972.248398328506,
                        "constant_coef": 0.7182324428761417,
                        "linear_coef": -0.07236323838166267,
                        "square_coef": -0.01186268490005909,
                        "cube_coef": 0.0029998817759270004,
                    },
                    {
                        "x_left": 63972.248398328506,
                        "x_right": 67065.61997408152,
                        "constant_coef": 0.6370064013703471,
                        "linear_coef": -0.07062540737222188,
                        "square_coef": 0.0059186286088307805,
                        "cube_coef": 0.0009181460902823449,
                    },
                    {
                        "x_left": 67065.61997408152,
                        "x_right": 75414.26964845543,
                        "constant_coef": 0.5732177686972383,
                        "linear_coef": -0.1512284635117096,
                        "square_coef": 0.02006325245901723,
                        "cube_coef": 0.002536506641372146,
                    },
                    {
                        "x_left": 75414.26964845543,
                        "x_right": 80125.47665110888,
                        "constant_coef": 0.44458906428591816,
                        "linear_coef": -0.05840157639843378,
                        "square_coef": 0.0024231994234334307,
                        "cube_coef": 0.0016535511208128346,
                    },
                    {
                        "x_left": 80125.47665110888,
                        "x_right": 83554.79428169767,
                        "constant_coef": 0.3902642384317305,
                        "linear_coef": -0.035372264147593806,
                        "square_coef": 0.002628390758546751,
                        "cube_coef": 0.0014198107766751537,
                    },
                    {
                        "x_left": 83554.79428169767,
                        "x_right": 86368.79834397865,
                        "constant_coef": 0.3589401758193589,
                        "linear_coef": -0.02121676625433652,
                        "square_coef": 0.00286804352985574,
                        "cube_coef": 0.0010668826125972096,
                    },
                    {
                        "x_left": 86368.79834397865,
                        "x_right": 88834.10330828455,
                        "constant_coef": 0.34165833570747517,
                        "linear_coef": -0.010758343483446876,
                        "square_coef": 0.0024565734423175655,
                        "cube_coef": -0.0005837495214536676,
                    },
                    {
                        "x_left": 88834.10330828455,
                        "x_right": 91126.97702723657,
                        "constant_coef": 0.332772816144892,
                        "linear_coef": -0.007065125704236275,
                        "square_coef": -0.0015148400009600545,
                        "cube_coef": 0.0011352885098375699,
                    },
                    {
                        "x_left": 91126.97702723657,
                        "x_right": 93302.06842983622,
                        "constant_coef": 0.3253281389495335,
                        "linear_coef": -0.006345336923906479,
                        "square_coef": 0.003064941910705314,
                        "cube_coef": -0.0003831183133799747,
                    },
                    {
                        "x_left": 93302.06842983622,
                        "x_right": 95446.73300737947,
                        "constant_coef": 0.3216646256229523,
                        "linear_coef": -0.0013457160745928682,
                        "square_coef": -0.0011174237578421547,
                        "cube_coef": 0.000813748124057499,
                    },
                    {
                        "x_left": 95446.73300737947,
                        "x_right": 97599.39004476496,
                        "constant_coef": 0.3200152339145748,
                        "linear_coef": -0.0011435650862901036,
                        "square_coef": 0.0024594737067302973,
                        "cube_coef": -0.00015557585310756164,
                    },
                    {
                        "x_left": 97599.39004476496,
                        "x_right": 98124.00145707926,
                        "constant_coef": 0.32117556668190733,
                        "linear_coef": 0.0008063328344812443,
                        "square_coef": -2.7719744754166486e-05,
                        "cube_coef": -2.332845948065537e-05,
                    },
                    {
                        "x_left": 98124.00145707926,
                        "x_right": 100363.02463152006,
                        "constant_coef": 0.3219308513121536,
                        "linear_coef": 0.0029060914058247887,
                        "square_coef": -0.0012748204630022514,
                        "cube_coef": 0.000779688981211232,
                    },
                    {
                        "x_left": 100363.02463152006,
                        "x_right": 102713.11041153174,
                        "constant_coef": 0.32434181123618794,
                        "linear_coef": 0.0028292235823842993,
                        "square_coef": 0.002576872337313658,
                        "cube_coef": -0.0002890209631840129,
                    },
                    {
                        "x_left": 102713.11041153174,
                        "x_right": 105292.50478155485,
                        "constant_coef": 0.3294588861927016,
                        "linear_coef": 0.007810236714998215,
                        "square_coef": -0.0010445245803001998,
                        "cube_coef": -4.059802709924274e-05,
                    },
                    {
                        "x_left": 105292.50478155485,
                        "x_right": 108180.31207253839,
                        "constant_coef": 0.33618400030030005,
                        "linear_coef": 0.006268901523794241,
                        "square_coef": -0.00015266064957998254,
                        "cube_coef": 0.000837309389937717,
                    },
                    {
                        "x_left": 108180.31207253839,
                        "x_right": 111479.87079215859,
                        "constant_coef": 0.343137550564452,
                        "linear_coef": 0.009683969464800594,
                        "square_coef": 0.0032793103697723474,
                        "cube_coef": -0.0004905577136968235,
                    },
                    {
                        "x_left": 111479.87079215859,
                        "x_right": 115400.16608514886,
                        "constant_coef": 0.3556102726853282,
                        "linear_coef": 0.01754972757171994,
                        "square_coef": -0.002077481681023386,
                        "cube_coef": 0.00018350893838413998,
                    },
                    {
                        "x_left": 115400.16608514886,
                        "x_right": 120394.65568457168,
                        "constant_coef": 0.37126602751440857,
                        "linear_coef": 0.0177664195625655,
                        "square_coef": 0.0008935588106747668,
                        "cube_coef": 0.0010826301816266527,
                    },
                    {
                        "x_left": 120394.65568457168,
                        "x_right": 127890.12695154373,
                        "constant_coef": 0.39100863606927583,
                        "linear_coef": 0.03421920157905081,
                        "square_coef": 0.007315045741178566,
                        "cube_coef": -0.0016543792824051725,
                    },
                    {
                        "x_left": 127890.12695154373,
                        "x_right": 141573.78602805943,
                        "constant_coef": 0.43088850410710056,
                        "linear_coef": 0.08011813597047591,
                        "square_coef": -0.016541044914068898,
                        "cube_coef": 0.006798884682974894,
                    },
                    {
                        "x_left": 141573.78602805943,
                        "x_right": 146677.5031201331,
                        "constant_coef": 0.5012644798464826,
                        "linear_coef": 0.02515097917935416,
                        "square_coef": 0.0028374461262897378,
                        "cube_coef": -0.0001963530905098765,
                    },
                    {
                        "x_left": 146677.5031201331,
                        "x_right": 153749.61682586998,
                        "constant_coef": 0.5290565520616163,
                        "linear_coef": 0.04189851628521617,
                        "square_coef": -0.0011310564479831808,
                        "cube_coef": -0.00016678197604308887,
                    },
                    {
                        "x_left": 153749.61682586998,
                        "x_right": 164857.08478917566,
                        "constant_coef": 0.5696572299228062,
                        "linear_coef": 0.061467126031480086,
                        "square_coef": -0.0012342468828830534,
                        "cube_coef": -0.00038786667694249647,
                    },
                    {
                        "x_left": 164857.08478917566,
                        "x_right": 187144.13175269405,
                        "constant_coef": 0.6295022423944612,
                        "linear_coef": 0.11604553655376072,
                        "square_coef": -0.0046846660996622555,
                        "cube_coef": -0.0015373886530064532,
                    },
                    {
                        "x_left": 187144.13175269405,
                        "x_right": 209431.17871621245,
                        "constant_coef": 0.7393257241955538,
                        "linear_coef": 0.10206403839541686,
                        "square_coef": -0.004612165959019783,
                        "cube_coef": 1.3887564425896857e-16,
                    },
                    {
                        "x_left": 209431.17871621245,
                        "x_right": 418862.3574324249,
                        "constant_coef": 0.8367775966319504,
                        "linear_coef": 0.8724138819759957,
                        "square_coef": 0.0,
                        "cube_coef": 0.0,
                    },
                ],
                "from_lookback": True,
                "R2_calib": np.nan,
                "duplicate_listed_exp": np.nan,
                "listed_expiry": np.nan,
                "isodate": np.nan,
                "api_version": np.nan,
            }
        )
        self.relevant_monies = MONEYNESS
        self.relevant_deltas = DELTAS
        yield
        del self.sabr_data
        del self.svi_data
        del self.relevant_monies
        del self.relevant_deltas

    def test_calculate_moneyness_from_params_SVI(self) -> None:
        expected_result = pd.Series(
            {
                "10money": 1.8623548129572653,
                "20money": 1.5620169933180958,
                "30money": 1.3559387136559091,
                "40money": 1.1883891542401925,
                "50money": 1.0402440854785695,
                "60money": 0.9017855857468915,
                "70money": 0.7666630137415601,
                "80money": 0.631371094926335,
                "90money": 0.5218568015212156,
                "100money": 0.5325884414458625,
                "110money": 0.5767278142973499,
                "120money": 0.6184904516615146,
                "130money": 0.6556414499736127,
                "140money": 0.6886641444402061,
                "150money": 0.7182382251899301,
                "160money": 0.7449459397340499,
                "170money": 0.7692522931252699,
                "180money": 0.7915254016951836,
                "190money": 0.81205862007007,
                "200money": 0.8310882815358864,
                "210money": 0.8488070142931606,
                "220money": 0.8653736020775689,
                "230money": 0.8809203096644828,
                "240money": 0.8955583759887387,
                "250money": 0.9093821804531975,
                "260money": 0.9224724405192946,
                "270money": 0.9348986943533312,
                "280money": 0.946721249684366,
                "290money": 0.9579927295025541,
                "300money": 0.9687593098472339,
                "vol_space": "money",
            }
        )

        test_result = calculate_moneyness_from_params(
            self.svi_data, "SVI", self.relevant_monies
        )
        assert isinstance(test_result, pd.Series)
        assert test_result.index.tolist() == [
            *MONEYNESS_STR_OUTPUT,
            "vol_space",
        ]
        for key in expected_result.index:
            assert expected_result[key] == pytest.approx(
                test_result[key]
            ), f"Mismatch in value for {key}"

    def test_calculate_moneyness_from_params_SABR(self) -> None:
        expected_result = pd.Series(
            {
                "10money": 2.186105841681892,
                "20money": 1.7134558745516082,
                "30money": 1.42082646506169,
                "40money": 1.2032300442759145,
                "50money": 1.0272481534950355,
                "60money": 0.8781653010787485,
                "70money": 0.7490376294105568,
                "80money": 0.6384202568268414,
                "90money": 0.5531691688798014,
                "100money": 0.514022008681851,
                "110money": 0.5331320937755928,
                "120money": 0.5843897082711057,
                "130money": 0.6437497319680461,
                "140money": 0.7025232250887328,
                "150money": 0.7582898024554563,
                "160money": 0.8105566603552016,
                "170money": 0.8594208507363822,
                "180money": 0.9051507938058898,
                "190money": 0.9480474166680647,
                "200money": 0.9883974772774566,
                "210money": 1.0264594383920318,
                "220money": 1.0624612202156212,
                "230money": 1.0966021795191245,
                "240money": 1.129056369452269,
                "250money": 1.1599759414248425,
                "260money": 1.189494272836537,
                "270money": 1.2177286980764523,
                "280money": 1.2447828375815861,
                "290money": 1.2707485629953035,
                "300money": 1.2957076485241077,
                "vol_space": "money",
            }
        )

        test_result = calculate_moneyness_from_params(
            self.sabr_data, "SABR", self.relevant_monies
        )
        assert isinstance(test_result, pd.Series)
        assert test_result.index.tolist() == [
            *MONEYNESS_STR_OUTPUT,
            "vol_space",
        ]
        for key in expected_result.index:
            assert expected_result[key] == pytest.approx(
                test_result[key]
            ), f"Mismatch in value for {key}"

    def test_calculate_moneyness_from_params_spline(self) -> None:
        expected_result = pd.Series(
            {
                "10money": 1.45100144820897,
                "20money": 1.35279138672448,
                "30money": 1.24686962802346,
                "40money": 1.13107170532146,
                "50money": 1.00199636178297,
                "60money": 0.873987608155515,
                "70money": 0.753032080457295,
                "80money": 0.65178637299722,
                "90money": 0.583431562831748,
                "100money": 0.565630189574002,
                "110money": 0.581000106493183,
                "120money": 0.609771045092675,
                "130money": 0.644543886201793,
                "140money": 0.684163368834591,
                "150money": 0.718523324307921,
                "160money": 0.755945656465216,
                "170money": 0.789603392268923,
                "180money": 0.820290748409696,
                "190money": 0.848447471667959,
                "200money": 0.874073010488343,
                "210money": 0.897923925286223,
                "220money": 0.920251859663071,
                "230money": 0.94179462259442,
                "240money": 0.962855512048507,
                "250money": 0.98346548643896,
                "260money": 1.00365232473752,
                "270money": 1.02344106566302,
                "280money": 1.04285437181799,
                "290money": 1.06191283387777,
                "300money": 1.08063522647041,
                "vol_space": "money",
            }
        )

        test_result = calculate_moneyness_from_params(
            self.spline_data, "spline", self.relevant_monies
        )
        assert isinstance(test_result, pd.Series)
        assert test_result.index.tolist() == [
            *MONEYNESS_STR_OUTPUT,
            "vol_space",
        ]
        for key in expected_result.index:
            assert expected_result[key] == pytest.approx(
                test_result[key]
            ), f"Mismatch in value for {key}"

    def test_calculate_delta_from_params_SVI(self) -> None:
        expected_result = pd.Series(
            {
                "atm": 0.5325884414458625,
                "-1delta": 0.9862970720595216,
                "-2delta": 0.8846338018993534,
                "-3delta": 0.821506551356352,
                "-4delta": 0.7750256100728546,
                "-5delta": 0.7380704081209178,
                "-10delta": 0.6211099585040779,
                "-15delta": 0.5593907191516118,
                "-20delta": 0.5285397828706098,
                "-25delta": 0.5164011072124609,
                "-30delta": 0.514097596581255,
                "-35delta": 0.5167077927901441,
                "-40delta": 0.5219297676593241,
                "-45delta": 0.5287168104838338,
                "-50delta": 0.5365950210771168,
                "50delta": 0.5386677287427696,
                "45delta": 0.5476536125138948,
                "40delta": 0.5575111674699238,
                "35delta": 0.568343362983306,
                "30delta": 0.5803663347586634,
                "25delta": 0.5939535554971676,
                "20delta": 0.6097493775534791,
                "15delta": 0.6289580161408765,
                "10delta": 0.654229047696094,
                "5delta": 0.6937189470245454,
                "4delta": 0.7056448853527967,
                "3delta": 0.7205524465651685,
                "2delta": 0.7407744539744261,
                "1delta": 0.7735395679826267,
                "strikes": [
                    19096.03915765128,
                    21728.39599687067,
                    23385.311580995785,
                    24607.197241811446,
                    25576.907314590706,
                    28646.103200988895,
                    30386.221531351763,
                    31539.32211218384,
                    32404.15799162592,
                    33136.207367473,
                    33811.22150460142,
                    34467.126084143296,
                    35126.321111832025,
                    35805.33719940092,
                    35976.61280115009,
                    36701.57233004659,
                    37482.5109117388,
                    38341.290417981145,
                    39308.41836609429,
                    40430.26600021816,
                    41784.31207986573,
                    43517.10442898075,
                    45964.06267763068,
                    50233.60511728578,
                    51643.975524702866,
                    53494.15050403059,
                    56169.89622303556,
                    60953.44641826468,
                ],
                "vol_space": "delta",
            }
        )

        test_result = calculate_delta_from_params(
            self.svi_data, "SVI", "bybit", self.relevant_deltas
        )
        assert isinstance(test_result, pd.Series)
        assert sorted(test_result.index.tolist()) == sorted(
            [*DELTAS_STR_OUTPUT, "atm", "strikes", "vol_space"]
        )
        for key in expected_result.index:
            if isinstance(expected_result[key], list):
                assert all(
                    a == pytest.approx(b)
                    for a, b in zip(expected_result[key], test_result[key])
                ), f"Mismatch in list for {key}"
            else:
                assert expected_result[key] == pytest.approx(
                    test_result[key]
                ), f"Mismatch in value for {key}"

    def test_calculate_delta_from_params_SABR(self) -> None:
        expected_result = pd.Series(
            {
                "atm": 0.514022008681851,
                "-1delta": 0.9496901129244467,
                "-2delta": 0.8426357832764673,
                "-3delta": 0.7846215612650743,
                "-4delta": 0.7455951571383054,
                "-5delta": 0.7165882131448902,
                "-10delta": 0.634073779037696,
                "-15delta": 0.5918772225118236,
                "-20delta": 0.5653114354087949,
                "-25delta": 0.5471190368122111,
                "-30delta": 0.5342332979030474,
                "-35delta": 0.5251106176384402,
                "-40delta": 0.5189010125418234,
                "-45delta": 0.5151255076628953,
                "-50delta": 0.5135348099057966,
                "50delta": 0.5134657528961266,
                "45delta": 0.5144983465638838,
                "40delta": 0.5177413757496472,
                "35delta": 0.5234733854358,
                "30delta": 0.5322175370157717,
                "25delta": 0.5448998824915201,
                "20delta": 0.5632117080894036,
                "15delta": 0.5905510468335705,
                "10delta": 0.6351006063974972,
                "5delta": 0.725519749960137,
                "4delta": 0.7582657589221729,
                "3delta": 0.8030921150785039,
                "2delta": 0.8713625676698226,
                "1delta": 1.0025894161395439,
                "strikes": [
                    19512.029400979824,
                    22207.441622323382,
                    23798.413388233523,
                    24928.677018314167,
                    25805.103036194192,
                    28528.32156010527,
                    30138.41136684871,
                    31309.57777943178,
                    32252.810321316425,
                    33062.71870494379,
                    33791.072041666106,
                    34470.61924109066,
                    35124.92730007179,
                    35773.31263337249,
                    35933.48852732261,
                    36599.95034530023,
                    37303.429822187565,
                    38069.36312604083,
                    38933.45067884792,
                    39951.365934451154,
                    41220.38525824234,
                    42936.873004270994,
                    45594.190658159896,
                    51090.00855100851,
                    53198.76836350353,
                    56225.677845297236,
                    61197.73579987503,
                    72232.66848272379,
                ],
                "vol_space": "delta",
            }
        )

        test_result = calculate_delta_from_params(
            self.sabr_data, "SABR", "bybit", self.relevant_deltas
        )

        assert isinstance(test_result, pd.Series)
        assert sorted(test_result.index.tolist()) == sorted(
            [*DELTAS_STR_OUTPUT, "atm", "strikes", "vol_space"]
        )
        assert test_result["vol_space"], "delta"
        for key in expected_result.index:
            if isinstance(expected_result[key], list):
                assert all(
                    a == pytest.approx(b)
                    for a, b in zip(expected_result[key], test_result[key])
                ), f"Mismatch in list for {key}"
            else:
                assert expected_result[key] == pytest.approx(
                    test_result[key]
                ), f"Mismatch in value for {key}"

    def test_calculate_delta_from_params_spline(self) -> None:
        expected_result = pd.Series(
            {
                "atm": 0.565630189574002,
                "-1delta": 0.921661552551796,
                "-2delta": 0.847501875012232,
                "-3delta": 0.792027625826867,
                "-4delta": 0.751756304066829,
                "-5delta": 0.724710902863677,
                "-10delta": 0.651724391506745,
                "-15delta": 0.614554087769806,
                "-20delta": 0.593049527885303,
                "-25delta": 0.581816441549692,
                "-30delta": 0.575284029338815,
                "-35delta": 0.569502974087722,
                "-40delta": 0.567047754188297,
                "-45delta": 0.565707799460113,
                "-50delta": 0.566441615931322,
                "50delta": 0.566957659552489,
                "45delta": 0.56895464076088,
                "40delta": 0.572053892595853,
                "35delta": 0.577632167639479,
                "30delta": 0.582575486573489,
                "25delta": 0.589907544872485,
                "20delta": 0.602338860070848,
                "15delta": 0.615868186597183,
                "10delta": 0.639487693152062,
                "5delta": 0.688370181247677,
                "4delta": 0.702269616320534,
                "3delta": 0.72200386034858,
                "2delta": 0.755471874889816,
                "1delta": 0.809637474388163,
                "strikes": [
                    53874.48431331396,
                    60156.35557406821,
                    64402.16104322787,
                    67514.92315885796,
                    69843.96895652158,
                    77044.01364678696,
                    81387.17577597052,
                    84570.21996124662,
                    87141.32352666532,
                    89400.65556813811,
                    91497.75720439549,
                    93483.67334418822,
                    95427.94762906227,
                    97377.6797419475,
                    97774.65558261998,
                    99790.76662889474,
                    101912.94235797263,
                    104220.73102336528,
                    106740.36742988633,
                    109631.93729504311,
                    113171.77230699726,
                    117583.45612877636,
                    123994.02054190637,
                    135974.98555055575,
                    139924.84080121972,
                    145339.83632781904,
                    153944.9431316791,
                    169902.05877701752,
                ],
                "vol_space": "delta",
            }
        )

        test_result = calculate_delta_from_params(
            self.spline_data, "spline", "deribit", self.relevant_deltas
        )

        assert isinstance(test_result, pd.Series)
        assert sorted(test_result.index.tolist()) == sorted(
            [*DELTAS_STR_OUTPUT, "atm", "strikes", "vol_space"]
        )
        assert test_result["vol_space"], "delta"
        for key in expected_result.index:
            if isinstance(expected_result[key], list):
                assert all(
                    a == pytest.approx(b)
                    for a, b in zip(expected_result[key], test_result[key])
                ), f"Mismatch in list for {key}"
            else:
                assert expected_result[key] == pytest.approx(
                    test_result[key]
                ), f"Mismatch in value for {key}"
