import logging
from typing import Any
from unittest.mock import patch

import pytest
from retrieve_data import (
    _decompose_exchanges,
    _transform_results,
    retrieve_data,
)


def test_only_raw_exchanges() -> None:
    exchanges = ["deribit", "v2lyra"]
    expected_mapping = {"deribit": {"deribit"}, "v2lyra": {"v2lyra"}}
    expected_all_exchanges = ["deribit", "v2lyra"]

    mapping, all_exchanges = _decompose_exchanges(exchanges)

    assert mapping == expected_mapping
    assert sorted(all_exchanges) == sorted(expected_all_exchanges)


def test_mix_of_raw_and_supercomposite_exchanges() -> None:
    exchanges = ["deribit", "v2lyra-supercomposite"]
    expected_mapping = {
        "deribit": {"deribit"},
        "v2lyra": {"v2lyra-supercomposite"},
        "v2composite": {"v2lyra-supercomposite"},
    }
    expected_all_exchanges = ["deribit", "v2lyra", "v2composite"]

    mapping, all_exchanges = _decompose_exchanges(exchanges)

    assert mapping == expected_mapping
    assert sorted(all_exchanges) == sorted(expected_all_exchanges)


def test_multi_supercomposite_exchanges() -> None:
    exchanges = ["v2lyra-supercomposite", "exchange-supercomposite"]
    expected_mapping = {
        "v2lyra": {"v2lyra-supercomposite"},
        "v2composite": {"v2lyra-supercomposite", "exchange-supercomposite"},
        "exchange": {"exchange-supercomposite"},
    }
    expected_all_exchanges = ["v2lyra", "exchange", "v2composite"]

    mapping, all_exchanges = _decompose_exchanges(exchanges)

    assert mapping == expected_mapping
    assert sorted(all_exchanges) == sorted(expected_all_exchanges)


def test_duplicate_exchanges() -> None:
    exchanges = [
        "deribit",
        "deribit",
        "v2lyra-supercomposite",
        "v2lyra-supercomposite",
    ]
    expected_mapping = {
        "deribit": {"deribit"},
        "v2lyra": {"v2lyra-supercomposite"},
        "v2composite": {"v2lyra-supercomposite"},
    }
    expected_all_exchanges = ["deribit", "v2lyra", "v2composite"]

    mapping, all_exchanges = _decompose_exchanges(exchanges)

    assert mapping == expected_mapping
    assert sorted(all_exchanges) == sorted(expected_all_exchanges)


# fmt: off
@pytest.mark.parametrize(
    "results, qn_freq, is_live_version, expected",
    [
        (
                [{"qualified_name": "deribit.option.BTC.SVI.7d.1m.params", "timestamp": 1234567890}],
                "1m",
                True,
                [{"arbitrary_constant_maturity": False, "arbitrary_expiry": False, "listed_expiry": False, "qualified_name": "deribit.option.BTC.SVI.7d.live.params", "standard_constant_maturity": True, "timestamp": 1234567890}],
        ),
        (
                [{"qualified_name":"deribit.option.BTC.SVI.7d.1m.params", "timestamp": 1234567890}],
                "1m",
                False,
                [{"arbitrary_constant_maturity": False, "arbitrary_expiry": False, "listed_expiry": False, "qualified_name": "deribit.option.BTC.SVI.7d.1m.params", "standard_constant_maturity": True, "timestamp": 1234567890}]
        ),
        (
                [{"qualified_name":"deribit.option.BTC.SVI.listed.1m.params", "params": [{"qualified_name":"deribit.option.BTC.SVI.2000-05-22T08:00:00.1m.params"}, {"qualified_name":"deribit.option.BTC.SVI.3000-05-22T08:00:00.1m.params"}], "timestamp": 1234567890}],
                "1m",
                True,
                [{"arbitrary_constant_maturity": False, "arbitrary_expiry": False, "listed_expiry": True, "qualified_name": "deribit.option.BTC.SVI.2000-05-22T08:00:00.live.params", "standard_constant_maturity": False, "timestamp": 1234567890},
                 {"arbitrary_constant_maturity": False, "arbitrary_expiry": False, "listed_expiry": True, "qualified_name": "deribit.option.BTC.SVI.3000-05-22T08:00:00.live.params", "standard_constant_maturity": False, "timestamp": 1234567890}]
        ),
        (
                [{"qualified_name":"deribit.option.BTC.SVI.listed.1m.params", "timestamp": 1234567890}],
                "1m",
                False,
                []
        )
    ]
)
# fmt: off
def test_transform_results(results: list[dict[str, object]], qn_freq: str, is_live_version: bool, expected: list[dict[str, object]], caplog: pytest.LogCaptureFixture)-> None:
    with caplog.at_level(logging.ERROR):
        transformed_results = _transform_results(results, qn_freq, is_live_version)

    if not transformed_results:
        assert "invalid result, calc_type 'params' not found" in caplog.text

    assert transformed_results == expected


@pytest.mark.parametrize(
    "exchanges, expected_map",
    [
        (
            ["deribit", "v2lyra-supercomposite"],
            {
                "deribit": {"deribit"},
                "v2lyra": {"v2lyra-supercomposite"},
                "v2composite": {"v2lyra-supercomposite"},
            },
        ),
        (
            ["v2lyra-supercomposite", "v2kraken-supercomposite"],
            {
                "v2lyra": {"v2lyra-supercomposite"},
                "v2kraken": {"v2kraken-supercomposite"},
                "v2composite": {"v2lyra-supercomposite", "v2kraken-supercomposite"},
            },
        ),
        (
            ["deribit", "v2composite"],
            {
                "deribit": {"deribit"},
                "v2composite": {"v2composite"},
            },
        ),
    ],
)
def test_decompose_exchanges(exchanges: list[str], expected_map: dict[str, set[str]]) -> None:
    exchange_map, raw_exchanges = _decompose_exchanges(exchanges)
    assert exchange_map == expected_map
    assert set(raw_exchanges) == set(expected_map.keys())


@pytest.mark.parametrize(
    "input_data, expected_instruments, expected_exchange_map",
    [
        (
                {
                    "start": "2023-01-01T00:00:00Z",
                    "end": "2023-01-02T00:00:00Z",
                    "currencies": ["BTC"],
                    "exchanges": ["deribit", "v2lyra-supercomposite"],
                    "models": ["SVI"],
                    "calc_types": ["smile"],
                    "freq": "1m",
                    "interval": "1m",
                    "mock_instruments": [{
                        "strike": 20000,
                        "expiry": "2023-06-30T08:00:00Z",
                        "qualified_name": "deribit.option.BTC-30JUN23-20000-C",
                        "baseAsset": "BTC",
                        "instrument": "BTC-30JUN23-20000-C",
                        "listing": "2023-01-01T00:00:00Z",
                        "availableSince": "2023-01-01T00:00:00Z"
                    }],
                },
                {
                    "BTC-30JUN23-20000-C": {
                        "expiry": "2023-06-30T08:00:00Z",
                        "instrument_name": "BTC-30JUN23-20000-C",
                        "strike": 20000,
                        "baseAsset": "BTC",
                        "qualified_name": "deribit.option.BTC-30JUN23-20000-C",
                        "listing": "2023-01-01T00:00:00Z",
                        "availableSince": "2023-01-01T00:00:00Z"
                    }
                },
                {
                    "deribit": {"deribit"},
                    "v2lyra": {"v2lyra-supercomposite"},
                    "v2composite": {"v2lyra-supercomposite"}
                }
        ),
    ]
)
def test_retrieve_data_with_instruments(
        input_data: dict[str, Any],
        expected_instruments: dict[str, Any],
        expected_exchange_map: dict[str, Any],
) -> None:
    with patch("retrieve_data.grab") as mock_grab, \
            patch("retrieve_data.get_instruments") as mock_get_instruments:

        mock_get_instruments.return_value = input_data["mock_instruments"]
        mock_grab.return_value = []

        _, _, instruments, exchange_map = retrieve_data(
            start=input_data["start"],
            end=input_data["end"],
            currencies=input_data["currencies"],
            exchanges=input_data["exchanges"],
            models=input_data["models"],
            calc_types=input_data["calc_types"],
            freq=input_data["freq"],
            interval=input_data["interval"],
            include_listed_expiries=True,
            tenor_mode="standard",
            debug=False,
            consistent_read=False,
            fetch_lookback_data=False,
        )

        assert instruments == expected_instruments
        assert exchange_map == expected_exchange_map
