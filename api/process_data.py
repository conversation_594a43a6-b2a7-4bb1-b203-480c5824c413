import functools
import logging
import math
import time
from multiprocessing.connection import Connection
from typing import Any, cast

import pandas as pd
import utils_general
from calc_helpers import SMOOTH_PARAMS
from constants import (
    CALIB_R2_THRESHOLD,
    LOOKBACK_COLUMN_NAME,
    NUM_WORKERS,
    R2_SMOOTHCALIB_COLUMN_NAME,
)
from lambda_types import (
    AuxCalcResult,
    AuxCalcType,
    CalcTypeTenorSmiles,
    DerivedCalcResult,
    ExchangeCurrencyToTimestampTenorExpiryArbFields,
    FinalCalcResult,
    FrequencyDetails,
    ParamCalcFnsTypes,
    ParamsCalcResult,
    ParamsQueryResult,
    ProcessDataResult,
    ProcessDataSnapshot,
    SmoothingAggregator,
    SmoothingAndEstimationResult,
    SmoothingCalcType,
    VolSurfaceCalcType,
)
from lookback_window import lookback_and_append
from sabr_params_surface_calc import sabr_tenor_params
from spline_smile_calc_seq import spline_tenor_params
from svi_params_surface_calc import svi_tenor_params
from utils.arbitrary_lookback import (
    identify_and_populate_arbitrary_values_for_smoothing,
)
from utils.common import (
    is_derived_calc_result,
    is_params_calc_result,
    modify_string,
    remove_nan_columns_from_df,
    seconds_to_qn_convention,
)
from utils.derived_surface import smile_derived_data
from utils.outputs import (
    collect_final_results,
    drop_available_columns_from_df,
    group_listed_expiries,
    handle_tenor_intersections,
)
from utils.smoothing import (
    smooth_calc_type_chunk,
)
from utils.surface import create_volatility_surfaces
from utils_calc import Model
from utils_general import get_qfn_and_version

pd.options.mode.chained_assignment = None  # type: ignore

PARAMS_CALC_FNS: ParamCalcFnsTypes = {
    "SVI": svi_tenor_params,
    "SABR": sabr_tenor_params,
    "spline": spline_tenor_params,
}


def process_snap_chunk_into_params_helper(
    chunk: list[ParamsQueryResult],
    exchange_curr_timestamp_to_expiry_info: ExchangeCurrencyToTimestampTenorExpiryArbFields,
) -> list[ParamsCalcResult] | dict[str, str]:

    if not chunk:
        return {
            "error": "Empty chunk received in parallel process",
            "details": "Params to snap into Params",
        }

    results: list[ParamsCalcResult] = []
    try:
        for slice in chunk:
            qn = slice["qualified_name"]
            version, qn_tokens = get_qfn_and_version(qn)
            exchange, currency, model = (
                qn_tokens[0],
                qn_tokens[2],
                qn_tokens[3],
            )
            snap = modify_string(slice["params"])
            timestamp = slice["timestamp"]
            isodate = utils_general.to_iso(timestamp)
            # v2composite & supercomposite expected not to have R2 as they
            # already filter badly calibrated expiries internally
            filt_snap = [
                params
                for params in snap
                if ("R2" not in params or params["R2"] >= CALIB_R2_THRESHOLD)
            ]
            if not len(filt_snap):
                raise Exception("R2 filtration yielded empty results")

            try:
                if snap:
                    params_calc_snap_result = PARAMS_CALC_FNS[model](
                        snap=snap,  # type: ignore
                        exchange=exchange,
                        currency=currency,
                        expiry_iv_info=exchange_curr_timestamp_to_expiry_info[
                            exchange
                        ][currency][timestamp],
                    )
                    results.append(params_calc_snap_result)

                else:
                    logging.error(
                        f"Could not process params - listed params were empty. {qn=}, {timestamp=}, {isodate=}",
                    )
            except Exception:
                logging.exception(
                    f"Error processing snap into params, {qn=}, {timestamp=}, {isodate=}"
                )
        if not results:
            raise Exception(f"Empty results for chunk of size={len(chunk)}")

        return results

    except Exception as e:
        logging.exception(f"Error processing data chunk into params, {e=}")
        return {"error": str(e), "details": ""}


def process_snap_chunk_into_params(
    exchange_curr_timestamp_to_expiry_info: ExchangeCurrencyToTimestampTenorExpiryArbFields,
    chunk: list[ParamsQueryResult],
    conn: Connection,
) -> None:
    results = process_snap_chunk_into_params_helper(
        chunk=chunk,
        exchange_curr_timestamp_to_expiry_info=exchange_curr_timestamp_to_expiry_info,
    )
    conn.send(results)
    conn.close()


def process_params_chunk_into_volsurface_helper(
    calc_types: list[VolSurfaceCalcType],
    chunk: list[ParamsCalcResult],
    exchange_curr_timestamp_to_expiry_info: ExchangeCurrencyToTimestampTenorExpiryArbFields,
) -> list[DerivedCalcResult] | dict[str, str]:

    if not chunk:
        return {
            "error": "Empty chunk received in parallel process",
            "details": "Params to volsurface helper",
        }

    results: list[DerivedCalcResult] = []

    for slice in chunk:
        timestamp = slice["timestamp"]
        isodate = utils_general.to_iso(timestamp)
        try:
            params_df = slice["dfs"]["params"]
            # filter out tenors that may have failed due to bad calibrations.
            df_vol_matrix_pre = slice["df_vol_matrix_pre"][
                slice["df_vol_matrix_pre"].index.isin(params_df["tenor_days"])
            ]

            results.append(
                create_volatility_surfaces(
                    params_surface=params_df,
                    df_vol_matrix_pre=df_vol_matrix_pre,
                    timestamp=timestamp,
                    currency=slice["currency"],
                    exchange=slice["exchange"],
                    calc_types=calc_types,
                    model=slice["model"],
                    expiry_iv_info=exchange_curr_timestamp_to_expiry_info[
                        slice["exchange"]
                    ][slice["currency"]][timestamp],
                )
            )
        except Exception as e:
            logging.exception(
                f"Error processing params into volsurface, {slice['exchange']}.{slice['currency']}.{slice['model']} {timestamp} {isodate}, {e=:}"
            )
    if not results:
        log_msg = f"Empty results after processing params chunk into volsurface for chunk of size={len(chunk)}"
        logging.error(log_msg)
        return {"error": log_msg, "details": ""}

    return results


def process_params_chunk_into_volsurface(
    calc_types: list[VolSurfaceCalcType],
    chunk: list[ParamsCalcResult],
    conn: Connection,
    exchange_curr_timestamp_to_expiry_info: ExchangeCurrencyToTimestampTenorExpiryArbFields,
) -> None:
    results = process_params_chunk_into_volsurface_helper(
        calc_types=calc_types,
        chunk=chunk,
        exchange_curr_timestamp_to_expiry_info=exchange_curr_timestamp_to_expiry_info,
    )
    conn.send(results)
    conn.close()


def process_derived_chunk_into_aux_calc_types_helper(
    calc_types: list[AuxCalcType],
    chunk: list[FinalCalcResult],
) -> list[AuxCalcResult] | dict[str, str]:

    if not chunk:
        return {
            "error": "Empty chunk received in parallel process",
            "details": "Derived chunk to Aux Surfaces",
        }

    results: list[AuxCalcResult] = []
    for slice in chunk:
        try:
            timestamp = slice["timestamp"]
            isodate = utils_general.to_iso(timestamp)
            if slice["dfs"]["smile"]["timestamp"].nunique() != 1:
                raise AssertionError(
                    "Non Unique timestamps encountered, investigate"
                )

            results.append(
                smile_derived_data(
                    params_surface=slice["dfs"]["params"],
                    delta_vol_matrix_df=slice["dfs"]["smile"],
                    calc_types=calc_types,
                    model=slice["model"],
                )
            )
        except Exception as e:
            logging.exception(
                f"Error processing derived chunks into Aux volsurfaces, {slice['exchange']}.{slice['currency']}.{slice['model']} {slice['timestamp']} {isodate}, {e=:}"
            )
    if not results:
        error_msg = f"Empty results after processing derived chunks into Aux volsurfaces for chunk of size={len(chunk)}"
        logging.error(error_msg)
        return {"error": error_msg, "details": ""}

    return results


def process_derived_chunk_into_aux_calc_types(
    calc_types: list[AuxCalcType],
    chunk: list[FinalCalcResult],
    conn: Connection,
) -> None:
    results = process_derived_chunk_into_aux_calc_types_helper(
        calc_types=calc_types,
        chunk=chunk,
    )
    conn.send(results)
    conn.close()


def process_data(
    chunk: ProcessDataSnapshot,
    interval: str,
    periods: int,
    estimate_params: bool,
    include_listed_expiries: bool,
    smooth: bool = False,
    consistent_read: bool = False,
    debug: bool = False,
    scheduled_version: str = "",
    **kwargs: Any,  # Added for compatibility with blockstream utils
) -> ProcessDataResult:
    vol_surface_calc_types = chunk.calc_type_inputs["vol_surface_calc_types"]
    smoothing_calc_types = chunk.calc_type_inputs["smoothing_calc_types"]
    aux_calc_types = chunk.calc_type_inputs["aux_calc_types"]

    freq, historic_freq, round_periods, round_interval = get_frequency_details(
        interval=interval, periods=periods
    )

    # Calc params
    t = time.time()
    utils_general.log_bsdebug(f"Params input data {len(chunk.params)=}")
    params_results = cast(
        list[ParamsCalcResult],
        utils_general.parallel_process(
            chunk.params,
            NUM_WORKERS,
            functools.partial(
                process_snap_chunk_into_params,
                exchange_curr_timestamp_to_expiry_info=chunk.exchange_curr_timestamp_to_expiry_info.copy(),
            ),
            True,
        ),
    )
    logging.info(f"Params calc took {round(time.time() - t, 2)}s")

    # Calc volsurface
    t = time.time()
    derived_result = cast(
        list[DerivedCalcResult],
        utils_general.parallel_process(
            params_results,
            NUM_WORKERS,
            functools.partial(
                process_params_chunk_into_volsurface,
                calc_types=vol_surface_calc_types,
                exchange_curr_timestamp_to_expiry_info=chunk.exchange_curr_timestamp_to_expiry_info.copy(),
            ),
            True,
        ),
    )
    logging.info(
        f"Moneyness and Delta Surface calc took {round(time.time() - t, 2)}s"
    )

    calculation_results = handle_tenor_intersections(
        results=[*params_results, *derived_result],
        exchange_curr_timestamp_to_expiry_info=chunk.exchange_curr_timestamp_to_expiry_info.copy(),
        vol_surface_calc_types=vol_surface_calc_types,
        freq=freq if interval != "second" else "live",
        include_listed_expiries=include_listed_expiries,
        version=scheduled_version,
    )

    # collect final_results
    final_calc_result = collect_final_results(
        calculation_results=calculation_results,
        vol_surface_calc_types=vol_surface_calc_types,
    )

    _log_sanity_check_params_length(final_calc_result, "Pre Smoothing")

    t = time.time()
    if smooth:
        # helper dictionary to group post-smoothing smiles into their surfaces based on timestamps
        calc_type_aux: SmoothingAggregator = utils_general.nested_dict()

        all_calc_type_dfs = collect_calc_type_results(
            results=calculation_results,
            calc_type_aux=calc_type_aux,
            vol_surface_calc_types=vol_surface_calc_types,
            freq=freq,
        )

        # Smooth smiles
        # Note: if all_calc_type_dfs is empty, then the pd.concat() below would fail
        #       so skipping smoothing entirely (nothing to smooth anyway)
        if freq in SMOOTH_PARAMS and len(all_calc_type_dfs) > 0:
            current_results = pd.concat(
                all_calc_type_dfs, copy=False, sort=False, ignore_index=True
            )
            current_and_lookback_results = lookback_and_append(
                previous_results=chunk.previous_results,
                current_result=current_results,
                end_str=chunk.lookback_end_iso,
                interval=round_interval,
                lookback_window_size=SMOOTH_PARAMS[freq]["zscore"]["window"],
                debug=debug,
                freq=historic_freq,
                consistent_read=consistent_read,
                include_listed_expiries=include_listed_expiries,
                calc_types=smoothing_calc_types,
                scheduled_version=scheduled_version,
            )
            current_and_lookback_results = (
                identify_and_populate_arbitrary_values_for_smoothing(
                    current_and_lookback_results=current_and_lookback_results,
                    current_result=current_results,
                    calc_types=vol_surface_calc_types,
                    scheduled_version=scheduled_version,
                    chunk_start=chunk.lookback_end_iso,
                )
            )
            # Update the copy of the lookback as it will be populated with arbitrary lookback vols
            # The copy is returned from process_data, updated and the original lookback is replaced with
            # the copy and passed onto the subsequent run (in live or backfill mode)
            chunk.previous_results["df"] = current_and_lookback_results[
                current_and_lookback_results[LOOKBACK_COLUMN_NAME].eq(True)
            ]
            logging.info(
                f"Smoothing lookback preparation time took {round(time.time() - t, 2)}s"
            )

            t = time.time()
            qualified_name_prefix_timeseries_list = [
                qualified_name_prefix_grouped_df
                for _, qualified_name_prefix_grouped_df in current_and_lookback_results.groupby(
                    [
                        "qualified_name_prefix"
                    ],  # qualified_name_prefix is assigned in lookback function
                    sort=False,
                )
            ]

            # smooth params timeseries
            smoothing_results: list[SmoothingAndEstimationResult] = (
                utils_general.parallel_process(
                    data_slices=qualified_name_prefix_timeseries_list,
                    num_workers=NUM_WORKERS,
                    process_chunk_fn=functools.partial(
                        smooth_calc_type_chunk_helper,
                        freq=freq,
                        start=chunk.lookback_end_iso,
                        smoothing_calc_types=smoothing_calc_types,
                        estimate_params=estimate_params,
                    ),
                    chunk_data=True,
                )
            )
            # modifies calc_type_aux inside
            aggregate_smoothed_smiles_to_surfaces(
                smoothing_results=smoothing_results,
                aux_calc_type_dict=calc_type_aux,
                chunk_start=chunk.lookback_end_iso,
            )

        # concat individual smoothed smiles into their surfaces
        for r in final_calc_result:
            for _calc_type in smoothing_calc_types:
                try:
                    calc_type_dfs = calc_type_aux[r["exchange"]][r["currency"]][
                        r["model"]
                    ][r["timestamp"]][_calc_type]

                    r["dfs"][_calc_type] = remove_nan_columns_from_df(
                        pd.concat(
                            calc_type_dfs,
                            copy=False,
                            sort=False,
                            ignore_index=True,
                        )
                    )
                except Exception:
                    logging.exception(
                        f"Error concatenating smoothed smiles into surfaces, {r['exchange']}.{r['currency']}.{r['model']} {r['timestamp']}, {_calc_type=}"
                    )

        logging.info(f"Smoothing took {round(time.time() - t, 2)}s")

        # keeping this in memory slows down debug evaluations a lot
        del calc_type_aux

    _log_sanity_check_params_length(final_calc_result, "Post Smoothing")

    t = time.time()
    aux_calc_type_result = cast(
        list[AuxCalcResult],
        utils_general.parallel_process(
            final_calc_result,
            NUM_WORKERS,
            functools.partial(
                process_derived_chunk_into_aux_calc_types,
                calc_types=aux_calc_types,
            ),
            chunk_data=True,
        ),
    )
    logging.info(f"Aux Calc took {round(time.time() - t, 2)}s")

    aux_calc_type_result = cast(
        list[AuxCalcResult],
        sorted(
            aux_calc_type_result,
            key=lambda r: (r["timestamp"], r["model"], r["currency"]),
        ),
    )
    final_calc_result = cast(
        list[FinalCalcResult],
        sorted(
            final_calc_result,
            key=lambda r: (r["timestamp"], r["model"], r["currency"]),
        ),
    )

    final_results: list[pd.DataFrame] = []

    for r_final, r_aux in zip(
        final_calc_result,
        aux_calc_type_result,
    ):
        assert r_final["timestamp"] == r_aux["timestamp"]
        assert r_final["model"] == r_aux["model"]
        assert r_final["currency"] == r_aux["currency"]

        for ct in aux_calc_types:
            r_final["dfs"][ct] = r_aux["dfs"][ct]

        for _, df in r_final["dfs"].items():
            assert isinstance(df, pd.DataFrame)
            final_results.append(df)

    full_df = (
        pd.concat(final_results, ignore_index=True, sort=False, copy=False)
        if final_results
        else pd.DataFrame()
    )

    if full_df.empty:
        logging.exception("Resulting DataFrame returned empty!")
        return {
            "calc_output": full_df,
            "lookback_result": chunk.previous_results,
        }

    full_df = post_process_dataframe(
        full_df=full_df,
        chunk=chunk,
        include_listed_expiries=include_listed_expiries,
        interval=interval,
        estimate_params=estimate_params,
    )

    return {
        "calc_output": full_df,
        "lookback_result": chunk.previous_results,
    }


def post_process_dataframe(
    full_df: pd.DataFrame,
    chunk: ProcessDataSnapshot,
    include_listed_expiries: bool,
    interval: str,
    estimate_params: bool,
) -> pd.DataFrame:
    """
    Apply all post-processing steps to the final dataframe before returning results.

    Args:
        full_df: The concatenated dataframe from all calc results
        chunk: The original data snapshot containing params
        include_listed_expiries: Whether to group listed expiries
        interval: The calculation interval
        estimate_params: Whether params estimation is enabled

    Returns:
        Post-processed dataframe ready for storage
    """
    if full_df.empty:
        return full_df

    # Store intermediate columns
    intermediate_cols = ["R2_calib", R2_SMOOTHCALIB_COLUMN_NAME]

    def apply_intermediate(row: dict[str, Any]) -> dict[str, Any]:
        row_metrics: dict[str, Any] = {}
        params_scores: dict[str, Any] = {}
        for col in intermediate_cols:
            # TODO Pass R2/intermediate data from param output for listed expiries.
            # R2 is not computed for listed expiries, in the event this occurs don't add
            if col in row and not math.isnan(row[col]):
                row_metrics[col] = row[col]

        # Copy confidence score from params
        if "intermediate" in chunk.params[0]:
            params_scores = utils_general.json_loads(
                chunk.params[0]["intermediate"]
            )["scores"]
        return {"scores": params_scores, **row_metrics}

    full_df["intermediate"] = full_df.apply(apply_intermediate, axis=1)

    if "R2_smoothcalib" in full_df:
        # Filter out rows with poor R2 smooth calibration scores
        # Keep rows where R2_smoothcalib is NaN (not applicable) or above threshold
        initial_count = len(full_df)

        full_df = full_df[
            (full_df["R2_smoothcalib"].isna())
            | (full_df["R2_smoothcalib"] > CALIB_R2_THRESHOLD)
        ]

        dropped_count = initial_count - len(full_df)
        if dropped_count > 0:
            logging.info(
                f"Dropped {dropped_count} rows with R2_smoothcalib <= {CALIB_R2_THRESHOLD} "
                f"(kept {len(full_df)} out of {initial_count} total rows)"
            )

    # we do not want to group if we are running VSF. they need to be
    # available in their raw qn's e.g v2composite.option.BTC.SVI.2023-12-22T17:36:00Z.1m.params
    # so that the results can be read. (e.g when a listed expiry is subscribed to)
    # VSF will neccesarily include_listed_expiries
    if include_listed_expiries and interval != "second":
        full_df = group_listed_expiries(full_df, estimate_params)

    # Drop unnecessary columns
    # NOTE: only include columns to be dropped across both live and scheduled systems
    # we want our Tenor Flags to be present in the live systems result
    columns_to_drop = [
        "qualified_name_prefix",
        "vols",
        "strikes",
        "vol_space",
        "expiry_str",
    ]
    full_df = drop_available_columns_from_df(
        df=full_df,
        columns=columns_to_drop,
    )

    return full_df


def aggregate_smoothed_smiles_to_surfaces(
    smoothing_results: list[SmoothingAndEstimationResult],
    aux_calc_type_dict: SmoothingAggregator,
    chunk_start: str,
) -> None:
    for result in smoothing_results:
        for _qualified_name_prefix, calc_type_to_timeseries in result.items():
            completed_calc_types: list[str] = []
            for (
                calc_type,
                qn_smoothed_df,
            ) in calc_type_to_timeseries.items():

                assert isinstance(qn_smoothed_df, pd.DataFrame)  # for mypy

                # Reset the timestamp index to make it more readily available for logging + pd.groupby() below
                qn_smoothed_df.reset_index(inplace=True)

                qn_smoothed_df = qn_smoothed_df[
                    qn_smoothed_df[LOOKBACK_COLUMN_NAME].ne(True)
                ]

                # cleanup
                qn_smoothed_df.drop(
                    ["qualified_name_prefix"],
                    axis="columns",
                    inplace=True,
                )
                version, qn_tokens = utils_general.get_qfn_and_version(
                    next(iter(qn_smoothed_df["qualified_name"]))
                )
                e, c, m, ct = (
                    qn_tokens[0],
                    qn_tokens[2],
                    cast(Model, qn_tokens[3]),
                    cast(SmoothingCalcType, qn_tokens[-1]),
                )

                assert ct == calc_type
                assert qn_smoothed_df[
                    "timestamp"
                ].is_unique, f"Duplicate timestamps found! Qualified_name: {qn_smoothed_df['qualified_name'].iloc[0]}, timestamps: {qn_smoothed_df[qn_smoothed_df['timestamp'].duplicated()]['timestamp']}"

                aux_calc_type_set = aux_calc_type_dict[e][c][m]

                # group the individual smiles into their volsurfaces by their timestamps post smoothing
                for ts, ts_grouped_df in qn_smoothed_df.groupby(
                    "timestamp", sort=False
                ):
                    assert isinstance(ts, int)  # mypy

                    if calc_type in aux_calc_type_set[ts]:
                        aux_calc_type_set[ts][ct].append(ts_grouped_df)
                    else:
                        aux_calc_type_set[ts][ct] = [ts_grouped_df]

                completed_calc_types.append(calc_type)

            if "params" not in completed_calc_types:
                logging.error(
                    f"PARAMS missing from smoothing aggregator, Investigate. {chunk_start=}, {_qualified_name_prefix=}, {completed_calc_types=}"
                )

    return


def smooth_calc_type_chunk_helper(
    chunk: list[pd.DataFrame],
    freq: str,
    start: str,
    conn: Connection,
    smoothing_calc_types: list[SmoothingCalcType],
    estimate_params: bool,
) -> None:
    results: SmoothingAndEstimationResult | dict[str, str] = {
        "error": "Error smoothing data chunk",
        "details": "",
    }
    try:
        results = smooth_calc_type_chunk(
            chunk=chunk,
            freq=freq,
            start=start,
            smoothing_calc_types=smoothing_calc_types,
            estimate_params=estimate_params,
        )

    except Exception as e:
        logging.exception(f"Error smoothing data chunk, e={e}")
        results = cast(dict[str, str], results)
        results["details"] = str(e)
    conn.send(results)
    conn.close()


def collect_calc_type_results(
    results: list[ParamsCalcResult | DerivedCalcResult],
    calc_type_aux: SmoothingAggregator,
    vol_surface_calc_types: list[VolSurfaceCalcType],
    freq: str,
) -> list[pd.DataFrame]:
    def _update_ts_data(
        ts_data: CalcTypeTenorSmiles,
        calc_type_key: SmoothingCalcType,
        df: pd.DataFrame,
    ) -> None:
        if calc_type_key in ts_data:
            ts_data[calc_type_key].append(df)
        else:
            ts_data[calc_type_key] = [df]

    all_calc_type_dfs = []

    for result in results:
        if is_derived_calc_result(result):
            for calc_type in vol_surface_calc_types:
                vol_surface_df = result["dfs"][calc_type]
                all_calc_type_dfs.append(vol_surface_df)
                if freq not in SMOOTH_PARAMS:
                    ts_data = calc_type_aux[result["exchange"]][
                        result["currency"]
                    ][result["model"]][result["timestamp"]]
                    _update_ts_data(
                        ts_data=ts_data,
                        calc_type_key=calc_type,
                        df=vol_surface_df,
                    )

        elif is_params_calc_result(result):
            params_df = result["dfs"]["params"]
            all_calc_type_dfs.append(params_df)
            if freq not in SMOOTH_PARAMS:
                ts_data = calc_type_aux[result["exchange"]][result["currency"]][
                    result["model"]
                ][result["timestamp"]]
                _update_ts_data(
                    ts_data=ts_data,
                    calc_type_key="params",
                    df=params_df,
                )

    return all_calc_type_dfs


def get_frequency_details(periods: int, interval: str) -> FrequencyDetails:
    """
    Calculate the frequencies for live and historic data.

    :param periods: The number of periods
    :param interval: The interval type (e.g., 'minute', 'hour', 'day')
    :return: A tuple containing the original frequency string, historic frequency string, rounded periods, and rounded interval
    """
    freq = f"{periods}{utils_general.INTERVAL_TO_LETTER[interval]}"

    assert (
        freq in SMOOTH_PARAMS
    ), "Frequency does not have a smoothing configuration"
    # Live frequency is rounded to 60s to fetch lookback data for it when needed
    round_to_seconds = SMOOTH_PARAMS[freq].get("round_to_seconds")
    if round_to_seconds:
        round_periods, round_interval = seconds_to_qn_convention(
            round_to_seconds
        )
        historic_freq = (
            f"{round_periods}{utils_general.INTERVAL_TO_LETTER[round_interval]}"
        )
    else:
        historic_freq = freq
        round_periods = periods
        round_interval = interval

    return freq, historic_freq, round_periods, round_interval


def _log_sanity_check_params_length(
    final_calc_result: list[FinalCalcResult], stage: str
) -> None:
    for calc_result in final_calc_result:
        dfs = calc_result["dfs"]
        if len(dfs["params"]) != len(dfs["moneyness"]):
            logging.warning(
                f"Params and moneyness length mismatch found {stage}, {calc_result['timestamp']=}, {calc_result['model']=}, {len(dfs['params'])=}, {len(dfs['moneyness'])=}"
            )
