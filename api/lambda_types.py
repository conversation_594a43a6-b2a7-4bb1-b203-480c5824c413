from collections import UserDict, defaultdict
from dataclasses import dataclass, field
from enum import Enum
from typing import (
    Any,
    Callable,
    DefaultDict,
    Literal,
    Mapping,
    Optional,
    TypedDict,
    TypeVar,
)

import numpy as np
import pandas as pd
from numpy.typing import NDArray
from pandas import DataFrame
from typing_extensions import NotRequired
from utils_aws import CalcBase, DateRange, Frequency
from utils_calc import CubicSpline, Model, SabrParams, SviParams

CalcType = Literal[
    "params", "smile", "skew", "moneyness", "butterfly", "strike"
]
ParamsCalcType = Literal["params"]
VolSurfaceCalcType = Literal["smile", "moneyness", "strike"]
SmoothingCalcType = Literal["smile", "moneyness", "params", "strike"]
AuxCalcType = Literal["skew", "butterfly"]
TenorMode = Literal["standard", "granular"]
SmoothingType = Literal["EMA", "SMA"]

CompositeExchanges = Literal["bybit", "deribit"]

CalibrationR2 = float
RMSE = float
MSE = float
MAE = float
CalibrationMetrics = tuple[CalibrationR2, RMSE, MSE, MAE]

NDArrayFloat64 = NDArray[np.float64]

LowerExpiryStrikes = NDArrayFloat64
StrikesToInterpolate = NDArrayFloat64
UpperExpiryStrikes = NDArrayFloat64

StrikesToInterpolateResult = tuple[
    LowerExpiryStrikes,
    StrikesToInterpolate,
    UpperExpiryStrikes,
]


SingleParams = (
    pd.Series  # type: ignore
    | Mapping[str, str | float | int | list[float] | list[int]]
)  # assuming one row of a modelparams df output

QnsList = TypeVar("QnsList", pd.Series, list[str])  # type: ignore
ExpiryType = TypeVar("ExpiryType", pd.Series, str)  # type: ignore
VolSpace = Literal["money", "delta"]

SmoothedVolDf = DataFrame

VolCalcStrikeInputs = TypeVar(
    "VolCalcStrikeInputs", float, NDArrayFloat64, list[float]
)

CalcTypeTenorSmiles = dict[SmoothingCalcType, list[pd.DataFrame]]

# exchange -> currency -> model -> timestamp -> CalcTypeTenorSmiles
SmoothingAggregator = defaultdict[
    str,
    defaultdict[
        str,
        defaultdict[
            Model,
            defaultdict[int, CalcTypeTenorSmiles],
        ],
    ],
]

Freq = str
HistoricFreq = str
RoundPeriods = int
RoundInterval = str
FrequencyDetails = tuple[Freq, HistoricFreq, RoundPeriods, RoundInterval]


class CalcArgs(TypedDict):
    date_range: DateRange
    frequency: Frequency
    exchanges: list[str]
    currencies: list[str]
    models: list[Model]
    types: list[CalcType]
    tenor_mode: TenorMode
    include_listed_expiries: bool
    smooth: Optional[bool]
    consistent_read: Optional[bool]
    previous_result: Optional[DataFrame]
    include_result_response: Optional[bool]
    debug: Optional[bool]
    estimate_params: NotRequired[bool]


class VolSmileCalc(CalcBase):
    args: CalcArgs


class BaseCalcResult(TypedDict):
    model: Model
    exchange: str
    currency: str
    timestamp: int


class QueryResult(TypedDict):
    timestamp: int
    qualified_name: str
    intermediate: NotRequired[str]


class ListedTenorParamsBase(TypedDict):
    expiry: float
    timestamp: int
    spot: float
    underlying_index: str
    forward: float
    runtime: str
    api_version: str
    atm_vol: float
    R2: float


class ListedTenorParamsSVI(ListedTenorParamsBase):
    svi_a: float
    svi_b: float
    svi_rho: float
    svi_m: float
    svi_sigma: float


class ListedTenorParamsSABR(ListedTenorParamsBase):
    sabr_alpha: float
    sabr_rho: float
    sabr_volvol: float


class ListedTenorParamsSpline(ListedTenorParamsBase):
    spline_info: list[dict[str, float]]


ListedTenorParams = (
    ListedTenorParamsSVI | ListedTenorParamsSABR | ListedTenorParamsSpline
)


class ParamsQueryResult(QueryResult):
    params: str


class ParsedParamsQueryResult(QueryResult):
    params: list[ListedTenorParams]


class SmileQueryResult(QueryResult):
    smile: str


class MoneynessQueryResult(QueryResult):
    moneyness: str


AllQueryResults = ParamsQueryResult | MoneynessQueryResult | SmileQueryResult


class CalcTypeInputs(TypedDict):
    params_calc_types: list[ParamsCalcType]
    vol_surface_calc_types: list[VolSurfaceCalcType]
    smoothing_calc_types: list[SmoothingCalcType]
    aux_calc_types: list[AuxCalcType]


class ParamsCalcDfs(TypedDict):
    params: DataFrame


class ParamsCalcResult(BaseCalcResult):
    dfs: ParamsCalcDfs
    df_vol_matrix_pre: DataFrame


class DerivedCalcDfs(TypedDict):
    smile: DataFrame
    moneyness: DataFrame
    strike: NotRequired[DataFrame]


class DerivedCalcResult(BaseCalcResult):
    dfs: DerivedCalcDfs


class AuxCalcDfs(TypedDict, total=False):
    skew: DataFrame
    butterfly: DataFrame


class AuxCalcResult(BaseCalcResult):
    dfs: AuxCalcDfs


class FinalCalcDfs(TypedDict):
    params: DataFrame
    smile: DataFrame
    moneyness: DataFrame
    skew: NotRequired[DataFrame]
    butterfly: NotRequired[DataFrame]
    strike: NotRequired[DataFrame]


class FinalCalcResult(BaseCalcResult):
    dfs: FinalCalcDfs


class PreviousResults(TypedDict):
    df: Optional[DataFrame]


class SnapshotDateRange(TypedDict):
    start: float | int
    end: float | int


class SnapshotDates(TypedDict, total=False):
    # Snapshots loaded either based on a list of pre-computed timestamps
    # or based on a date range. These internal representations
    # don't seem to fit the `DateRange` under utils_aws.

    range: SnapshotDateRange
    timestamps: list[int]


class TenorType(Enum):
    LISTED_EXPIRY = "listed_expiry"
    ARBITRARY_EXPIRY = "arbitrary_expiry"
    STANDARD_CONSTANT_MATURITY = "standard_constant_maturity"
    ARBITRARY_CONSTANT_MATURITY = "arbitrary_constant_maturity"


@dataclass
class ExpiryArbFields:
    strikes: set[float] = field(default_factory=set)
    deltas: set[float] = field(default_factory=set)
    moneyness: set[float] = field(default_factory=set)
    listed_expiry: bool = False
    arbitrary_expiry: bool = False
    standard_constant_maturity: bool = False
    arbitrary_constant_maturity: bool = False
    iso_expiry: str = ""
    tenor_str: str = ""

    def merge(self, other: "ExpiryArbFields") -> "ExpiryArbFields":
        """
        Merge the current instance with another ExpiryArbFields instance.

        - If one instance is listed and the other is arbitrary, the result is considered listed and not arbitrary.
        - If one instance is arbitrary constant maturity and the other is standard constant maturity, the result is considered standard constant maturity and not arbitrary constant maturity.
        """

        new_listed_expiry = self.listed_expiry or other.listed_expiry
        new_standard_constant_maturity = (
            self.standard_constant_maturity or other.standard_constant_maturity
        )
        new_arbitrary_expiry = (
            False
            if new_listed_expiry
            else (self.arbitrary_expiry or other.arbitrary_expiry)
        )
        new_arbitrary_constant_maturity = (
            False
            if new_standard_constant_maturity
            else (
                self.arbitrary_constant_maturity
                or other.arbitrary_constant_maturity
            )
        )
        new_iso_expiry = self.iso_expiry or other.iso_expiry
        new_tenor_str = self.tenor_str or other.tenor_str

        # Special handling for listed and arbitrary expiries
        if (self.listed_expiry and other.arbitrary_expiry) or (
            self.arbitrary_expiry and other.listed_expiry
        ):
            assert (
                self.iso_expiry == other.iso_expiry
            ), f"Both objects are Expiries. Fields must be the same, {self.iso_expiry=}, {other.iso_expiry=}"
            new_listed_expiry = True
            new_arbitrary_expiry = False

        # Special handling for constant maturity combinations
        if (
            self.arbitrary_constant_maturity
            and other.standard_constant_maturity
        ) or (
            self.standard_constant_maturity
            and other.arbitrary_constant_maturity
        ):
            assert (
                self.tenor_str == other.tenor_str
            ), f"Both objects are constant maturires. Fields must be the same, {self.tenor_str=}, {other.tenor_str=}"
            new_standard_constant_maturity = True
            new_arbitrary_constant_maturity = False

        merged_instance = ExpiryArbFields(
            strikes=self.strikes.union(other.strikes),
            deltas=self.deltas.union(other.deltas),
            moneyness=self.moneyness.union(other.moneyness),
            listed_expiry=new_listed_expiry,
            arbitrary_expiry=new_arbitrary_expiry,
            standard_constant_maturity=new_standard_constant_maturity,
            arbitrary_constant_maturity=new_arbitrary_constant_maturity,
            iso_expiry=new_iso_expiry,
            tenor_str=new_tenor_str,
        )

        self._validate_item()

        return merged_instance

    def update(
        self, key: str | dict[str, Any], value: Optional[Any] = None
    ) -> None:
        """
        Updates one or more attributes if they exist and validates type compatibility.
        If a dictionary is provided as the 'key', it updates the attributes according to the dictionary.
        :param key: The attribute name to update, or a dictionary of attribute names and their new values.
        :param value: The new value to set for the specified attribute (if 'key' is a string).

        """

        if isinstance(key, dict):
            for k, v in key.items():
                self._update_single_attribute(k, v)
        else:
            self._update_single_attribute(key, value)

        self._validate_item()

    def _update_single_attribute(self, key: str, value: Any) -> None:
        """
        Updates an attribute if it exists and validates type compatibility.
        """
        if hasattr(self, key):
            current_value = getattr(self, key)
            if isinstance(current_value, set):
                if isinstance(
                    value, (set, list, tuple)
                ):  # Allow adding from any iterable
                    current_value.update(value)
                else:
                    raise ValueError(
                        f"Expected an iterable to update set for {key}, received {type(value).__name__}"
                    )
            else:
                if type(current_value) is not type(value):
                    # Check for exact type match; adjust according to your needs
                    raise ValueError(
                        f"Type mismatch for {key}: expected {type(current_value).__name__}, got {type(value).__name__}"
                    )
                setattr(self, key, value)
        else:
            raise AttributeError(
                f"{key} is not an attribute of {type(self).__name__}"
            )

    def _validate_item(self) -> None:

        assert not (
            self.listed_expiry and self.arbitrary_expiry
        ), "Both listed_expiry and arbitrary_expiry cannot be True at the same time."
        assert not (
            self.arbitrary_constant_maturity and self.standard_constant_maturity
        ), "Both arbitrary_constant_maturity and standard_constant_maturity cannot be True at the same time."

        if self.listed_expiry or self.arbitrary_expiry:
            assert (
                self.iso_expiry
            ), "For listed_expiry or arbitrary_expiry, iso_expiry must be set."
        if self.standard_constant_maturity or self.arbitrary_constant_maturity:
            assert (
                self.tenor_str
            ), "For standard_constant_maturity or arbitrary_constant_maturity, tenor_str must be set."
        # handling overlaping tenors
        if (
            self.listed_expiry
            and (
                self.arbitrary_constant_maturity
                or self.standard_constant_maturity
            )
        ) or (
            self.arbitrary_expiry
            and (
                self.arbitrary_constant_maturity
                or self.standard_constant_maturity
            )
        ):
            assert (
                self.iso_expiry and self.tenor_str
            ), "For overlapping expiry and maturity, both iso_expiry and tenor_str must be set."


class TenorExpiryArbFields(UserDict[float, ExpiryArbFields]):
    data: dict[float, ExpiryArbFields] = field(default_factory=dict)

    def add_expiry_arb_fields(
        self, tenor: float, new_field: ExpiryArbFields
    ) -> None:
        """
        Adds a new expiry arb field or merges it with an existing one and
        validates its entries
        """
        if tenor not in self.data:
            self.data[tenor] = new_field
        else:
            self.data[tenor] = self.data[tenor].merge(new_field)

        self.data[tenor]._validate_item()

    def sort_data(self) -> None:
        self.data = dict(sorted(self.data.items()))


class TimestampToTenorExpiryArbFields(UserDict[int, TenorExpiryArbFields]):
    data: dict[int, TenorExpiryArbFields] = field(default_factory=dict)

    def __getitem__(self, key: int) -> TenorExpiryArbFields:
        """
        Override to return sorted TenorExpiryArbFields.
        """
        if key in self.data:
            self.data[key].sort_data()
            return self.data[key]
        return TenorExpiryArbFields()


class CurrencyToTimestampTenorExpiryArbFields(
    UserDict[str, TimestampToTenorExpiryArbFields]
):
    data: dict[str, TimestampToTenorExpiryArbFields] = field(
        default_factory=dict
    )

    def __getitem__(self, key: str) -> TimestampToTenorExpiryArbFields:
        if key not in self.data:
            self.data[key] = TimestampToTenorExpiryArbFields()
        return self.data[key]


class ExchangeCurrencyToTimestampTenorExpiryArbFields(
    UserDict[str, CurrencyToTimestampTenorExpiryArbFields]
):
    data: dict[str, CurrencyToTimestampTenorExpiryArbFields] = field(
        default_factory=dict
    )

    def __getitem__(self, key: str) -> CurrencyToTimestampTenorExpiryArbFields:
        if key not in self.data:
            self.data[key] = CurrencyToTimestampTenorExpiryArbFields()
        return self.data[key]


Exchange = str
BaseAsset = str
Expiry = str
Strikes = set[float]
RawSuperExchangeMap = dict[Exchange, set[Exchange]]
ExchangeStrikeExpiryMap = dict[Exchange, dict[BaseAsset, dict[Expiry, Strikes]]]
TimestampToExchangeStrikeExpiryMap = dict[int, ExchangeStrikeExpiryMap]

ZscoreLimit = dict[str, int]


class CalcTypeFieldsDict(TypedDict):
    fields: list[str]
    aux_fields: NotRequired[list[str]]


ModelFieldProperties = dict[Model, CalcTypeFieldsDict]
CalcTypeProperties = dict[VolSurfaceCalcType, ModelFieldProperties]


class ZscoreDict(TypedDict, total=False):
    window: int
    span_smooth: dict[str, int]
    span_zscore: dict[str, int]
    adjust: bool
    limit: ZscoreLimit
    calc_type_properties: CalcTypeProperties


class ZscoreParams(TypedDict):
    window: int
    span_smooth: int
    span_zscore: int
    adjust: bool
    limit: int


class SmoothConfig(TypedDict):
    exchange: str
    model: Model
    freq: str
    zscore_details: ZscoreParams


class SmoothParamsDetails(TypedDict):
    round_to_seconds: NotRequired[int]
    zscore: ZscoreDict


class SmoothAndRecalibInfo(DerivedCalcDfs):
    params: DataFrame


# qualified_name_prefix ->  SmoothAndRecalibInfo
SmoothingAndEstimationResult = dict[str, SmoothAndRecalibInfo]


class ClosestTenorInfo(TypedDict):
    closest_tenor: int | float
    is_constant_tenor: bool
    closest_qualified_name: str


SmoothParamsDict = dict[str, SmoothParamsDetails]

SVICallable = Callable[
    [
        list[ListedTenorParamsSVI],  # List of parameters for each tenor
        str,  # Exchange name
        str,  # Currency
        TenorExpiryArbFields,  # Extra tenors information
    ],
    ParamsCalcResult,  # The result of parameters calculation
]
SABRCallable = Callable[
    [
        list[ListedTenorParamsSABR],  # List of parameters for each tenor
        str,  # Exchange name
        str,  # Currency
        TenorExpiryArbFields,  # Extra tenors information
    ],
    ParamsCalcResult,  # The result of parameters calculation
]
SplineCallable = Callable[
    [
        list[ListedTenorParamsSpline],  # List of parameters for each tenor
        str,  # Exchange name
        str,  # Currency
        TenorExpiryArbFields,  # Extra tenors information
    ],
    ParamsCalcResult,  # The result of parameters calculation
]

ParamCalcFnsTypes = dict[str, SVICallable | SABRCallable | SplineCallable]


class ParamsCalcData(TypedDict):
    params: list[ParsedParamsQueryResult]
    previous_results: PreviousResults


@dataclass
class ProcessDataSnapshot:
    params: list[ParsedParamsQueryResult]
    calc_type_inputs: CalcTypeInputs
    exchange_curr_timestamp_to_expiry_info: (
        ExchangeCurrencyToTimestampTenorExpiryArbFields
    )
    previous_results: PreviousResults
    lookback_end_iso: str


class OptionContractDetails(TypedDict):
    qualified_name: str
    expiry: str
    instrument_name: str
    strike: float | None
    baseAsset: str
    listing: str
    availableSince: str
    quoteAsset: NotRequired[str]


class SmileCtInfo(TypedDict):
    params_df: pd.DataFrame
    earliest_current_result: pd.Series  # type: ignore


class SabrParamsVectorized(TypedDict):
    sabr_alpha: NDArrayFloat64
    sabr_rho: NDArrayFloat64
    sabr_volvol: NDArrayFloat64


class SviParamsVectorized(TypedDict):
    svi_a: NDArrayFloat64
    svi_b: NDArrayFloat64
    svi_rho: NDArrayFloat64
    svi_m: NDArrayFloat64
    svi_sigma: NDArrayFloat64


CubicSplineVectorized = list[CubicSpline]


ModelParamsBase = SabrParams | SviParams | CubicSpline
ModelParamsVectorized = (
    SabrParamsVectorized | SviParamsVectorized | CubicSplineVectorized
)
ModelParamsTypes = ModelParamsBase | ModelParamsVectorized

# issue_type -> qn -> issue details
ArbitraryLookbackVolCalcDetails = DefaultDict[
    str, dict[str, str | dict[float, str] | int]
]

# exchange -> curr -> iso_expiry -> ExpiryArbFields
SubscribedArbitraryInfo = dict[str, dict[str, dict[str, ExpiryArbFields]]]


class ProcessDataResult(TypedDict):
    calc_output: pd.DataFrame
    lookback_result: PreviousResults


class EmptyCalcTypeError(Exception):
    pass
