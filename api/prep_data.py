import logging
from collections import defaultdict
from datetime import datetime, timezone
from typing import Any, Literal, cast

import pandas as pd
import utils_general
from calc_helpers import get_tenors
from constants import (
    EXPIRY_PRECISION,
    LOOKBACK_COLUMN_NAME,
    PARAMS_CALC_TYPES,
    TENOR_PRECISION,
    VOLSURFACE_CALC_TYPES,
)
from lambda_types import (
    AllQueryResults,
    AuxCalcType,
    CalcType,
    CalcTypeInputs,
    ExchangeCurrencyToTimestampTenorExpiryArbFields,
    ExchangeStrikeExpiryMap,
    ExpiryArbFields,
    ListedTenorParams,
    OptionContractDetails,
    ParamsCalcData,
    ParamsCalcType,
    ParamsQueryResult,
    ParsedParamsQueryResult,
    PreviousResults,
    ProcessDataSnapshot,
    RawSuperExchangeMap,
    SmoothingCalcType,
    SubscribedArbitraryInfo,
    TenorMode,
    TenorType,
    TimestampToExchangeStrikeExpiryMap,
    TimestampToTenorExpiryArb<PERSON><PERSON>s,
    VolSurfaceCalcType,
)
from utils.common import (
    round_value,
    set_qualified_name_timestamp_index,
)
from utils_general import (
    convert_expiry_to_days,
    convert_maturity_string_to_day_or_minutes,
    convert_tenor_days_to_constant_maturity,
    get_qfn_and_version,
    is_constant_maturity,
    json_loads,
    to_iso,
)


def _should_include_instrument(
    i: OptionContractDetails, current_ts: int
) -> bool:
    """
    Determine whether the instrument should be included based on its listing date and tenor.

    :param i: An instrument (option contract details).
    :param current_ts: The current timestamp in nanoseconds.
    :returns: True if the instrument should be included; otherwise, False.
    """
    # Check for a listing date from either 'listing' or 'availableSince'
    listing_field: Literal["listing", "availableSince"] | None = None
    if "listing" in i:
        listing_field = "listing"
    elif "availableSince" in i:
        listing_field = "availableSince"

    if listing_field is not None and listing_field:
        listing_ts = int(
            utils_general.from_iso(i[listing_field]).timestamp() * 1e9
        )
        if listing_ts > current_ts:
            # Skip instruments whose listing date is in the future (useful for backfilling)
            utils_general.log_bsdebug(
                "Skipping instrument as it is listed in the future"
            )
            return False
    else:
        logging.warning(
            "Encountered instrument without a `listing` or `availableSince` date. Processing it regardless"
        )

    # Compute the tenor and ensure expired instruments are not included for large backfills
    tenor = round_value(
        convert_expiry_to_days(f"{i['expiry'][:19]}Z", current_ts),
        TENOR_PRECISION,
    )

    return bool(tenor > 0)


def _get_exchange_strike_map(
    instruments: dict[str, OptionContractDetails],
    exchange_map: RawSuperExchangeMap,
    timestamps: set[int],
) -> TimestampToExchangeStrikeExpiryMap:
    """
    Get exchange strike map with timestamp awareness.

    :param instruments: Dictionary of instruments with their details
    :param exchange_map: Mapping of exchanges to their super exchanges
    :param timestamps: Set of timestamps to process (nanoseconds)
    :param snapshot_ts: Optional specific timestamp to use (nanoseconds)

    :returns: Dictionary keyed by timestamps, each containing the exchange strike map
    """

    # Result dictionary keyed by timestamps
    result_by_timestamp: TimestampToExchangeStrikeExpiryMap = {}

    # Process each timestamp
    for current_ts in sorted(timestamps):
        exp_strike_map: ExchangeStrikeExpiryMap = defaultdict(
            lambda: defaultdict(lambda: defaultdict(set))
        )

        for i in instruments.values():
            if not _should_include_instrument(i, current_ts):
                continue

            # Process the instrument
            exch = i["qualified_name"].split(".")[0]
            exchanges_to_populate = exchange_map.get(exch, set())

            for ex in exchanges_to_populate:
                strike_set = exp_strike_map[ex][i["baseAsset"]][
                    f"{i['expiry'][:19]}Z"
                ]
                strike = i.get("strike")
                if strike:
                    strike_set.add(strike)

        result_by_timestamp[current_ts] = exp_strike_map

    return result_by_timestamp


def get_calculated_data_from_raw_data(
    raw_params_data: list[ParamsQueryResult],
    lookback_data: list[AllQueryResults],
) -> ParamsCalcData:
    lookback_df = pd.DataFrame(lookback_data)
    if not lookback_df.empty:
        lookback_df.loc[:, LOOKBACK_COLUMN_NAME] = True
        lookback_df = set_qualified_name_timestamp_index(df=lookback_df)

    previous_results: PreviousResults = PreviousResults(
        df=lookback_df if not lookback_df.empty else None
    )
    params_data: list[ParsedParamsQueryResult] = [
        {**rp, "params": json_loads(rp["params"])} for rp in raw_params_data
    ]
    return {
        "params": params_data,
        "previous_results": previous_results,
    }


def _is_valid_listed(
    listed: ListedTenorParams, ccy: str, exchange: str
) -> bool:
    # Ensure that both expiry and atm_vol exist
    if listed.get("expiry") and listed.get("atm_vol"):
        return True
    logging.error(
        f"Found bad calibrated listed expiry, removing: {ccy} {exchange} {listed}"
    )
    return False


def prep_data_lambda(
    raw_params_data: list[ParamsQueryResult],
    lookback_data: list[AllQueryResults],
    instruments: dict[str, OptionContractDetails],
    tenor_mode: TenorMode,
    calc_types: list[CalcType],
    estimate_params: bool,
    freq: str,
    start: str,
    end: str,
    exchange_map: RawSuperExchangeMap,
    include_listed_expiries: bool,
    time_condition: Any | None = None,
) -> ProcessDataSnapshot:
    calculated_data = get_calculated_data_from_raw_data(
        raw_params_data=raw_params_data, lookback_data=lookback_data
    )
    return prep_data(
        calculated_data=calculated_data,
        instruments=instruments,
        tenor_mode=tenor_mode,
        calc_types=calc_types,
        estimate_params=estimate_params,
        freq=freq,
        exchange_map=exchange_map,
        include_listed_expiries=include_listed_expiries,
        time_condition=time_condition,
        start=start,
        end=end,
    )


def prep_data(
    calculated_data: ParamsCalcData,
    instruments: dict[str, OptionContractDetails],
    tenor_mode: TenorMode,
    calc_types: list[CalcType],
    estimate_params: bool,
    freq: str,
    exchange_map: RawSuperExchangeMap,
    include_listed_expiries: bool,
    start: str = "",
    end: str = "",
    subscribed_arbitrary_info: SubscribedArbitraryInfo | None = None,
    time_condition: Any | None = None,
    snapshot_ts: int | None = None,
    **kwargs: Any,  # Added for compatibility with blockstream utils
) -> ProcessDataSnapshot:
    assert calculated_data["params"], "empty params, skipping calculation"

    exchange_curr_timestamp_to_expiry_info = (
        ExchangeCurrencyToTimestampTenorExpiryArbFields()
    )

    # The end of the lookback is the same start of the chunk we are processing
    lookback_end_iso = start if not snapshot_ts else to_iso(snapshot_ts)

    if time_condition is None:
        time_condition = {}

    params = calculated_data["params"]
    if time_condition:
        params = cast(
            list[ParsedParamsQueryResult],
            list(
                filter(
                    lambda p: datetime.fromtimestamp(
                        p["timestamp"] / 1e9, tz=timezone.utc
                    ).hour
                    == time_condition["hour"],
                    params,
                )
            ),
        )

    if not snapshot_ts:
        # Generate timestamps to process based on the start time and frequency
        timestamps_to_process = utils_general.generate_timestamps(
            start=start,
            end=end,
            periods=int(freq[:-1]),
            interval="hour" if freq[-1] == "h" else "minute",
            time_condition=time_condition,
        )
    else:
        timestamps_to_process = {snapshot_ts}

    timestamp_exchange_ccy_expiry_strike_map = _get_exchange_strike_map(
        instruments=instruments,
        exchange_map=exchange_map,
        timestamps=timestamps_to_process,
    )

    calc_type_inputs = _prepare_calc_types(
        calc_types=calc_types,
        timestamp_exchange_ccy_expiry_strike_map=timestamp_exchange_ccy_expiry_strike_map,
        subscribed_arbitrary_info=subscribed_arbitrary_info,
    )

    if estimate_params:
        if not all(
            item in calc_type_inputs["vol_surface_calc_types"]
            for item in ["smile", "moneyness"]
        ):
            calc_type_inputs["vol_surface_calc_types"] = cast(
                list[VolSurfaceCalcType],
                {*calc_type_inputs["vol_surface_calc_types"]}.union(
                    VOLSURFACE_CALC_TYPES
                ),
            )

    if not include_listed_expiries:
        return ProcessDataSnapshot(
            params=params,
            calc_type_inputs=calc_type_inputs,
            exchange_curr_timestamp_to_expiry_info=exchange_curr_timestamp_to_expiry_info,
            previous_results=calculated_data["previous_results"],
            lookback_end_iso=lookback_end_iso,
        )

    for snap in params:
        _, qfn_tokens = get_qfn_and_version(snap["qualified_name"])
        timestamp = snap["timestamp"]
        exchange, ccy = qfn_tokens[0], qfn_tokens[2]

        timestamp_tenor_expiry_info = exchange_curr_timestamp_to_expiry_info[
            exchange
        ][ccy]
        tenor_expiry_info = timestamp_tenor_expiry_info[timestamp]

        # Loop through the strike map instead of params from snapshot as expiries
        # could be filtered out in modelParametersCalc
        for expiry, strikes in timestamp_exchange_ccy_expiry_strike_map[
            timestamp
        ][exchange][ccy].items():
            tenor = round_value(
                convert_expiry_to_days(expiry, timestamp), TENOR_PRECISION
            )

            arb_fields = ExpiryArbFields(
                strikes=strikes,
                moneyness=set(),
                deltas=set(),
                listed_expiry=True,
                iso_expiry=expiry,
            )
            tenor_expiry_info.add_expiry_arb_fields(tenor, arb_fields)

        snap["params"] = [
            listed
            for listed in snap["params"]
            if _is_valid_listed(
                listed, ccy, exchange
            )  # Filter out params outputs with Nones
        ]

        # Validate we've got all params in listed bundle
        for listed in json_loads(snap["params"]):
            listed_expiry = round_value(listed["expiry"], EXPIRY_PRECISION)
            tenor = round_value(
                convert_expiry_to_days(listed_expiry, listed["timestamp"]),
                TENOR_PRECISION,
            )

            if tenor in tenor_expiry_info:
                continue
            # This should never occur - but safety first
            logging.warning(
                f"tenor={tenor} expiry={listed['expiry']} ({listed['underlying_index']}) not found in tenor_expiry_info (keys={tenor_expiry_info.keys()}). Inserting tenor into map without catalog strikes."
            )
            arb_fields = ExpiryArbFields(
                strikes=set(),
                moneyness=set(),
                deltas=set(),
                listed_expiry=True,
                iso_expiry=utils_general.convert_year_expiry_to_iso_stamp(
                    listed_expiry, listed["timestamp"]
                ),
            )
            tenor_expiry_info.add_expiry_arb_fields(tenor, arb_fields)
        exchange_curr_timestamp_to_expiry_info[exchange][ccy][
            timestamp
        ] = tenor_expiry_info

    for (
        exchange,
        curr_timestamp_to_expiry_info,
    ) in exchange_curr_timestamp_to_expiry_info.items():
        for (
            currency,
            timestamp_to_expiry_info,
        ) in curr_timestamp_to_expiry_info.items():
            for (
                _timestamp,
                _tenor_expiry_info,
            ) in timestamp_to_expiry_info.items():
                # Used to decide whether tenor is arbitrary or not, specifically in the live system where we load from db.
                stored_tenor_data = get_tenors(
                    freq=freq if freq != "live" else "1m",
                    mode=tenor_mode,
                    exchange=exchange,
                    currency=currency,
                )
                tenor_grid = get_tenors(
                    freq=freq,
                    mode=tenor_mode,
                    exchange=exchange,
                    currency=currency,
                )
                for tenor in tenor_grid:
                    is_in_scheduled_system = tenor in stored_tenor_data
                    arb_fields = ExpiryArbFields(
                        strikes=set(),
                        moneyness=set(),
                        deltas=set(),
                        arbitrary_constant_maturity=not is_in_scheduled_system,
                        standard_constant_maturity=is_in_scheduled_system,
                        tenor_str=convert_tenor_days_to_constant_maturity(
                            tenor
                        ),
                    )
                    _tenor_expiry_info.add_expiry_arb_fields(
                        round_value(tenor, TENOR_PRECISION), arb_fields
                    )

    if subscribed_arbitrary_info and snapshot_ts:
        for _ex, curr_tenor_arb_fields in subscribed_arbitrary_info.items():
            for (
                currency,
                _tenor_arb_fields,
            ) in curr_tenor_arb_fields.items():
                ts_tenor_expiry_info = exchange_curr_timestamp_to_expiry_info[
                    _ex
                ].get(currency, TimestampToTenorExpiryArbFields())
                expiry_info = ts_tenor_expiry_info[snapshot_ts]

                # sister code for the below block in stream_flex_function.get_expiries_in_days() where all available
                # subscription messages are retrieved and shaped intp an ExpiryArbFields object
                for _tenor, _arb_fields in _tenor_arb_fields.items():
                    if is_constant_maturity(_tenor):
                        _arb_fields.update(
                            {
                                TenorType.ARBITRARY_CONSTANT_MATURITY.value: True,
                                "tenor_str": _tenor,
                            }
                        )
                        exp_days = round_value(
                            convert_maturity_string_to_day_or_minutes(
                                _tenor, "days"
                            ),
                            TENOR_PRECISION,
                        )
                        expiry_info.add_expiry_arb_fields(exp_days, _arb_fields)
                    else:
                        _arb_fields.update(
                            {
                                TenorType.ARBITRARY_EXPIRY.value: True,
                                "iso_expiry": _tenor,
                            }
                        )
                        exp_days = convert_expiry_to_days(_tenor, snapshot_ts)
                        if exp_days > 0:
                            exp_days = round_value(exp_days, TENOR_PRECISION)
                            expiry_info.add_expiry_arb_fields(
                                exp_days, _arb_fields
                            )

                exchange_curr_timestamp_to_expiry_info[_ex][currency][
                    snapshot_ts
                ] = expiry_info

    if snapshot_ts:
        violations = []
        for (
            exchange,
            curr_timestamp_to_expiry_info,
        ) in exchange_curr_timestamp_to_expiry_info.items():
            for (
                currency,
                timestamp_to_expiry_info,
            ) in curr_timestamp_to_expiry_info.items():
                if len(list(timestamp_to_expiry_info.keys())) > 1:
                    violations.append((exchange, currency))

        if violations:
            raise ValueError(
                f"More than one snapshot found in the live system. {violations=}"
            )

    return ProcessDataSnapshot(
        params=params,
        calc_type_inputs=calc_type_inputs,
        exchange_curr_timestamp_to_expiry_info=exchange_curr_timestamp_to_expiry_info,
        previous_results=calculated_data["previous_results"],
        lookback_end_iso=lookback_end_iso,
    )


def _prepare_calc_types(
    calc_types: list[CalcType],
    timestamp_exchange_ccy_expiry_strike_map: TimestampToExchangeStrikeExpiryMap,
    subscribed_arbitrary_info: SubscribedArbitraryInfo | None,
) -> CalcTypeInputs:
    """
    Prepare calculation types considering all timestamps.

    :param calc_types: List of calculation types
    :param timestamp_exchange_ccy_expiry_strike_map: Timestamp-indexed exchange strike expiry map
    :param subscribed_arbitrary_info: Information about subscribed arbitrary values

    :returns: Dictionary of calculation type inputs
    """
    has_arbitrary_subscribed_strike = False
    has_any_listed_strikes = False

    if subscribed_arbitrary_info is not None and subscribed_arbitrary_info:
        has_arbitrary_subscribed_strike = any(
            any(
                any(
                    bool(arb_fields.strikes)
                    for arb_fields in tenor_arb_fields.values()
                )
                for tenor_arb_fields in currency_arb_fields.values()
            )
            for currency_arb_fields in subscribed_arbitrary_info.values()
        )

    # Check for listed strikes across all timestamps
    if timestamp_exchange_ccy_expiry_strike_map:
        has_any_listed_strikes = any(
            strikes
            for timestamp, exchange_map in timestamp_exchange_ccy_expiry_strike_map.items()
            for base_asset_map in exchange_map.values()
            for expiry_map in base_asset_map.values()
            for strikes in expiry_map.values()
        )

    if (
        not (has_any_listed_strikes or has_arbitrary_subscribed_strike)
        and "strike" in calc_types
    ):
        logging.warning(
            "No strikes included in any listed expiries across any timestamps. Removing 'strike' from input calc_types"
        )
        calc_types.remove("strike")

    params_calc_types = cast(
        list[ParamsCalcType],
        list({*calc_types}.intersection(PARAMS_CALC_TYPES)),
    )
    vol_surface_calc_types = cast(
        list[VolSurfaceCalcType],
        list({*calc_types}.intersection(VOLSURFACE_CALC_TYPES)),
    )
    smoothing_calc_types = cast(
        list[SmoothingCalcType],
        list({*vol_surface_calc_types}.union(params_calc_types)),
    )
    aux_calc_types = cast(
        list[AuxCalcType],
        list({*calc_types} - {*smoothing_calc_types}),
    )
    assert all(
        "smile" in calc_types
        for _ct in aux_calc_types
        if _ct in ["butterfly", "skew"]
    ), "Smile calc type is required to calculate butterfly or skew"

    return {
        "params_calc_types": params_calc_types,
        "vol_surface_calc_types": vol_surface_calc_types,
        "smoothing_calc_types": smoothing_calc_types,
        "aux_calc_types": aux_calc_types,
    }
