import multiprocessing
import os
from datetime import datetime, timezone
from typing import Optional

import boto3
from cachetools import TTLCache, cached
from lambda_types import (
    ParamsCalcType,
    SmoothingCalcType,
    TenorType,
    VolSurfaceCalcType,
)
from mypy_boto3_s3 import S3Client as S3ClientSync
from utils_aws import load_ssm_params
from utils_calc.rates import DomesticRates

cache = TTLCache(maxsize=100, ttl=14400)  # type: ignore

ALL_START = datetime(2020, 1, 1, tzinfo=timezone.utc)
S3: S3ClientSync = boto3.client("s3")
READ_OPERATIONS_LIMIT = 50
NUM_WORKERS = multiprocessing.cpu_count() * 2

LOOKBACK_COLUMN_NAME = "from_lookback"
NEXT_ITERATION_LOOKBACK_NAME = "lookback_for_next_iteration"
SPIKED_PARAMS_COLUMN_NAME = "contains_recalib_spike"
IDX_SMOOTHING_COUNT_LIMIT = 50
MIN_SMOOTHING_LOOKBACK_RATIO = 0.75
ZSCORE_RECALIBRATION_MULTIPLIER = 1.2
# Determines the minimum ratio of smoothed/unsmoothed datapoints in a lookback
# before we enter a smoothing backstopping mode and start smoothing less agressively
MAX_LOOKBACK_SMOOTHED_RATIO = 0.5
# Once in the smoothing backstopping mode, we scale the smoothing span parameter by a
# maximum of MAX_SMOOTH_SPAN_SCALING when the entire lookback is smoothed
MAX_SMOOTH_SPAN_SCALING = 0.5

BLOCKSTREAM_ENDPOINT: Optional[str] = os.environ.get("BLOCKSTREAM_ENDPOINT")
METRIC_S = int(os.environ.get("METRIC_S", 60))


TARGET_SNAPSHOTS: list[int] = []
TOL = 1e-18

# Expiry precision must be higher than tenor precision. lower expiry precisions
# will lead to inconsistent ISO expiries being generated
EXPIRY_PRECISION = 14  # Encapsulates seconds 3.1709791983765e-8 years = 1 sec
TENOR_PRECISION = 7  # Encapsulates seconds, 1.15740740740741e-5 days = 1 sec
EXPIRY_TOLERANCE = 5e-10

TWO_MINUTES_IN_YEARS = 2.0 / (60 * 24 * 365)  # 2 minutes in years

SPLINE_EXTRAPOLATION_DELTA = 0.005

ECS_CLUSTER: str = os.environ.get("ECS_CLUSTER", "")
ECS_SERVICE: str = os.environ.get("ECS_SERVICE", "")

SHORT_EXPIRY_CUT_OFF = 5.0 / 365.0
CALIBRATION_DELTA_BOUNDARY_FOR_LONG_EXPIRY = 0.10
CALIBRATION_DELTA_BOUNDARY_FOR_SHORT_EXPIRY = 0.20

R2_SMOOTHCALIB_COLUMN_NAME = "R2_smoothcalib"
CALIB_R2_THRESHOLD = 0.7

VOLSURFACE_CALC_TYPES: set[VolSurfaceCalcType] = {
    "smile",
    "moneyness",
    "strike",
}
PARAMS_CALC_TYPES: set[ParamsCalcType] = {"params"}
SMOOTHING_CALC_TYPES: set[SmoothingCalcType] = (
    VOLSURFACE_CALC_TYPES | PARAMS_CALC_TYPES
)
MS_PER_DAY = 86400000

OUTPUT_COL_SCALING_FACTOR = 100

# This will likely be seen if the svi_b is at the bounds.
# Occurs during tenor interpolation if the upper bordering expiry has svi_b at the bounds
BAD_MONEYNESS_CUTOFF_RATIO = 1e5

UNIX_MINUTE_NS = 60 * 1e9
UNIX_HOUR_NS = 60 * UNIX_MINUTE_NS

TENOR_TYPES: list[TenorType] = list(TenorType)

CALC_TYPE_TO_COL_SPACE: dict[VolSurfaceCalcType, str] = {
    "smile": "delta",
    "moneyness": "money",
    "strike": "strike",
}

COL_SPACE_TO_OUTPUT_SCALING_FACTOR: dict[str, int] = {
    "delta": 100,
    "money": 100,
    "strike": 1,
}


@cached(cache)
def load_domestic_rates() -> DomesticRates:
    rates = load_ssm_params(
        {
            "DOMESTIC_RATES": "/data/interest-rates/domestic",
        }
    )["DOMESTIC_RATES"]
    return DomesticRates(raw_rates=rates)
