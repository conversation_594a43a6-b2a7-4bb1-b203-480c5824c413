import logging
from datetime import datetime, timezone
from typing import Optional, cast

import pandas as pd
import utils_general
from constants import (
    LOOKBACK_COLUMN_NAME,
    MIN_SMOOTHING_LOOKBACK_RATIO,
)
from lambda_types import (
    PreviousResults,
    SmoothingCalcType,
    SnapshotDates,
    TenorType,
)
from retrieve_data import retrieve_data_for_qns
from utils.common import (
    activate_tenor_types,
    concat_lookback,
    remove_nan_columns_from_df,
    set_qualified_name_timestamp_index,
)
from utils.lookback import (
    determine_closest_tenor,
    extract_constant_tenors_from_maturities,
    extract_listed_params_from_df,
    filter_qualified_names,
    generate_lookback_timestamps,
    get_calc_type_qns_from_qualified_name_prefix,
    get_qualified_name_prefixs_from_qns,
)
from utils_calc import TENORS_DAYS_STANDARD


def lookback_from_db(
    snapshot_dates: SnapshotDates,
    qualified_names: list[str],
    debug: bool = False,
    consistent_read: bool = False,
) -> pd.DataFrame:
    """
    Preloads calculated data preceding the start of the calculated window.
    This additional data can optionally be included in the result set
    and be used by later steps for smoothing (currently done by s3saver)

    :param snapshot_dates: Either date range or list of timestamps
    :param qualified_names: list of qualified names (based on calc results)
    :param debug: Enable debug mode
    :param consistent_read: Make consistent reads when retrieving data
    :return:
    """

    logging.info(f"Preload window for {len(qualified_names)} QNs from DB")

    retrieval_start = datetime.now(tz=timezone.utc).timestamp()
    lookback_df = pd.DataFrame(
        retrieve_data_for_qns(
            snapshot_dates=snapshot_dates,
            qualified_names=qualified_names,
            debug=debug,
            consistent_read=consistent_read,
        )
    )

    if lookback_df.empty:
        logging.warning(
            f"Retrieved lookback window was empty: {snapshot_dates}"
        )
    else:
        lookback_df.loc[:, LOOKBACK_COLUMN_NAME] = True

    retrieval_end = datetime.now(tz=timezone.utc).timestamp()
    logging.info(
        f"Retrieved lookback data in {(retrieval_end - retrieval_start):.2f}s",
    )

    return lookback_df


def lookback_from_previous_df(
    lookback_df: pd.DataFrame,
    snapshot_dates: SnapshotDates,
    qualified_names: list[str],
    calc_types: list[SmoothingCalcType],
) -> pd.DataFrame:
    if lookback_df.empty:
        logging.info("Lookback df empty, not concatenating")

        # Return empty df for signature compatibility
        return lookback_df

    if "range" in snapshot_dates:
        snapshot_range = snapshot_dates["range"]
        lookback_slice = lookback_df[
            lookback_df["timestamp"] >= snapshot_range["start"]
        ]

    elif "timestamps" in snapshot_dates:
        # Unless casting is done, filtering may not work due to float errors
        # Could dig deeper for a better solution
        lookback_df["timestamp"] = lookback_df["timestamp"].astype(int)
        lookback_slice = lookback_df[
            lookback_df["timestamp"].isin(snapshot_dates["timestamps"])
        ]

    else:
        raise NotImplementedError(
            "Only 'range' and 'timestamps' based lookback is supported"
        )

    listed_df = extract_listed_params_from_df(lookback_slice, calc_types)
    lookback_slice = pd.concat([lookback_slice, listed_df])

    # Historic data can be more comprehensive than current data
    # if the include_listed_expiries flag is true, all available
    # listed expires (+expiries currently subscribed, if in VSF) will be available
    # in `qualified_names`
    lookback_slice = lookback_slice[
        lookback_slice["qualified_name"].isin(qualified_names)
    ]

    logging.info(
        f"Passed in lookback df: {lookback_df.shape}, sliced: {lookback_slice.shape}",
    )
    if lookback_slice.shape[0] != 0:
        lookback_slice.loc[:, LOOKBACK_COLUMN_NAME] = True
    else:
        logging.warning("No relevant qfn's found in lookback_df")
    return lookback_slice


def results_to_historic_qualified_names(
    current_result: pd.DataFrame,
    freq: str,
    include_listed_expiries: bool,
    scheduled_version: str = "",
) -> Optional[tuple[list[str], list[str]]]:
    # Load historic data (for smoothing) for qualified names in current result,
    # Ensure that the qualified names are in the correct frequency (live data not stored)

    def _filter_for_db_qns(qn_list: list[str]) -> list[str]:
        def is_db_qn(qn: str) -> bool:
            if ".listed." in qn:
                return True
            standard_or_arb_maturity = qn.split(".")[-3]
            return (
                utils_general.string_to_decimal(standard_or_arb_maturity)[0]
                in TENORS_DAYS_STANDARD
            )

        non_listed_or_arb_exp_qns = [qn for qn in qn_list if ":" not in qn]
        db_qns = {qn for qn in non_listed_or_arb_exp_qns if is_db_qn(qn)}

        return sorted(db_qns)

    if "qualified_name" not in current_result.columns:
        return None

    qn_token_cols = [
        "exchange",
        "option",
        "ccy",
        "model",
        "date_time",
        "freq",
        "calc_type",
    ]
    if scheduled_version:
        qn_token_cols.insert(0, "version")

    updated_qualified_names = [
        qn.replace(".live.", f".{freq}.")
        for qn in current_result["qualified_name"].unique()
    ]
    combined_qualified_names = []

    if include_listed_expiries:
        split_df = current_result["qualified_name"].str.split(".", expand=True)
        split_df.columns = pd.Index(qn_token_cols)
        # drop datetime as this shows is either the tenor, or expiry date (listed expiries)
        # and is not needed to fetch listed expiries which have .listed instead
        unique_combinations = split_df.drop(
            columns=["date_time"]
        ).drop_duplicates()
        # construct new listed qualified_names from existing qualified_names
        unique_combinations["model"] = unique_combinations["model"] + ".listed"
        unique_combinations.loc[
            unique_combinations["freq"] == "live", "freq"
        ] = freq
        # combine the split qn tokens to construct unique listed qualified_names
        combined_qualified_names = (
            unique_combinations.apply(".".join, axis=1).unique().tolist()
        )

    full_qn_list = sorted(
        set(combined_qualified_names + updated_qualified_names)
    )
    db_qns = _filter_for_db_qns(full_qn_list)

    return full_qn_list, db_qns


def lookback_and_append(
    previous_results: PreviousResults,
    current_result: pd.DataFrame,
    end_str: str,
    interval: str,
    freq: str,
    lookback_window_size: int,
    include_listed_expiries: bool,
    calc_types: list[SmoothingCalcType],
    debug: bool = False,
    consistent_read: bool = False,
    scheduled_version: str = "",
) -> pd.DataFrame:

    # All timestamps of lookback data
    snapshot_timestamps = list(
        generate_lookback_timestamps(end_str, lookback_window_size, interval)
    )
    snapshot_dates: SnapshotDates = {
        "range": {
            "end": max(snapshot_timestamps),
            "start": min(snapshot_timestamps),
        }
    }

    if current_result.empty:
        # If the results are empty, lookback is not possible (as that would rely on qualified names)
        logging.warning("No data retrieved/provided, skipping lookback")
        return current_result

    elif previous_results["df"] is not None:

        previous_results["df"].reset_index(inplace=True)
        # Cache is warm (live or backfill_run)

        # should contain listed_qns and arbitrary qn's for VSF

        qualified_name_prefixs_in_current_result, filtered_lookback_slice = (
            get_warm_cache_data(
                previous_results=previous_results,
                current_result=current_result,
                calc_types=calc_types,
                snapshot_timestamps=snapshot_timestamps,
            )
        )

        # Slice and update cache to remove stale items, prevent memory leak
        previous_results["df"] = filtered_lookback_slice[
            filtered_lookback_slice["timestamp"] >= min(snapshot_timestamps)
        ]
    else:
        # Cache is cold, DB lookup
        qualified_name_prefixs_in_current_result, filtered_lookback_slice = (
            get_cold_db_data(
                current_result=current_result,
                include_listed_expiries=include_listed_expiries,
                calc_types=calc_types,
                snapshot_dates=snapshot_dates,
                snapshot_timestamps=snapshot_timestamps,
                debug=debug,
                historic_freq=freq,
                consistent_read=consistent_read,
                scheduled_version=scheduled_version,
            )
        )

    current_result.loc[:, LOOKBACK_COLUMN_NAME] = False
    current_and_lookback_df = pd.concat(
        [filtered_lookback_slice, current_result], ignore_index=True
    )
    current_and_lookback_df["qualified_name_prefix"] = (
        get_qualified_name_prefixs_from_qns(
            current_and_lookback_df["qualified_name"]
        )
    )

    # conditions
    is_listed_expiry = current_and_lookback_df[
        TenorType.LISTED_EXPIRY.value
    ].eq(True)
    is_arbitrary_expiry = current_and_lookback_df[
        TenorType.ARBITRARY_EXPIRY.value
    ].eq(True)
    is_arbitrary_constant_maturity = current_and_lookback_df[
        TenorType.ARBITRARY_CONSTANT_MATURITY.value
    ].eq(True)

    all_available_listed_expiries_df = current_and_lookback_df[
        is_listed_expiry & ~is_arbitrary_expiry
    ]

    lookback_dfs_for_listed_exp_smoothing: list[pd.DataFrame] = []
    lookback_dfs_for_arbitrary_value_smoothing: list[pd.DataFrame] = []
    if include_listed_expiries:
        """
        This block of code assumes the presence of calc_type suffixed listed expiry qualified_names.

        """

        qns_in_curr_result = get_calc_type_qns_from_qualified_name_prefix(
            calc_types=calc_types,
            qualified_name_prefixs=qualified_name_prefixs_in_current_result,
        )

        lstd_expiries_df_for_curr_result = current_and_lookback_df[
            current_and_lookback_df["qualified_name"].isin(
                qns_in_curr_result  # excludes potentially expired qns that are fetched from the lookback.
            )
            & is_listed_expiry
            & ~is_arbitrary_expiry
        ]

        # we have a small history for this listed expiry. so we need to
        # append the closest listed expiry to the history to make it complete.
        # MIN_SMOOTHING_LOOKBACK_RATIO * window is the minimum number of datapoints
        # that are needed for smoothing
        lookbck_needed_lstd_df = filter_qualified_names(
            df=lstd_expiries_df_for_curr_result,
            threshold=MIN_SMOOTHING_LOOKBACK_RATIO * lookback_window_size,
            filter_type="below_threshold",
        )

        if not lookbck_needed_lstd_df.empty:
            lookback_dfs_for_listed_exp_smoothing = _populate_lookback_for_listed_expiries(
                lookbck_needed_lstd_df=lookbck_needed_lstd_df,
                current_and_lookback_df=current_and_lookback_df,
                curr_and_lookback_lstd_exps=all_available_listed_expiries_df,
                lookback_window_size=lookback_window_size,
            )

    if is_arbitrary_expiry.any() or is_arbitrary_constant_maturity.any():
        """
        This block of code assumes that arbitrary expiries/maturities will only ever have .params suffixed
        qualified names (safe). We do this because we can only guarantee the availability of this
        qualified_name on each run. The 'calc_type' suffixes are calculated downstream and may not
        be available yet (in the case when we are processing a new subscription).

        """

        def _generate_lookback_df_helper(
            arb_col_flag: pd.Series,  # type: ignore
        ) -> list[pd.DataFrame]:
            arbitrary_df = current_and_lookback_df[arb_col_flag]
            return _generate_lookback_for_arbitrary_value(
                current_and_lookback_df=current_and_lookback_df,
                all_available_listed_expiries_df=all_available_listed_expiries_df,
                lookback_window_size=lookback_window_size,
                calc_types=calc_types,
                arbitrary_df=arbitrary_df,
            )

        for arb_condition in [
            is_arbitrary_constant_maturity,
            is_arbitrary_expiry,
        ]:
            if arb_condition.any():
                lookback_dfs_for_arbitrary_value_smoothing.extend(
                    _generate_lookback_df_helper(arb_condition)
                )

    # indices of resulting dataframe need to be unique. This functionality is important
    # for pre-smoothing processing where unique indices are required
    current_and_lookback_df = pd.concat(
        [
            current_and_lookback_df,
            *lookback_dfs_for_arbitrary_value_smoothing,
            *lookback_dfs_for_listed_exp_smoothing,
        ],
        ignore_index=True,
    )

    # clean out columns with nans
    current_and_lookback_df = remove_nan_columns_from_df(
        current_and_lookback_df
    )

    # The above block adds lookbacks for these listed expires + arbitrary expiries
    # we need to preserve these lookbacks in our cache, so we update the cache after we have added
    # the lookbacks for each expiry (arbitrary or listed) that needs one

    if previous_results["df"] is None:
        previous_results["df"] = current_and_lookback_df
    else:
        previous_results["df"] = concat_lookback(
            previous_results["df"], current_and_lookback_df
        )

    # dataframe becomes highly fragmented across memory due to the continuous addition of columns
    # we overwrite the existing dataframe with a copy which now be in one continuous block of memory
    current_and_lookback_df = current_and_lookback_df.copy()

    current_and_lookback_df = set_qualified_name_timestamp_index(
        df=current_and_lookback_df
    )
    # The lookback function periodically introduces duplicates into the dataframe which affects the arbitrary lookback
    # function that happens after this. The root cause of this needs to be investigated, but in the mean time we drop
    # these duplicates from the dataframe
    duplicated_indices = list(
        current_and_lookback_df.index[
            current_and_lookback_df.index.duplicated()
        ]
        .unique()
        .values
    )
    if duplicated_indices:
        logging.warning(
            f"Duplicated indices found in the lookback dataframe. Dropping: {duplicated_indices}"
        )
        current_and_lookback_df = current_and_lookback_df.loc[
            ~current_and_lookback_df.index.duplicated(keep="first")
        ]
    return current_and_lookback_df


def get_cold_db_data(
    current_result: pd.DataFrame,
    include_listed_expiries: bool,
    calc_types: list[SmoothingCalcType],
    snapshot_dates: SnapshotDates,
    snapshot_timestamps: list[int],
    historic_freq: str,
    debug: bool = False,
    consistent_read: bool = False,
    scheduled_version: str = "",
) -> tuple[list[str], pd.DataFrame]:
    historic_db_and_full_qns = results_to_historic_qualified_names(
        current_result=current_result,
        freq=historic_freq,
        include_listed_expiries=include_listed_expiries,
        scheduled_version=scheduled_version,
    )

    assert historic_db_and_full_qns

    qualified_names_in_current_result, historic_qualified_names = (
        historic_db_and_full_qns
    )

    historic_qualified_name_prefixs_in_current_result = (
        get_qualified_name_prefixs_from_qns(historic_qualified_names)
    )
    historic_qualified_names_with_calc_types = (
        get_calc_type_qns_from_qualified_name_prefix(
            calc_types, historic_qualified_name_prefixs_in_current_result
        )
    )

    lookback_slice: pd.DataFrame = lookback_from_db(
        snapshot_dates=snapshot_dates,
        qualified_names=sorted(historic_qualified_names_with_calc_types),
        debug=debug,
        consistent_read=consistent_read,
    )

    if not lookback_slice.empty:

        non_listed_bundle_rows = ~lookback_slice["qualified_name"].str.contains(
            "listed"
        )
        # because we populate TenorType columns first and then activate the STANDARD_CONSTANT_MATURITY
        # on a slice of the dataframe. We need to make sure TenorType columns are initialised for this to work
        lookback_slice = activate_tenor_types(
            df=lookback_slice,
            initialise_tenor_types=True,
            tenor_types_to_toggle=[],
        )
        lookback_slice.loc[non_listed_bundle_rows] = activate_tenor_types(
            df=lookback_slice.loc[non_listed_bundle_rows],
            initialise_tenor_types=True,
            tenor_types_to_toggle=[TenorType.STANDARD_CONSTANT_MATURITY],
        )

        if include_listed_expiries:
            listed_df = extract_listed_params_from_df(
                lookback_slice, calc_types
            )
            if not listed_df.empty:
                listed_df.loc[:, LOOKBACK_COLUMN_NAME] = True

            lookback_slice = pd.concat([lookback_slice, listed_df])
            # drop rows with .listed as we have extracted the underlying listed expiries
            lookback_slice = lookback_slice[
                ~lookback_slice["qualified_name"].str.contains(
                    ".listed", na=False
                )
            ]

        # Coerce historic QNs to 'live' which is required for smoothing
        if current_result["qualified_name"].str.contains(".live.").any():
            lookback_slice["qualified_name"] = lookback_slice[
                "qualified_name"
            ].str.replace(f".{historic_freq}.", ".live.")

    filtered_lookback_slice = (
        lookback_slice[lookback_slice["timestamp"].isin(snapshot_timestamps)]
        if not lookback_slice.empty
        else pd.DataFrame()
    )

    qualified_name_prefixs_in_current_result = (
        get_qualified_name_prefixs_from_qns(qualified_names_in_current_result)
    )
    # Coerce historic QNs to 'live' which is required for smoothing
    if current_result["qualified_name"].str.contains(".live.").any():
        # rsplit as qualified_name_prefixes do not have a trailing "."
        qualified_name_prefixs_in_current_result = [
            qn.rsplit(".", 1)[0] + ".live"
            for qn in qualified_name_prefixs_in_current_result
        ]

    return qualified_name_prefixs_in_current_result, filtered_lookback_slice


def get_warm_cache_data(
    previous_results: PreviousResults,
    current_result: pd.DataFrame,
    calc_types: list[SmoothingCalcType],
    snapshot_timestamps: list[int],
) -> tuple[list[str], pd.DataFrame]:
    # current_result may not contain the qualified names on the data that we want to fetch a lookback for
    # the qualified_name_prefix allows us to construct qualified_names from other qualified names

    qualified_name_prefixs_in_current_result = (
        get_qualified_name_prefixs_from_qns(current_result["qualified_name"])
        .unique()
        .tolist()
    )

    # we add the calc types to the qualified names to ensure all relevant smoothing data is fetched
    historic_qualified_names_with_calc_types = (
        get_calc_type_qns_from_qualified_name_prefix(
            calc_types, qualified_name_prefixs_in_current_result
        )
    )

    assert previous_results["df"] is not None

    # function extracts listed expiry as well
    filtered_lookback_slice: pd.DataFrame = lookback_from_previous_df(
        lookback_df=previous_results["df"],
        snapshot_dates={"timestamps": snapshot_timestamps},
        qualified_names=historic_qualified_names_with_calc_types,
        calc_types=calc_types,
    )

    # drop rows with .listed as we have extracted the underlying listed expiries
    filtered_lookback_slice = filtered_lookback_slice[
        ~filtered_lookback_slice["qualified_name"].str.contains(".listed")
    ]
    return qualified_name_prefixs_in_current_result, filtered_lookback_slice


def _populate_lookback_for_listed_expiries(
    current_and_lookback_df: pd.DataFrame,
    lookbck_needed_lstd_df: pd.DataFrame,
    curr_and_lookback_lstd_exps: pd.DataFrame,
    lookback_window_size: int,
) -> list[pd.DataFrame]:
    """

    :param current_and_lookback_df: A Dataframe containing all available constant tenor and listed
        expiry data from the current result and from the lookback
    :param lookbck_needed_lstd_df: The dataframe containing all qualified_names that need data
        to be added to their lookbacks
    :param curr_and_lookback_lstd_exps: A dataframe containing all available listed expiries in the
        current result, and from the lookback
    :param lookback_window_size: The targeted number of datapoints we want a listed expiries lookback
        to have
    """
    lookback_dfs_for_listed_exp_smoothing: list[pd.DataFrame] = []

    # we should group by qualified name and not tenors to prevent overpopulating the lookback
    # history when we grab a history of lookback params
    # this will iterate through the different calc types as they are present in qn's
    for (
        lookback_tgt_qn,
        lookback_tgt_df,
    ) in lookbck_needed_lstd_df.groupby("qualified_name"):
        assert isinstance(lookback_tgt_qn, str)

        calc_type = lookback_tgt_qn.split(".")[-1]

        selected_data_for_lookback = _add_lookback_for_target_df(
            lookback_tgt_df=lookback_tgt_df,
            curr_and_lookback_lstd_exps=curr_and_lookback_lstd_exps,
            current_and_lookback_df=current_and_lookback_df,
            lookback_window_size=lookback_window_size,
            calc_type=cast(SmoothingCalcType, calc_type),
            calc_type_tgt_qn=lookback_tgt_qn,  # lookback will be populated with this qn
        )

        # safe
        selected_data_for_lookback = activate_tenor_types(
            df=selected_data_for_lookback,
            initialise_tenor_types=True,
            tenor_types_to_toggle=[TenorType.LISTED_EXPIRY],
        )

        lookback_dfs_for_listed_exp_smoothing.append(selected_data_for_lookback)

    return lookback_dfs_for_listed_exp_smoothing


def _populate_lookback_for_arbitrary_expiries(
    current_and_lookback_df: pd.DataFrame,
    curr_and_lookback_lstd_exps: pd.DataFrame,
    lookback_window_size: int,
    arb_exp_calc_type_qns: list[str],
) -> list[pd.DataFrame]:
    """

    :param current_and_lookback_df: A Dataframe containing all available constant tenor and listed
        expiry data from the current result and from the lookback
    :param curr_and_lookback_lstd_exps: A dataframe containing all available listed expiries in the
        current result, and from the lookback
    :param lookback_window_size: The targeted number of datapoints we want a listed expiries lookback
        to have
    :param arb_exp_calc_type_qns: The calc_type suffixed qualified_names that we want to get the lookback
        data for. These qualified names will also be used  to replace the qualified_names
        of the data we append to the lookback.

    """
    lookback_dfs_for_subscribed_exp_smoothing: list[pd.DataFrame] = []

    for arb_exp_calc_type_qn in arb_exp_calc_type_qns:
        arb_exp_calc_type_df = current_and_lookback_df[
            current_and_lookback_df["qualified_name"].eq(arb_exp_calc_type_qn)
        ]

        calc_type = cast(SmoothingCalcType, arb_exp_calc_type_qn.split(".")[-1])

        if (
            len(arb_exp_calc_type_df)
            > MIN_SMOOTHING_LOOKBACK_RATIO * lookback_window_size
        ):
            # no need for lookback
            continue

        if not arb_exp_calc_type_df.empty:
            lookback_tgt_df = arb_exp_calc_type_df

        else:
            # Used for fallback when the calc_type suffixed qualified_names do not exist.
            # We can be sure that the .params suffixed qualified_names exist
            lookback_tgt_df = current_and_lookback_df[
                current_and_lookback_df["qualified_name"].eq(
                    arb_exp_calc_type_qn.replace(calc_type, "params")
                )
            ]

        selected_data_for_lookback = _add_lookback_for_target_df(
            lookback_tgt_df=lookback_tgt_df,
            curr_and_lookback_lstd_exps=curr_and_lookback_lstd_exps,
            current_and_lookback_df=current_and_lookback_df,
            lookback_window_size=lookback_window_size,
            calc_type=calc_type,
            calc_type_tgt_qn=arb_exp_calc_type_qn,  # lookback will be populated with this qn
        )

        # data copied from the lookback may be a listed expiry
        # we need to make sure this is set to False
        selected_data_for_lookback = activate_tenor_types(
            df=selected_data_for_lookback,
            initialise_tenor_types=True,
            tenor_types_to_toggle=[TenorType.ARBITRARY_EXPIRY],
        )

        lookback_dfs_for_subscribed_exp_smoothing.append(
            selected_data_for_lookback
        )

    return lookback_dfs_for_subscribed_exp_smoothing


def _add_lookback_for_target_df(
    lookback_tgt_df: pd.DataFrame,
    curr_and_lookback_lstd_exps: pd.DataFrame,
    current_and_lookback_df: pd.DataFrame,
    lookback_window_size: int,
    calc_type: SmoothingCalcType,
    calc_type_tgt_qn: str,
) -> pd.DataFrame:
    """

    :param lookback_tgt_df: The incomplete dataset we want to append data onto
    :param curr_and_lookback_lstd_exps: A dataframe containing all available listed expiries in the
        current result, and from the lookback
    :param current_and_lookback_df: A Dataframe containing all available constant tenor and listed
        expiry data from the current result and from the lookback
    :param lookback_window_size: The targeted number of datapoints we want a listed expiries lookback
        to have
    :param calc_type_tgt_qn: This is the target qualified name that we want to replace the qualified_names
        of the data we append to the lookback.
    :param calc_type: This is the calc_type of candidate data to append to the lookback
    """

    # this is the earliest timestamp we have data for. We need to append data earlier than this timestamp
    lookback_tgt_df.sort_values("timestamp", inplace=True)
    earliest_lookback_tgt_df_snap = lookback_tgt_df.iloc[0]

    # We look for data to append based on the tenor of the listed expiry at this snapshot
    # Note: This is important as the "tenors" of listed expiries changes with time
    lookback_tgt_tenor_to_compare = earliest_lookback_tgt_df_snap["tenor_days"]
    is_before_earliest_snapshot = (
        curr_and_lookback_lstd_exps["timestamp"]
        < earliest_lookback_tgt_df_snap["timestamp"]
    )
    scheduled_version, qn_tokens = utils_general.get_qfn_and_version(
        calc_type_tgt_qn
    )
    exchange, instrument, currency, model, freq = (
        qn_tokens[0],
        qn_tokens[1],
        qn_tokens[2],
        qn_tokens[3],
        qn_tokens[-2],
    )

    # we do not want to be searching lookbacks for .smile when considering .moneyness
    contains_correct_qn_tokens = (
        curr_and_lookback_lstd_exps["qualified_name"].str.contains(exchange)
        & curr_and_lookback_lstd_exps["qualified_name"].str.contains(model)
        & curr_and_lookback_lstd_exps["qualified_name"].str.contains(currency)
        & curr_and_lookback_lstd_exps["qualified_name"].str.endswith(calc_type)
    )

    listed_expiries_to_search = curr_and_lookback_lstd_exps[
        is_before_earliest_snapshot & contains_correct_qn_tokens
    ]

    min_historic_data_for_smoothing = (
        MIN_SMOOTHING_LOOKBACK_RATIO * lookback_window_size
    ) - len(lookback_tgt_df)

    listed_expiries_to_search = filter_qualified_names(
        df=listed_expiries_to_search,
        threshold=min_historic_data_for_smoothing,
        filter_type="above_eq_threshold",
    )

    candidate_lstd_exps_for_lookbck = None
    if not listed_expiries_to_search.empty:
        # Select the rows with the most recent timestamp
        candidate_lstd_exps_for_lookbck = listed_expiries_to_search[
            listed_expiries_to_search["timestamp"]
            == listed_expiries_to_search["timestamp"].max()
        ]
        assert candidate_lstd_exps_for_lookbck["qualified_name"].is_unique

    # we pass in the calc_type dataframe, so that we are sure that the tenors returned
    # actually exist for the calc_type. E.g calc_type=strike - if xx.xxx.7d.moneyness exists but xx.xxx.7d.strike
    # does not, 7 should not be included in the list returned.
    # we also do not consider the tenors for the qualified name whose lookback we are filling
    # Filter the DataFrame based on the conditions
    valid_constant_maturities_df = current_and_lookback_df[
        current_and_lookback_df["qualified_name"].str.contains(calc_type)
        & (
            current_and_lookback_df["timestamp"]
            < earliest_lookback_tgt_df_snap["timestamp"]
        )
        & ~current_and_lookback_df["qualified_name"].str.contains(
            calc_type_tgt_qn
        )
    ]
    # Extract the constant tenors from the filtered DataFrame
    available_constant_tenors = extract_constant_tenors_from_maturities(
        df=valid_constant_maturities_df
    )

    closest_tenor_info = determine_closest_tenor(
        target_tenor_days=lookback_tgt_tenor_to_compare,
        listed_expiry_df=candidate_lstd_exps_for_lookbck,
        constant_tenors=available_constant_tenors,
        exchange=exchange,
        instrument=instrument,
        currency=currency,
        model=model,
        calc_type=calc_type,
        freq=freq,
        scheduled_version=scheduled_version,
    )

    if closest_tenor_info["is_constant_tenor"]:
        is_correct_qn = current_and_lookback_df["qualified_name"].eq(
            closest_tenor_info["closest_qualified_name"]
        )
        is_from_lookback = current_and_lookback_df[LOOKBACK_COLUMN_NAME].eq(
            True
        )
        is_before_earliest_snapshot = (
            current_and_lookback_df["timestamp"]
            < earliest_lookback_tgt_df_snap["timestamp"]
        )

        selected_data_for_lookback = current_and_lookback_df.loc[
            is_correct_qn
            & is_from_lookback
            & is_before_earliest_snapshot
            # this will remove overlapping timestamps (listed params and closest tenor params)
            # note that the vols of listed params will be used if they available from in previous_results
        ]
    else:
        # listed expiry is closer
        selected_data_for_lookback = listed_expiries_to_search[
            listed_expiries_to_search["qualified_name"]
            == closest_tenor_info["closest_qualified_name"]
        ]

        # we set this flag to true, so that we safely ignore this data post smoothing
        # if we are backfilling a large chunk, and an expiry gets listed in the middle of a chunk,
        # data used to populate the newly listed expiry's lookback may not have
        # LOOKBACK_COLUMN_NAME = True
        selected_data_for_lookback[LOOKBACK_COLUMN_NAME] = True

    # manipulate the qn of the data for the lookback to match the data that we want to smooth
    # we still keep a reference of its original qualified_name for debugging
    selected_data_for_lookback["qualified_name"] = calc_type_tgt_qn
    selected_data_for_lookback["tenor_days"] = lookback_tgt_tenor_to_compare
    selected_data_for_lookback["closest_qualified_name"] = closest_tenor_info[
        "closest_qualified_name"
    ]
    # must assign after we relace qualified_name
    selected_data_for_lookback.loc[:, "qualified_name_prefix"] = (
        get_qualified_name_prefixs_from_qns(
            selected_data_for_lookback["qualified_name"]
        )
    )

    return cast(pd.DataFrame, selected_data_for_lookback)


def _generate_lookback_for_arbitrary_value(
    current_and_lookback_df: pd.DataFrame,
    all_available_listed_expiries_df: pd.DataFrame,
    lookback_window_size: int,
    calc_types: list[SmoothingCalcType],
    arbitrary_df: pd.DataFrame,
) -> list[pd.DataFrame]:
    unique_arb_exp_qualified_name_prefixs = get_qualified_name_prefixs_from_qns(
        data=arbitrary_df["qualified_name"].unique().tolist()
    )

    arb_exp_calc_type_qns = get_calc_type_qns_from_qualified_name_prefix(
        calc_types=calc_types,
        qualified_name_prefixs=unique_arb_exp_qualified_name_prefixs,
    )

    lookback_dfs_for_subscribed_exp_smoothing = (
        _populate_lookback_for_arbitrary_expiries(
            current_and_lookback_df=current_and_lookback_df,
            curr_and_lookback_lstd_exps=all_available_listed_expiries_df,
            lookback_window_size=lookback_window_size,
            arb_exp_calc_type_qns=arb_exp_calc_type_qns,
        )
    )
    return lookback_dfs_for_subscribed_exp_smoothing
