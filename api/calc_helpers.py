from typing import Callable

import numpy as np
import pandas as pd
import utils_calc
import utils_general
from constants import TENOR_PRECISION
from lambda_types import SmoothParamsDict, TenorMode
from pandas import DataFrame
from utils.common import (
    get_tenor_mapping_key,
    is_potential_float_str,
    round_value,
)
from utils.outputs import create_output_col
from utils_calc import (
    DEFAULT_SURFACE_DELTAS,
    DEFAULT_SURFACE_MONEYNESS,
    TENORS_DAYS_STANDARD,
)

RR_DELTAS = [
    0.45,
    0.4,
    0.35,
    0.3,
    0.25,
    0.2,
    0.15,
    0.1,
    0.05,
    0.04,
    0.03,
    0.02,
    0.01,
]
BF_DELTAS = [
    0.45,
    0.4,
    0.35,
    0.3,
    0.25,
    0.2,
    0.15,
    0.1,
    0.05,
    0.04,
    0.03,
    0.02,
    0.01,
]


def make_tenors(step: int, max: int) -> list[int]:
    return sorted(set(range(0, max + 1, step)) - {0})


TENORS_DAYS_GRANULAR = sorted(
    {
        *make_tenors(1, 14),
        *make_tenors(7, 365),
        30,
        60,
        90,
        120,
        180,
        270,
        365,
        547,
        2 * 365,
        3 * 365,
    }
)

TENORS_DAYS_RESTRICTED = [
    1,
    2,
    3,
    4,
    5,
    6,
    7,
    14,
    30,
    60,
    90,
    120,
]


def get_short_dated_jasper_tenors() -> list[float]:
    return [*[t / 24 for t in list(range(1, 23, 1))], 0.5 / 24]


def get_tenors(
    freq: str,
    mode: TenorMode,
    exchange: str,
    currency: str,
) -> list[int] | list[float]:
    """
    Returns a list of unique tenors to calculate over
    """
    key = get_tenor_mapping_key(exchange, currency)
    tenors = TENOR_MAPPING.get(mode, {}).get(freq, {}).get(key)
    if not tenors:
        utils_general.log_bsdebug(
            f"No tenor config found for {exchange}.{currency}, {mode=}, {freq=}. Falling back to standard tenor grid"
        )
        tenors = TENOR_MAPPING.get(mode, {}).get(freq, {}).get("")
        if not tenors:
            raise ValueError(f"No fallback tenors found for {mode=}, {freq=}")
    return tenors


JASPER_TENOR_SET = [
    round_value(tenor, TENOR_PRECISION)
    for tenor in {
        *get_short_dated_jasper_tenors(),
        *TENORS_DAYS_STANDARD,
    }
]

TENOR_MAPPING: dict[str, dict[str, dict[str, list[int] | list[float]]]] = {
    "standard": {
        "live": {
            "v2composite.BTC": JASPER_TENOR_SET,
            "v2composite.ETH": JASPER_TENOR_SET,
            "blockscholes-syn.ARB": TENORS_DAYS_RESTRICTED,
            "blockscholes-syn.OP": TENORS_DAYS_RESTRICTED,
            "blockscholes-syn.SUI": TENORS_DAYS_RESTRICTED,
            "": TENORS_DAYS_STANDARD,
        },
        "1h": {
            "": TENORS_DAYS_STANDARD,
            "blockscholes-syn.ARB": TENORS_DAYS_RESTRICTED,
            "blockscholes-syn.OP": TENORS_DAYS_RESTRICTED,
            "blockscholes-syn.SUI": TENORS_DAYS_RESTRICTED,
            "coincall.BNB": TENORS_DAYS_RESTRICTED,
            "coincall.XRP": TENORS_DAYS_RESTRICTED,
            "coincall.SUI": TENORS_DAYS_RESTRICTED,
            "coincall.DOGE": TENORS_DAYS_RESTRICTED,
            "deribit.BNB": TENORS_DAYS_RESTRICTED,
            "deribit.XRP": TENORS_DAYS_RESTRICTED,
            "okx.XRP": TENORS_DAYS_RESTRICTED,
            "bybit.XRP": TENORS_DAYS_RESTRICTED,
            "deribit.SOL": TENORS_DAYS_RESTRICTED,
            "okx.SOL": TENORS_DAYS_RESTRICTED,
            "bybit.SOL": TENORS_DAYS_RESTRICTED,
            "v2composite.XRP": TENORS_DAYS_RESTRICTED,
            "v2composite.SOL": TENORS_DAYS_RESTRICTED,
            "v2composite.BNB": TENORS_DAYS_RESTRICTED,
        },
        "1m": {
            "": [
                round_value(tenor, TENOR_PRECISION)
                for tenor in {0.333333333333, *TENORS_DAYS_STANDARD}
            ],
            "v2composite.BTC": [
                round_value(tenor, TENOR_PRECISION)
                # 1h and 8h are stored
                for tenor in {1 / 24, 0.333333333333, *TENORS_DAYS_STANDARD}
            ],
            "v2composite.ETH": [
                round_value(tenor, TENOR_PRECISION)
                # 1h and 8h are stored
                for tenor in {1 / 24, 0.333333333333, *TENORS_DAYS_STANDARD}
            ],
            "blockscholes-syn.ARB": [
                round_value(tenor, TENOR_PRECISION)
                for tenor in {0.333333333333, *TENORS_DAYS_RESTRICTED}
            ],
            "blockscholes-syn.OP": [
                round_value(tenor, TENOR_PRECISION)
                for tenor in {0.333333333333, *TENORS_DAYS_RESTRICTED}
            ],
            "blockscholes-syn.SUI": [
                round_value(tenor, TENOR_PRECISION)
                for tenor in {0.333333333333, *TENORS_DAYS_RESTRICTED}
            ],
            "coincall.BNB": TENORS_DAYS_RESTRICTED,
            "coincall.XRP": TENORS_DAYS_RESTRICTED,
            "coincall.SUI": TENORS_DAYS_RESTRICTED,
            "coincall.DOGE": TENORS_DAYS_RESTRICTED,
            "deribit.XRP": TENORS_DAYS_RESTRICTED,
            "deribit.SOL": TENORS_DAYS_RESTRICTED,
            "deribit.BNB": TENORS_DAYS_RESTRICTED,
            "okx.XRP": TENORS_DAYS_RESTRICTED,
            "okx.SOL": TENORS_DAYS_RESTRICTED,
            "bybit.XRP": TENORS_DAYS_RESTRICTED,
            "bybit.SOL": TENORS_DAYS_RESTRICTED,
            "v2composite.XRP": TENORS_DAYS_RESTRICTED,
            "v2composite.SOL": TENORS_DAYS_RESTRICTED,
            "v2composite.BNB": TENORS_DAYS_RESTRICTED,
        },
    },
    "granular": {"1h": {"": TENORS_DAYS_GRANULAR}},
}

DELTAS_STR = [str(x) for x in DEFAULT_SURFACE_DELTAS]
MONEYNESS_STR = [str(x) for x in DEFAULT_SURFACE_MONEYNESS]
EXTRA_MONEYNESS_LESS_STRIKES = [
    0.992,
    0.994,
    0.996,
    0.998,
    1.002,
    1.004,
    1.006,
    1.008,
]
MIN_STRIKES = 5
RR_DELTAS_STR = [str(x) for x in RR_DELTAS]
BF_DELTAS_STR = [str(x) for x in BF_DELTAS]
DELTAS_STR_OUTPUT = [create_output_col("delta", f) for f in DELTAS_STR]
MONEYNESS_STR_OUTPUT = [create_output_col("money", f) for f in MONEYNESS_STR]
RR_DELTAS_STR_OUTPUT = [create_output_col("delta", f) for f in RR_DELTAS_STR]
BF_DELTAS_STR_OUTPUT = [create_output_col("delta", f) for f in BF_DELTAS_STR]


SPAN_SMOOTH_DEFAULT = {  # TODO: correct comments
    "1h": 20,  # impact: 7h: ~50% / 14h: ~75% / 24h: ~90%
    "1m": 20,  # impact: 7m: ~50% / 14m: ~75% / 24m: ~90%
    "20s": 20,  # impact: 7m: ~50% / 14m: ~75% / 24m: ~90%
}
SPAN_ZSCORE_DEFAULT = {
    "1h": 20,  # impact: 7h: ~50% / 14h: ~75% / 24h: ~90%
    "1m": 20,  # impact: 7m: ~50% / 14m: ~75% / 24m: ~90%
    "20s": 20,  # impact: 7m: ~50% / 14m: ~75% / 24m: ~90%
}


SMOOTH_PARAMS: SmoothParamsDict = {
    "1h": {
        "zscore": {
            "window": 48,
            "span_smooth": {},  # SPAN_SMOOTH_DEFAULT used for exchanges not here
            "span_zscore": {},  # SPAN_ZSCORE_DEFAULT used for exchanges not here
            "adjust": True,
            "limit": {
                "deribit": 10,
                "bybit": 8,
                "lyra-optimism": 5,
                "lyra-arbitrum": 7,
                "aevo": 8,
                "okx": 8,
                "v2composite": 8,
                "v2lyra": 8,
                "v2lyra-supercomposite": 8,
                "bybit-supercomposite": 8,
                "blockscholes-syn": 8,
                "eurex": 8,
                "coincall": 8,
            },
        },
    },
    "1m": {
        "zscore": {
            "window": 60,
            "span_smooth": {  # SPAN_SMOOTH_DEFAULT used for exchanges not here
                "v2lyra-supercomposite": 60,  # impact: 10m: ~50% / 20m: ~75% / 30m: ~90%
            },
            "span_zscore": {
                "v2lyra-supercomposite": 60,
            },
            "adjust": True,
            "limit": {
                "deribit": 10,
                "bybit": 8,
                "lyra-optimism": 5,
                "lyra-arbitrum": 7,
                "aevo": 8,
                "okx": 8,
                "v2lyra": 8,
                "v2composite": 8,
                "v2lyra-supercomposite": 8,
                "bybit-supercomposite": 8,
                "blockscholes-syn": 8,
                "eurex": 8,
                "coincall": 8,
            },
        },
    },
    "20s": {
        "round_to_seconds": 60,
        "zscore": {
            "window": 60,
            "span_smooth": {  # SPAN_SMOOTH_DEFAULT used for exchanges not here
                "v2lyra-supercomposite": 120,  # impact: 90m: ~90%
                "bybit-supercomposite": 120,  # impact: 90m: ~90%
            },
            "span_zscore": {  # SPAN_ZSCORE_DEFAULT used for exchanges not here
                "v2lyra-supercomposite": 120,
                "bybit-supercomposite": 120,
            },
            "adjust": True,
            "limit": {
                "deribit": 10,
                "bybit": 8,
                "lyra-optimism": 5,
                "lyra-arbitrum": 7,
                "aevo": 8,
                "okx": 8,
                "v2lyra": 8,
                "v2composite": 8,
                "v2lyra-supercomposite": 8,
                "bybit-supercomposite": 8,
                "blockscholes-syn": 8,
                "eurex": 8,
                "coincall": 8,
            },
        },
    },
}


COLS_RENAME_FNS: dict[str, Callable[[str], str]] = {
    "smile": lambda col: create_output_col("delta", col),
    "moneyness": lambda col: create_output_col("money", col),
    "skew": lambda col: create_output_col("delta", col),
    "butterfly": lambda col: create_output_col("delta", col),
}

COLS_REVERSE_RENAME_CFG = {
    "smile": {f: DELTAS_STR[i] for i, f in enumerate(DELTAS_STR_OUTPUT)},
    "moneyness": {
        f: MONEYNESS_STR[i] for i, f in enumerate(MONEYNESS_STR_OUTPUT)
    },
    "skew": {f: RR_DELTAS_STR[i] for i, f in enumerate(RR_DELTAS_STR_OUTPUT)},
    "butterfly": {
        f: BF_DELTAS_STR[i] for i, f in enumerate(BF_DELTAS_STR_OUTPUT)
    },
}


def finalize_result_df(
    df: DataFrame,
    calc_type: str,
    runtime: str,
    timestamp: int,
) -> pd.DataFrame:
    df[["timestamp", "isodate", "runtime"]] = np.asarray(
        [
            timestamp,
            utils_general.to_iso(timestamp),
            runtime,
        ],
        dtype=object,
    )

    if calc_type in COLS_RENAME_FNS:
        rename_func = COLS_RENAME_FNS[calc_type]
        # Rename all float strings dynamically
        rename_mapping: dict[str, str] = {
            col: rename_func(col)
            for col in df.columns
            if is_potential_float_str(col)
        }
        df.rename(rename_mapping, axis="columns", inplace=True)
    return df


def get_calibration_delta_boundary(expiry: float) -> dict[str, float]:
    calibration_boundary = utils_calc.get_calibration_delta_boundary(expiry)

    return {
        "put_wing_boundary": -1 * calibration_boundary,
        "call_wing_boundary": calibration_boundary,
    }  # *-1 for put delta


def get_atm_deltas_from_boundaries(
    put_wing_delta: float, call_wing_delta: float
) -> list[float]:
    """
    This function filters out deltas that lie outside the specified put_wing_delta and call_wing_delta deltas
    and returns a list of deltas that we want to recalibrate with. The deltas returned ar always deltas that are
    close to the atm.
    """

    relevant_deltas = [
        delta
        for delta in DEFAULT_SURFACE_DELTAS
        if delta <= put_wing_delta or delta >= call_wing_delta
    ]
    return relevant_deltas
