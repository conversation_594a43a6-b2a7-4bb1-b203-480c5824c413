import argparse

import app
from utils_aws import backfill_run

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument(
        "-result_queue",
        type=str,
        default="s3saver-4",
        help="Name of s3saver SQS queue to write results to",
    )

    parser.add_argument(
        "-events",
        type=str,
        default="event.json",
        help="Name of events.json",
    )

    parser.add_argument(
        "-chunk_size",
        type=int,
        default=1,
        help="Size of single chunk",
    )

    parser.add_argument(
        "-chunk_interval",
        type=str,
        default="hour",
        help="Interval of date range chunk",
    )

    parser.add_argument(
        "-target_table",
        required=True,
        type=str,
        help="The dynamo table to specify in sqs messages for saving results",
    )

    args = parser.parse_args()

    backfill_run(
        lambda_handler=app.lambda_handler,  # type: ignore
        result_queue_name=args.result_queue,
        events_fname=args.events,
        chunk_interval=args.chunk_interval,
        chunk_size=args.chunk_size,
        target_dynamo_table=args.target_table,
        s3_saver_action_sets=[
            [
                {
                    "filter": {
                        "by_row": {
                            "exclude": {
                                "field_to_values": {"from_lookback": ["True"]}
                            }
                        },
                        "by_field": {
                            "exclude": [
                                "isodate",
                                "to_smooth",
                                "window_fully_marked_to_smooth",
                                "from_lookback",
                                "api_version",
                            ]
                        },
                    },
                    "split": {
                        "by_group": {
                            "on": {"field": {"name": "qualified_name"}},
                            "tag": "#QN",
                        }
                    },
                }
            ]
        ],
        partition_key_val="#QN",
    )
