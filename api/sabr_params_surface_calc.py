import logging
import math
import warnings
from datetime import datetime, timezone
from functools import partial

import numpy as np
import pandas as pd
import utils_calc
import utils_general
from calc_helpers import DELTAS_STR, MONEYNESS_STR, finalize_result_df
from constants import EXPIRY_PRECISION, TENOR_PRECISION, load_domestic_rates
from lambda_types import (
    ListedTenorParamsSABR,
    ParamsCalcResult,
    TenorExpiryArbFields,
)
from utils.calibrate import (
    calibrate_sabr_with_recalibrations,
    fit_spline_with_exception,
)
from utils.common import (
    check_calibrated_vols_and_log_error_with_details,
    round_value,
)
from utils.extrapolate import get_strikes_and_vols_to_extrapolate
from utils.interpolate import get_strikes_to_interpolate
from utils_calc import BOUNDS_SABR, CalibrationQuery, get_domestic_rate
from utils_calc import DEFAULT_SURFACE_MONEYNESS as MONEYNESS

warnings.simplefilter(action="ignore", category=RuntimeWarning)


def sabr_tenor_params(
    snap: list[ListedTenorParamsSABR],
    exchange: str,
    currency: str,
    expiry_iv_info: TenorExpiryArbFields,
) -> ParamsCalcResult:
    # TODO: test that correct params are being passed through
    tenors_days = sorted(
        [round_value(x, TENOR_PRECISION) for x in expiry_iv_info.keys()]
    )
    df_vol_matrix_pre = pd.DataFrame(
        np.zeros((len(tenors_days), len(DELTAS_STR))),
        columns=DELTAS_STR,
        index=tenors_days,
    )

    df_vol_matrix_money = pd.DataFrame(
        np.zeros((len(tenors_days), len(MONEYNESS_STR))),
        columns=MONEYNESS_STR,
        index=tenors_days,
    )

    snap_data = pd.DataFrame(snap)
    snap_data["expiry"] = snap_data["expiry"].apply(
        partial(round_value, precision=EXPIRY_PRECISION)
    )
    # snap_data = snap_data[snap_data["R2"] > 0.9]
    # snap_data = snap_data[snap_data["expiry"] > (2 / 365)]
    all_listed_expiries = snap_data["expiry"].tolist()
    all_listed_tenors = sorted(
        round_value(x * 365, TENOR_PRECISION) for x in all_listed_expiries
    )
    listed_tenor_expiry_map = dict(zip(all_listed_tenors, all_listed_expiries))
    max_exp_listed = max(all_listed_expiries)
    min_exp_listed = min(all_listed_expiries)
    spot = np.mean(snap_data["spot"])
    timestamp = int(snap_data["timestamp"].iloc[0])
    runtime = datetime.now(tz=timezone.utc).strftime("%Y-%m-%dT%H:%M:%S.000Z")

    # max_forward = snap_data[snap_data["expiry"] == max_exp_listed].iloc[0]["forward"]
    # c_yield = (1 / max_exp_listed) * math.log(max_forward / spot)

    ### Yields ############
    c_yield_s, c_yield_left, c_yield_right = utils_calc.calc_convenience_yield(
        snap_data, spot
    )

    ###########################

    df_vol_matrix_pre["atm"] = 0
    df_vol_matrix_pre["lower_bordering_expiry"] = "Extra"
    df_vol_matrix_pre["upper_bordering_expiry"] = "Extra"

    tenors_params_sabr = pd.DataFrame(
        columns=[
            "tenor_days",
            "forward",
            "sabr_alpha",
            "sabr_rho",
            "sabr_volvol",
            "atm_vol",
            "expiry",
            "rd",
        ]
    )

    tenor_completed = []

    tenor_days_without_listed_expiries = sorted(
        set(tenors_days).difference(all_listed_tenors)
    )
    tenor_days_only_listed_expiries = sorted(
        set(tenors_days).intersection(all_listed_tenors)
    )

    for tenor in tenor_days_only_listed_expiries:
        exp = listed_tenor_expiry_map[tenor]
        tenor_completed.append(tenor)
        sabr_Date = snap_data[snap_data["expiry"] == exp].iloc[0]
        df_vol_matrix_pre.loc[tenor, "lower_bordering_expiry"] = sabr_Date[
            "underlying_index"
        ]
        df_vol_matrix_pre.loc[tenor, "upper_bordering_expiry"] = sabr_Date[
            "underlying_index"
        ]
        forward = sabr_Date["forward"]
        alpha = sabr_Date["sabr_alpha"]
        rho = sabr_Date["sabr_rho"]
        volvol = sabr_Date["sabr_volvol"]
        atm_vol = sabr_Date["atm_vol"]
        rd = get_domestic_rate(load_domestic_rates(), exp * 365)

        tenors_params_sabr = tenors_params_sabr.append(  # type: ignore
            {
                "tenor_days": tenor,
                "forward": forward,
                "sabr_alpha": alpha,
                "sabr_rho": rho,
                "sabr_volvol": volvol,
                "atm_vol": atm_vol,
                "expiry": exp,
                "rd": rd,
            },
            ignore_index=True,
        )

    for tenor in tenor_days_without_listed_expiries:
        try:
            exp = round_value(tenor / 365, EXPIRY_PRECISION)
            if exp < min_exp_listed:
                # print("The expiry was lesser than the shortest listed Expiry")
                continue
            elif exp > max_exp_listed:
                # print("The expiry was greater than the largest listed Expiry")
                continue

            else:
                # Tenor interpolation
                (
                    lower_bordering_expiry,
                    upper_bordering_expiry,
                ) = utils_calc.get_bordering_expiries(
                    snap_df=snap_data, target_expiry=exp
                )

                if (
                    lower_bordering_expiry is None
                    or upper_bordering_expiry is None
                ):
                    raise Exception(
                        f"Both bordering Listed expiries must be present: "
                        f"{lower_bordering_expiry=}"
                        f"{upper_bordering_expiry=}"
                    )

                df_vol_matrix_pre.loc[tenor, "lower_bordering_expiry"] = (
                    lower_bordering_expiry["underlying_index"]
                )
                df_vol_matrix_pre.loc[tenor, "upper_bordering_expiry"] = (
                    upper_bordering_expiry["underlying_index"]
                )

                # interpolate forward
                frac = (exp - lower_bordering_expiry["expiry"]) / (
                    upper_bordering_expiry["expiry"]
                    - lower_bordering_expiry["expiry"]
                )
                forward_inter = pow(
                    lower_bordering_expiry["forward"], 1 - frac
                ) * pow(upper_bordering_expiry["forward"], frac)

                (
                    lower_bordering_strikes,
                    interpolated_tenor_strikes,
                    upper_bordering_strikes,
                ) = get_strikes_to_interpolate(
                    upper_expiry=upper_bordering_expiry,
                    lower_expiry=lower_bordering_expiry,
                    model="SABR",
                    spot=spot,
                    interpolated_forward=forward_inter,
                    exchange=exchange,
                )

                lower_bordering_sabr_vars = utils_calc.sabr_var(
                    np.array(lower_bordering_strikes),
                    lower_bordering_expiry["forward"],
                    lower_bordering_expiry["expiry"],
                    lower_bordering_expiry["sabr_alpha"],
                    1,
                    lower_bordering_expiry["sabr_rho"],
                    lower_bordering_expiry["sabr_volvol"],
                )

                upper_bordering_sabr_vars = utils_calc.sabr_var(
                    np.array(upper_bordering_strikes),
                    upper_bordering_expiry["forward"],
                    upper_bordering_expiry["expiry"],
                    upper_bordering_expiry["sabr_alpha"],
                    1,
                    upper_bordering_expiry["sabr_rho"],
                    upper_bordering_expiry["sabr_volvol"],
                )

                interpolated_sabr_vars = [
                    (a * (1 - frac)) + (b * frac)
                    for a, b in zip(
                        np.asarray(lower_bordering_sabr_vars),
                        np.asarray(upper_bordering_sabr_vars),
                    )
                ]
                interpolated_vols = [
                    np.sqrt(var / exp) for var in interpolated_sabr_vars
                ]
                ############ Scipy Optimization ########################################################
                # x0_SABR = np.asarray([ 0.5*min(interpolated_tenor_sabr_vars) , 0.1 , -0.5 , 0.1 , 0.1 ])
                # bounds_SABR = [(1e-5, max(interpolated_tenor_sabr_vars)) , (0.001, 1) , (-1, 1) , (2*min(np.log(strikes/forward_inter)) , 2*max(np.log(strikes/forward_inter)) ) , (0.001, 1)  ]
                # bounds_lower_SABR = [1e-5 , 0.001 , -1 ,2*min(np.log(strikes/forward_inter)) , 0.001  ]
                # bounds_upper_SABR = [max(interpolated_tenor_sabr_vars) , 1 , 1 ,2*max(np.log(strikes/forward_inter)) , 1  ]
                # bounds_ls_SABR =(bounds_lower_SABR, bounds_upper_SABR)
                # res_SABR = optimize.minimize(vol_squared_error_SABR, x0_SABR, args = (strikes , forward_inter), method = "L-BFGS-B",bounds=bounds_SABR)
                # a , b , rho , m , sigma = res_SABR.x

                ####################### SABR Parameters - API Calibration ###################################################
                rd = get_domestic_rate(load_domestic_rates(), exp * 365)
                query: CalibrationQuery = {
                    "expiry": exp,
                    "forward": forward_inter,
                    "spot": spot,
                    "domestic_rate": rd,
                    "test_type": "strikes",
                    "vol_test_type": "vol_lognormal",
                    "model": "SABR",
                    "LNvols": interpolated_vols,
                    "strikes": list(interpolated_tenor_strikes),
                    "biv": interpolated_vols,
                    "aiv": interpolated_vols,
                }
                ################ SABR Parameters Calibration #######################################################
                ################ SABR Parameters Calibration #######################################################

                x0_SABR = utils_calc.ini_guess_sabr(
                    list(interpolated_tenor_strikes),
                    interpolated_vols,
                    forward_inter,
                )

                sabr_parameters = calibrate_sabr_with_recalibrations(
                    query=query, x0_SABR=x0_SABR, bounds_SABR=BOUNDS_SABR
                )

                ##########################################################################################
                alpha, rho, volvol = (
                    sabr_parameters["sabr_alpha"],
                    sabr_parameters["sabr_rho"],
                    sabr_parameters["sabr_volvol"],
                )
                ##########################################################################################
                predicted_vols = utils_calc.sabr_vol(
                    k=np.array(interpolated_tenor_strikes),
                    f=forward_inter,
                    t=exp,
                    alpha=alpha,
                    beta=1,
                    rho=rho,
                    volvol=volvol,
                )
                # TODO: bad calibrations will have nans in vols. We need to fix the underlying SABR clibration or return sensible fallback values
                # returns 0 for bad calibrations
                (
                    calib_r2,
                    _,
                    _,
                    _,
                ) = check_calibrated_vols_and_log_error_with_details(
                    precalibrated_vols=interpolated_vols,
                    model_predicted_vols=np.array(predicted_vols),
                    tenor=tenor,
                    timestamp=timestamp,
                    model="SABR",
                    process="tenor interpolation",
                )

                tenors_params_sabr = tenors_params_sabr.append(  # type: ignore
                    {
                        "tenor_days": tenor,
                        "forward": forward_inter,
                        "sabr_alpha": alpha,
                        "sabr_rho": rho,
                        "sabr_volvol": volvol,
                        "R2_calib": calib_r2,
                        "atm_vol": utils_calc.sabr_vol(
                            forward_inter,
                            forward_inter,
                            exp,
                            alpha,
                            1,
                            rho,
                            volvol,
                        ),
                        "expiry": exp,
                        "rd": rd,
                    },
                    ignore_index=True,
                )
                tenor_completed.append(tenor)

        except Exception as e:
            logging.exception(
                f"Error calculating SABR derived data for {exchange=}, tenor={tenor}, timestamp={timestamp}, e={e}"
            )

    ### Fill Moneyness Surface for Interpolated Tenors######################
    for tenor in tenor_completed:
        temp = tenors_params_sabr[
            tenors_params_sabr["tenor_days"] == tenor
        ].iloc[0]
        forward = temp["forward"]
        alpha = temp["sabr_alpha"]
        rho = temp["sabr_rho"]
        volvol = temp["sabr_volvol"]
        strikes = np.array(MONEYNESS) * forward
        tenor_vols = utils_calc.sabr_vol(
            strikes,
            forward,
            round_value(tenor / 365, EXPIRY_PRECISION),
            alpha,
            1,
            rho,
            volvol,
        )
        df_vol_matrix_money.loc[tenor, MONEYNESS_STR] = tenor_vols
    ####################################################################
    #### Extrapolation Left ############
    # TENOR MUST BE SORTED FOR THIS TO WORK
    df_vol_matrix_money = utils_calc.extrapolate_left(df_vol_matrix_money)
    ###################################
    ## Temp run ###
    """
    series_t = df_vol_matrix_money.index.values
    #### Extrapolation Right ############
    df_vol_matrix_money = df_vol_matrix_money**2
    df_vol_matrix_money = df_vol_matrix_money.mul(series_t / 365, axis=0)
    df_vol_matrix_money = df_vol_matrix_money.replace(to_replace=0, method="ffill")
    df_vol_matrix_money = df_vol_matrix_money.div(series_t / 365, axis=0)
    df_vol_matrix_money = df_vol_matrix_money ** (1 / 2)
    ###################################
    """

    ###### Info for Interpolated and Extrapolated Tenors ##########
    tenor_in_ex = [t for t in tenors_days if t not in tenor_completed]
    min_inter = min(tenor_completed)
    max_inter = max(tenor_completed)
    tenor_in_ex_right = [x for x in tenor_in_ex if x > max_inter]
    ############################################################
    """
    ###########Temp Block #################
    df_vol_matrix_money['1'] = df_vol_matrix_money['1'].replace(to_replace = 0 , method = 'ffill')
    atm_val = df_vol_matrix_money['1'].values
    df_vol_matrix_money_ratio = df_vol_matrix_money.div(atm_val , axis = 0)
    test_x_right = [x/365 for x in tenor_in_ex_right]
    for mon_str in MONEYNESS_STR:
        if mon_str == '1':
            continue
        train_y = df_vol_matrix_money_ratio[df_vol_matrix_money_ratio.index.isin(tenor_completed)][mon_str].tolist()
        param = optimize.fsolve(func_fsolve , x0 = [1,2] , args= ([max_inter/365 , 5] , [train_y[-1] , 1.01]))
        if param[1]< 0:
            print('b less than 0')
        test_y_right = func_r(np.asarray(test_x_right), *param)
        df_vol_matrix_money_ratio.loc[tenor_in_ex_right , mon_str ] = test_y_right

    df_vol_matrix_money = df_vol_matrix_money_ratio.mul(atm_val , axis = 0)
    ##########################################
    """

    df_vol_matrix_money, tenor_in_ex = fit_spline_with_exception(
        df_vol_matrix_money=df_vol_matrix_money,
        tenor_completed=tenor_completed,
        right_side_tenors=tenor_in_ex_right,
        tenors_to_extrapolate=tenor_in_ex,
        timestamp=timestamp,
        exchange=exchange,
        currency=currency,
    )

    """
    ######## Fill Delta Surface for Interpolated Tenors######################
    for tenor in tenor_completed:
        exp = tenor/365
        temp = tenors_params_sabr[tenors_params_sabr['tenor_days'] == tenor].iloc[0]
        forward = temp["forward"]
        alpha = temp["sabr_alpha"]
        rho = temp["sabr_rho"]
        volvol = temp["sabr_volvol"]
        df_vol_matrix_pre.loc[tenor, "atm"] = sabr_vol(forward, forward, exp, alpha, 1, rho, volvol)
        for delta in DELTAS:
            if delta >= 0:
                start = float("inf")
                ini = 1.5 * forward
                phi = 1.0
            else:
                start = 0
                ini = 0.5 * forward
                phi = -1.0
            str_tol = 1e-9
            iteration = 0
            while abs(start - ini) > str_tol and iteration < 500:
                iteration += 1
                start = ini
                vol = sabr_vol(ini, forward, tenor/365 , alpha, 1, rho, volvol)
                ini = spot*(math.exp((-phi* NormalDist().inv_cdf(phi * delta)* vol* math.sqrt(exp))+((vol ** 2) * exp / 2)))

            df_vol_matrix_pre.loc[tenor, str(delta)] = sabr_vol(ini, forward, tenor/365, alpha, 1, rho, volvol)

            #df_vol_matrix_strike.loc[tenor, str(delta)] = ini
    ##############################################################################
    """
    """
    ###Extrapolation in Delta Left Side
    df_vol_matrix_pre = df_vol_matrix_pre.replace(
        to_replace=[0, math.nan], method="bfill"
    )
    """
    """
    ################## SABR Extrapolation ATM #############################################
    train_x = df_vol_matrix_pre[df_vol_matrix_pre["atm"] != 0].index.tolist()
    train_x = list(map(int, train_x))
    train_x = [x / 365 for x in train_x]
    train_y = df_vol_matrix_pre[df_vol_matrix_pre["atm"] != 0]["atm"].tolist()
    test_x = df_vol_matrix_pre[df_vol_matrix_pre["atm"] == 0].index.tolist()
    test_x = list(map(int, test_x))
    test_x = [x / 365 for x in test_x]

    try:
        param, param_covar = optimize.curve_fit(
            func, np.asarray(train_x)[1:], np.asarray(train_y)[1:], bounds= ((-math.inf , 0 , 0 ),(math.inf, math.inf , math.inf))
        )

        test_y = func(np.asarray(test_x), *param)
        final_atm = train_y + test_y.tolist()
        df_vol_matrix_pre["atm"] = final_atm
    except Exception as e:
        df_vol_matrix_pre["atm"] = df_vol_matrix_pre["atm"].replace(to_replace=0, method="ffill")

    """
    """
    ###Extrapolation in Delta Right Side
    df_vol_matrix_pre = df_vol_matrix_pre.replace(to_replace=0, method="ffill")
    """

    ############ Vol Surface - Moneyness ######################
    for t in tenor_in_ex:
        try:
            exp = round_value(t / 365, EXPIRY_PRECISION)
            c_yield = c_yield_left if t < min_inter else c_yield_right
            spot_shifted = (
                spot if t < min_inter else c_yield_s.iloc[-1]["forward"]
            )
            t_shifted = (
                exp if t < min_inter else exp - c_yield_s.iloc[-1]["expiry"]
            )
            assert t > 0
            forward_extrap = spot_shifted * (math.exp(c_yield * t_shifted))

            df_vol_matrix_pre.loc[t, "lower_bordering_expiry"] = snap_data[
                snap_data["expiry"] == listed_tenor_expiry_map[max_inter]
            ].iloc[0]["underlying_index"]

            vols = df_vol_matrix_money.loc[t, MONEYNESS_STR].values.tolist()  # type: ignore
            strikes = np.array(MONEYNESS) * forward_extrap
            # In the future we may want to use custom set of strikes to calibrate
            # E.g Longer tenors using a different set of strikes
            # We may want to anchor some deltas in certain regions of the wings and atm like in modelparams
            # calculate the grid based on the deltas and volatilites
            rd = get_domestic_rate(load_domestic_rates(), exp * 365)
            (
                strikes_narrow,
                vols_narrow,
                deltas,
            ) = get_strikes_and_vols_to_extrapolate(
                expiry=exp,
                forward=forward_extrap,
                spot=spot,
                strikes_to_vol=dict(zip(strikes, vols)),
                r_d=rd,
            )
            query = {
                "expiry": exp,
                "forward": forward_extrap,
                "spot": spot,
                "domestic_rate": rd,
                "test_type": "delta",
                "vol_test_type": "vol_lognormal",
                "model": "SABR",
                "LNvols": vols_narrow,
                "strikes": strikes_narrow,
                "biv": vols_narrow,
                "aiv": vols_narrow,
            }
            ################# Lambda Layer ######################################################################
            ############# Scipy for Local Run#################

            x0_SABR = utils_calc.ini_guess_sabr(
                strikes_narrow, vols_narrow, forward_extrap
            )

            sabr_parameters = calibrate_sabr_with_recalibrations(
                query=query, x0_SABR=x0_SABR, bounds_SABR=BOUNDS_SABR
            )

            ############################ Error Handling, Get previous calib values ############################
            if (
                np.isinf(np.asarray(list(sabr_parameters.values()))).any()
                or np.isnan(np.asarray(list(sabr_parameters.values()))).any()
            ):
                logging.error(
                    f"SABR calibration failed for extrapolated tenor - fallback to previous tenor values. {exchange=} t={exp} | {currency} | {utils_general.to_iso(timestamp)} | {timestamp}"
                )

                lower_fallback_expiry_row, upper_fallback_expiry_row = (
                    utils_calc.get_bordering_expiries(
                        snap_df=tenors_params_sabr, target_expiry=exp
                    )
                )

                if lower_fallback_expiry_row is not None:
                    pre_sol = lower_fallback_expiry_row
                elif upper_fallback_expiry_row is not None:
                    pre_sol = upper_fallback_expiry_row
                else:
                    raise NotImplementedError(
                        f"No bordering calibrated expiries found. Investigate! {exchange=} | t={exp} | {currency} | {utils_general.to_iso(timestamp)} | {timestamp}"
                    )

                (
                    sabr_parameters["sabr_alpha"],
                    sabr_parameters["sabr_rho"],
                    sabr_parameters["sabr_volvol"],
                ) = (
                    pre_sol["sabr_alpha"],
                    pre_sol["sabr_rho"],
                    pre_sol["sabr_volvol"],
                )
            #######################################################################################################
            alpha, rho, volvol = (
                sabr_parameters["sabr_alpha"],
                sabr_parameters["sabr_rho"],
                sabr_parameters["sabr_volvol"],
            )

            ############ Adding New Params in tenor params ####################################################
            predicted_vols = utils_calc.sabr_vol(
                k=np.array(strikes_narrow),
                f=forward_extrap,
                t=exp,
                alpha=alpha,
                beta=1,
                rho=rho,
                volvol=volvol,
            )

            # constant tenor R2 score
            # TODO: bad calibrations will have nans in vols. We need to fix the underlying SABR clibration or return sensible fallback values
            # returns 0 for bad calibrations

            (
                calib_r2,
                _,
                _,
                _,
            ) = check_calibrated_vols_and_log_error_with_details(
                precalibrated_vols=vols_narrow,
                model_predicted_vols=np.array(predicted_vols),
                tenor=t,
                timestamp=timestamp,
                model="SABR",
                process="tenor extrapolation",
            )

            tenors_params_sabr = tenors_params_sabr.append(  # type: ignore
                {
                    "tenor_days": t,
                    "forward": forward_extrap,
                    "sabr_alpha": alpha,
                    "sabr_rho": rho,
                    "sabr_volvol": volvol,
                    "R2_calib": calib_r2,
                    "atm_vol": utils_calc.sabr_vol(
                        forward_extrap,
                        forward_extrap,
                        exp,
                        alpha,
                        1,
                        rho,
                        volvol,
                    ),
                    "expiry": exp,
                    "rd": rd,
                },
                ignore_index=True,
            )
            #####################################################################################################
            """
            for money in MONEYNESS:
                df_vol_matrix_money.loc[t, str(money)] = sabr_vol(
                    money * forward, forward, t / 365, alpha, 1, rho, volvol
                )
            """
        except Exception as e:
            logging.exception(
                f"Error calculating moneyness and/or listed expiry param for {exchange=}, tenor={t}, timestamp={timestamp}, e={e}"
            )

    tenors_params_sabr["spot"] = spot

    tenors_params_sabr = finalize_result_df(
        df=tenors_params_sabr,
        calc_type="params",
        runtime=runtime,
        timestamp=timestamp,
    )

    return {
        "model": "SABR",
        "exchange": exchange,
        "currency": currency,
        "timestamp": timestamp,
        "dfs": {
            "params": tenors_params_sabr,
        },
        "df_vol_matrix_pre": df_vol_matrix_pre,
    }
