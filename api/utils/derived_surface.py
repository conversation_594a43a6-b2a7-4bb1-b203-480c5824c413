from datetime import datetime, timezone

import pandas as pd
import utils_general
from calc_helpers import BF_DELTAS_STR, RR_DELTAS_STR, finalize_result_df
from lambda_types import (
    AuxCalcDfs,
    AuxCalcResult,
    AuxCalcType,
    TenorType,
)
from utils_calc import Model

from .common import (
    create_qualified_name_from_expiry_str,
)
from .outputs import create_output_col


def smile_derived_data(
    params_surface: pd.DataFrame,
    delta_vol_matrix_df: pd.DataFrame,
    calc_types: list[AuxCalcType],
    model: Model,
) -> AuxCalcResult:
    timestamp = delta_vol_matrix_df.iloc[0]["timestamp"]
    runtime = datetime.now(tz=timezone.utc).strftime("%Y-%m-%dT%H:%M:%S.000Z")
    version, qn_tokens = utils_general.get_qfn_and_version(
        delta_vol_matrix_df.iloc[0]["qualified_name"]
    )
    exchange, currency = (
        qn_tokens[0],
        qn_tokens[2],
    )

    aux_calc_type_dfs = _process_aux_calc_types(
        delta_vol_matrix_df=delta_vol_matrix_df,
        calc_types=calc_types,
        params_surface=params_surface,
    )

    for ct in calc_types:
        aux_calc_type_dfs[ct] = finalize_result_df(
            df=aux_calc_type_dfs[ct],
            calc_type=ct,
            runtime=runtime,
            timestamp=timestamp,
        )

    result: AuxCalcResult = {
        "model": model,
        "exchange": exchange,
        "currency": currency,
        "timestamp": timestamp,
        "dfs": aux_calc_type_dfs,
    }

    return result


def _process_aux_calc_types(
    delta_vol_matrix_df: pd.DataFrame,
    calc_types: list[AuxCalcType],
    params_surface: pd.DataFrame,
) -> AuxCalcDfs:
    aux_calc_type_dfs: AuxCalcDfs = {}

    cols_to_inherit = [
        "spot",
        "expiry",
        "forward",
        "smoothed",
        "timestamp",
        "tenor_days",
        "qualified_name",
        TenorType.LISTED_EXPIRY.value,
        TenorType.ARBITRARY_EXPIRY.value,
        TenorType.STANDARD_CONSTANT_MATURITY.value,
        TenorType.ARBITRARY_CONSTANT_MATURITY.value,
    ]

    for calc_type in calc_types:
        if calc_type == "skew":
            aux_calc_type_dfs[calc_type] = calculate_skew(
                delta_vol_surface=delta_vol_matrix_df, deltas=RR_DELTAS_STR
            )

        elif calc_type == "butterfly":
            aux_calc_type_dfs[calc_type] = calculate_butterfly(
                delta_vol_surface=delta_vol_matrix_df, deltas=BF_DELTAS_STR
            )

        else:
            raise NotImplementedError(f"Unrecognised calc type {calc_type}")

        aux_calc_type_dfs[calc_type] = _get_output_df_for_calc_type(
            df=aux_calc_type_dfs[calc_type],
            cols_to_inherit=cols_to_inherit,
            calc_type=calc_type,
            params_surface=params_surface,
        )

    return aux_calc_type_dfs


def get_pos_and_neg_delta_columns(
    deltas: list[str],
) -> tuple[list[str], list[str]]:
    pos_cols = [create_output_col("delta", delta) for delta in deltas]
    neg_cols = ["-" + col for col in pos_cols]
    return pos_cols, neg_cols


def calculate_skew(
    delta_vol_surface: pd.DataFrame, deltas: list[str]
) -> pd.DataFrame:
    pos_cols, neg_cols = get_pos_and_neg_delta_columns(deltas)

    return delta_vol_surface[pos_cols].sub(delta_vol_surface[neg_cols].values)


def calculate_butterfly(
    delta_vol_surface: pd.DataFrame, deltas: list[str]
) -> pd.DataFrame:
    pos_cols, neg_cols = get_pos_and_neg_delta_columns(deltas)

    avg = (
        delta_vol_surface[pos_cols]
        .add(delta_vol_surface[neg_cols].values)
        .div(2)
    )
    return avg.sub(delta_vol_surface["atm"], axis=0)


def _get_output_df_for_calc_type(
    df: pd.DataFrame,
    cols_to_inherit: list[str],
    calc_type: AuxCalcType,
    params_surface: pd.DataFrame,
) -> pd.DataFrame:
    sample_qn = params_surface.iloc[0]["qualified_name"]
    version, qn_tokens = utils_general.get_qfn_and_version(sample_qn)

    df[cols_to_inherit] = params_surface[cols_to_inherit]
    df["qualified_name"] = create_qualified_name_from_expiry_str(
        exchange=qn_tokens[0],
        currency=qn_tokens[2],
        model=qn_tokens[3],
        expiry_str=params_surface["qualified_name"].str.split(".").str[-3],
        freq=qn_tokens[-2],
        calc_type=calc_type,
        version=version,
    )

    return df
