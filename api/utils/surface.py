import logging
from datetime import datetime, timezone
from typing import Literal, Optional

import numpy as np
import pandas as pd
import utils_calc
from calc_helpers import MONEYNESS_STR, finalize_result_df
from constants import BAD_MONEYNESS_CUTOFF_RATIO, load_domestic_rates
from lambda_types import (
    DerivedCalcDfs,
    DerivedCalcResult,
    ExpiryArbFields,
    NDArrayFloat64,
    SingleParams,
    TenorExpiryArbFields,
    VolSurfaceCalcType,
)
from utils_calc import DEFAULT_SURFACE_DELTAS as DELTAS
from utils_calc import DEFAULT_SURFACE_MONEYNESS as MONEYNESS
from utils_calc import Model, extract_model_params, get_domestic_rate
from utils_general import decimal_to_string

from .common import get_unique_merged_list, model_vol
from .outputs import create_output_col


def estimate_strike_from_delta(
    delta: float,
    params: SingleParams,
    spot: float,
    forward: float,
    expiry: float,
    model: Model,
    r_d: float,
    default: float,
) -> tuple[float, bool]:
    model_params = extract_model_params(params, model)
    try:
        return (
            utils_calc.estimate_strike_from_delta(
                model=model,
                model_params=model_params,
                s=spot,
                f=forward,
                t=expiry,
                delta=delta,
                rd=get_domestic_rate(load_domestic_rates(), expiry * 365),
            ),
            True,
        )
    except Exception as _:
        logging.exception(
            f"Impossible to calculate estimate_strike_from_delta, using default strike value, {model=}, {delta=}, {expiry=}, {default=}"
        )
        return default, False


def get_min_max_strike_from_deltas(
    params: SingleParams,
    spot: float,
    forward: float,
    expiry: float,
    model: Model,
    exchange: str,
    min_delta: float,
    max_delta: float,
) -> dict[str, float]:
    r_d = get_domestic_rate(load_domestic_rates(), expiry * 365)

    def _estimate_strike(delta: float, default: float) -> float:
        res, _ = estimate_strike_from_delta(
            delta,
            params,
            spot,
            forward,
            expiry,
            model,
            r_d,
            default,
        )
        return res

    return {
        "min_strike": _estimate_strike(min_delta, min(MONEYNESS) * forward),
        "max_strike": _estimate_strike(max_delta, max(MONEYNESS) * forward),
    }


def validate_estimated_strikes(
    forward: float,
    strikes_list: list[float] | NDArrayFloat64,
    deltas: list[float] | NDArrayFloat64,
) -> Optional[dict[Literal["strikes", "deltas"], NDArrayFloat64]]:
    """
    This function does some crude filtering on a set strikes that may be poorly estimated due to bad modelparameters.
    It filters moneyness strikes that are greater than BAD_MONEYNESS_CUTOFF_RATIO. This is chosen to be a large number such
    that we are unlikely to be intentionally calculating vols at these moneyness values.
    """

    moneyness_strikes_array = np.asarray(strikes_list) / forward
    exceeds_threshold = moneyness_strikes_array > BAD_MONEYNESS_CUTOFF_RATIO
    exceeded_threshold_index = np.argmax(exceeds_threshold)

    if exceeds_threshold[exceeded_threshold_index]:
        filtered_strikes = np.asarray(strikes_list[:exceeded_threshold_index])
        filtered_deltas = np.asarray(deltas[:exceeded_threshold_index])
        return {"strikes": filtered_strikes, "deltas": filtered_deltas}

    return None


def calculate_moneyness_from_params(
    row: pd.Series,  # type: ignore
    model: Model,
    monies: list[float],
) -> pd.Series:  # type: ignore
    """
    Function accepts a pandas series of modelParams and calculates
    the moneyness vols they imply

    """

    model_params = extract_model_params(row, model)
    forward = row["forward"]
    expiry = row["expiry"]

    strikes = np.asarray(monies) * forward

    tenor_vols = model_vol(
        model_params=model_params,
        forward=forward,
        expiry=expiry,
        model=model,
        strike=strikes,
    )
    columns = [create_output_col("money", str(money)) for money in monies]
    moneyness_vol_series = pd.Series(tenor_vols, index=columns)

    moneyness_vol_series["vol_space"] = "money"
    return moneyness_vol_series


def calculate_delta_from_params(
    row: pd.Series,  # type: ignore
    model: Model,
    exchange: str,
    deltas: Optional[list[float]] = None,
) -> pd.Series:  # type: ignore
    if deltas is None:
        deltas = DELTAS

    spot = row["spot"]
    forward = row["forward"]
    expiry = row["expiry"]
    r_d = get_domestic_rate(load_domestic_rates(), expiry * 365)
    model_params = extract_model_params(row, model)

    delta_vol = {}
    strike_for_delta_list = []

    delta_vol["atm"] = model_vol(
        model_params=model_params,
        forward=forward,
        expiry=expiry,
        model=model,
        strike=forward,
    )

    for delta in deltas:
        default = forward * MONEYNESS[0 if delta < 0 else -1]
        strike_for_delta, _ = estimate_strike_from_delta(
            delta, row, spot, forward, expiry, model, r_d, default
        )

        delta_vol[f"{delta}"] = model_vol(
            model_params=model_params,
            forward=forward,
            expiry=expiry,
            model=model,
            strike=strike_for_delta,
        )
        # store the list of strikes as we need these for later calibration
        strike_for_delta_list.append(strike_for_delta)

    delta_vol["strikes"] = strike_for_delta_list
    delta_vol_series = pd.Series(delta_vol)
    delta_vol_series = delta_vol_series.rename(
        index={str(f): create_output_col("delta", str(f)) for f in deltas}
    )
    delta_vol_series["vol_space"] = "delta"

    return delta_vol_series


def create_volatility_surfaces(
    params_surface: pd.DataFrame,
    df_vol_matrix_pre: pd.DataFrame,
    calc_types: list[VolSurfaceCalcType],
    currency: str,
    exchange: str,
    timestamp: int,
    model: Model,
    expiry_iv_info: TenorExpiryArbFields,
) -> DerivedCalcResult:
    spot = params_surface.iloc[0]["spot"]
    runtime = datetime.now(tz=timezone.utc).strftime("%Y-%m-%dT%H:%M:%S.000Z")
    tenors_in_surface = params_surface["tenor_days"].to_list()
    expiries_in_surface = params_surface["expiry"].to_list()

    df_vol_matrix_money = pd.DataFrame(
        np.zeros((len(tenors_in_surface), len(MONEYNESS_STR))),
        columns=MONEYNESS_STR,
        index=range(len(params_surface)),
    )
    df_strikes = pd.DataFrame()

    for idx, (tenor, exp) in enumerate(
        zip(tenors_in_surface, expiries_in_surface)
    ):

        matching_tenor_row = params_surface[
            params_surface["tenor_days"] == tenor
        ].iloc[0]
        forward: float = matching_tenor_row["forward"]
        model_params = extract_model_params(
            mapping_object=matching_tenor_row, model=model
        )

        df_vol_matrix_pre.loc[tenor, "expiry"] = exp
        df_vol_matrix_pre.loc[tenor, "atm"] = model_vol(
            model_params=model_params,
            forward=forward,
            expiry=exp,
            model=model,
            strike=forward,
        )

        extra_deltas = list(expiry_iv_info.get(tenor, ExpiryArbFields()).deltas)

        for delta in get_unique_merged_list(DELTAS, extra_deltas):
            default = forward * MONEYNESS[0 if delta < 0 else -1]
            strike_for_delta, _ = estimate_strike_from_delta(
                delta,
                matching_tenor_row,
                spot,
                forward,
                exp,
                model,
                get_domestic_rate(load_domestic_rates(), exp * 365),
                default,
            )

            df_vol_matrix_pre.loc[tenor, str(delta)] = model_vol(
                model_params=model_params,
                forward=forward,
                expiry=exp,
                model=model,
                strike=strike_for_delta,
            )

        extra_monies = list(
            expiry_iv_info.get(tenor, ExpiryArbFields()).moneyness
        )

        moneyness_complete = get_unique_merged_list(MONEYNESS, extra_monies)
        money_strikes = np.asarray(moneyness_complete) * forward

        tenor_vols = model_vol(
            model_params=model_params,
            forward=forward,
            expiry=exp,
            model=model,
            strike=money_strikes,
        )

        df_vol_matrix_money.loc[
            idx, [*[str(m) for m in moneyness_complete], "tenor_days", "expiry"]  # type: ignore
        ] = [
            *tenor_vols,
            tenor,
            exp,
        ]  # type: ignore

        # fill strikes df
        extra_strikes = list(
            expiry_iv_info.get(tenor, ExpiryArbFields()).strikes
        )
        if extra_strikes:
            strike_vols = model_vol(
                model_params=model_params,
                forward=forward,
                expiry=exp,
                model=model,
                strike=np.asarray(extra_strikes),
            )

            assert isinstance(
                strike_vols, (list, np.ndarray)
            )  # for mypy right below
            assert len(strike_vols) == len(extra_strikes)

            df_strikes.loc[  # type: ignore
                tenor,
                [
                    *[decimal_to_string(s, "strike") for s in extra_strikes],
                    "expiry",
                ],
            ] = [
                *strike_vols,
                exp,
            ]

    df_vol_matrix_pre.reset_index(inplace=True, names="tenor_days")
    df_strikes.reset_index(inplace=True, names="tenor_days")

    calc_type_dfs: DerivedCalcDfs = {
        "smile": df_vol_matrix_pre,
        "moneyness": df_vol_matrix_money,
        "strike": df_strikes,
    }
    for calc_type in calc_types:
        calc_type_dfs[calc_type] = finalize_result_df(
            df=calc_type_dfs[calc_type],
            calc_type=calc_type,
            runtime=runtime,
            timestamp=timestamp,
        )

    return {
        "model": model,
        "exchange": exchange,
        "currency": currency,
        "timestamp": timestamp,
        "dfs": calc_type_dfs,
    }
