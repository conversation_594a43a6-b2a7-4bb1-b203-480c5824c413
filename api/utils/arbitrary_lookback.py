import functools
import logging
from typing import Optional, cast, get_args

import numpy as np
import pandas as pd
import utils_calc
import utils_general
from constants import (
    BAD_MONEYNESS_CUTOFF_RATIO,
    CALC_TYPE_TO_COL_SPACE,
    CALIB_R2_THRESHOLD,
    LOOKBACK_COLUMN_NAME,
    R2_SMOOTHCALIB_COLUMN_NAME,
    SPIKED_PARAMS_COLUMN_NAME,
    load_domestic_rates,
)
from lambda_types import (
    ArbitraryLookbackVolCalcDetails,
    EmptyCalcTypeError,
    NDArrayFloat64,
    SmileCtInfo,
    VolSurfaceCalcType,
)
from utils_calc import Model, extract_model_params, get_domestic_rate

from utils.common import (
    model_vol,
    set_qualified_name_timestamp_index,
)
from utils.lookback import get_qualified_name_prefixs_from_qns
from utils.outputs import (
    create_output_col,
    drop_available_columns_from_df,
    undo_create_output_col,
)
from utils.surface import estimate_strike_from_delta


def identify_and_populate_arbitrary_values_for_smoothing(
    current_and_lookback_results: pd.DataFrame,
    current_result: pd.DataFrame,
    calc_types: list[VolSurfaceCalcType],
    chunk_start: str,
    scheduled_version: str = "",
) -> pd.DataFrame:
    """

    This function identified arbitrarily subscribed values that do not have a history,
    and uses the modelparameters to generate a history of vols so that we can smooth
    effectively.

    """

    if not current_and_lookback_results.index.is_unique:
        duplicated_indices = list(
            current_and_lookback_results.index[
                current_and_lookback_results.index.duplicated()
            ]
            .unique()
            .values
        )
        logging.error(
            f"Lookback dataframe must have unique index. Skipping function Arbitrary lookback calculation. {chunk_start=}. "
            f"Duplicated indices: {duplicated_indices}"
        )
        return current_and_lookback_results

    lookbacks_for_current_result_qns = current_and_lookback_results[
        current_and_lookback_results.index.get_level_values(
            "qualified_name"
        ).isin(current_result["qualified_name"])
    ]
    for calc_type in set(calc_types):
        try:
            calc_type_df, params_df = _get_calc_type_and_params_df(
                calc_type=calc_type,
                lookbacks_for_current_results=lookbacks_for_current_result_qns,
                scheduled_version=scheduled_version,
            )
        except EmptyCalcTypeError as e:
            logging.error(
                f"Error while populating arbitrary lookback. Skipping {calc_type=}, {e}"
            )
            continue

        for model, calc_type_model_df in calc_type_df.groupby("model"):
            try:
                assert model in get_args(Model)
                model = cast(Model, model)
                col_space = CALC_TYPE_TO_COL_SPACE[calc_type]

                model_params_columns = [
                    col
                    for col in params_df.columns
                    if col.startswith(model.lower())
                ]

                assert not any(
                    col
                    for col in calc_type_model_df.columns
                    if isinstance(col, (int, float))
                ), "There are numeric column names in the DataFrame, Arbitrary lookback population will not work"

                calc_type_model_df = calc_type_model_df.rename(
                    columns=functools.partial(
                        _rename_values,
                        suffix=col_space,
                        calc_type=calc_type,
                    )
                )

                calc_type_model_df = _drop_and_merge_on_params_df(
                    calc_type_df=calc_type_model_df,
                    params_df=params_df,
                    merge_columns=[
                        "timestamp",
                        "qualified_name_prefix",
                    ],
                    extra_cols=[
                        "forward",
                        "expiry",
                        "spot",
                        R2_SMOOTHCALIB_COLUMN_NAME,
                        SPIKED_PARAMS_COLUMN_NAME,
                        *model_params_columns,
                    ],
                    calc_type=calc_type,
                )

                calc_type_columns = [
                    col
                    for col in calc_type_model_df.columns
                    if isinstance(col, (int, float))
                ]

                # For each qualified_name, check the non_lookback data for the calc_type values
                # that are being calculated either because they are  subscribed to or are being calculated from the catalog
                # We will then check to see if there is a lookback for each of these strikes
                qn_missing_calc_type_dfs: list[pd.DataFrame] = []
                lookback_vol_calc_info: ArbitraryLookbackVolCalcDetails = (
                    utils_general.nested_dict()
                )
                for qualified_name, qn_grouped_df in calc_type_model_df.groupby(
                    level="qualified_name"
                ):
                    assert isinstance(qualified_name, str)  # for mypy
                    try:
                        missing_data_mappings, lookback_vol_calc_info = (
                            _get_missing_ct_columns_and_respective_strikes(
                                qn_df=qn_grouped_df,
                                calc_type=calc_type,
                                calc_type_columns=calc_type_columns,
                                qualified_name=qualified_name,
                                model=model,
                                params_columns=model_params_columns,
                                lookback_vol_calc_info=lookback_vol_calc_info,
                            )
                        )
                        if missing_data_mappings is not None:
                            missing_ct_columns, missing_ct_values = (
                                missing_data_mappings
                            )
                            qn_missing_data_df = qn_grouped_df.loc[
                                missing_ct_columns.index
                            ]
                            qn_missing_data_df[
                                f"missing_{calc_type}_columns"
                            ] = missing_ct_columns
                            qn_missing_data_df[
                                f"missing_{calc_type}_strikes"
                            ] = missing_ct_values
                            qn_missing_calc_type_dfs.append(qn_missing_data_df)

                    except Exception as ex:
                        logging.exception(
                            f"Error {ex}, while trying to populate arbitrary lookbacks. Failed to get missing calc_type columns and values for {qualified_name=}, {chunk_start=}"
                        )
                        continue

                if not qn_missing_calc_type_dfs:
                    continue

                # reduce excessive logging
                lookback_vol_calc_details: list[str] = [
                    f"{error_type} | {qn} | {details}"
                    for error_type, qn_details in lookback_vol_calc_info.items()
                    for qn, details in qn_details.items()
                ]
                if lookback_vol_calc_details:
                    logging.info(
                        f"Arbitrary Lookback Summary Info for {model=}, {calc_type=}: \n"
                        + "\n".join(lookback_vol_calc_details)
                    )

                all_calc_type_missing_data = pd.concat(qn_missing_calc_type_dfs)

                try:

                    model_params = extract_model_params(
                        mapping_object=all_calc_type_missing_data, model=model
                    )
                    vols = model_vol(
                        model_params=model_params,
                        forward=np.asarray(
                            all_calc_type_missing_data["forward"]
                        ),
                        expiry=np.asarray(all_calc_type_missing_data["expiry"]),
                        model=model,
                        strike=np.asarray(
                            all_calc_type_missing_data[
                                f"missing_{calc_type}_strikes"
                            ]
                        ),
                    )
                    assert isinstance(
                        vols, np.ndarray
                    ), f"Returned vols must be a numpy array {model=}, {calc_type=}"

                    for (qn_tstamp, df_row), vol_row in zip(
                        all_calc_type_missing_data.iterrows(), vols
                    ):
                        assert isinstance(qn_tstamp, tuple)
                        if SPIKED_PARAMS_COLUMN_NAME not in df_row:
                            raise KeyError(
                                f"Column {SPIKED_PARAMS_COLUMN_NAME} does not exist in the row, {df_row['qualified_name']=}, {df_row['timestamp']=}, {vol_row=}"
                            )
                        if not isinstance(
                            df_row[SPIKED_PARAMS_COLUMN_NAME], bool
                        ):
                            raise ValueError(
                                f"Value in column {SPIKED_PARAMS_COLUMN_NAME} is not a boolean: {df_row[SPIKED_PARAMS_COLUMN_NAME]}, {df_row['qualified_name']=}, {df_row['timestamp']=}, {vol_row=}"
                            )

                        assert not df_row[
                            SPIKED_PARAMS_COLUMN_NAME
                        ], f"Row cannot contain spike, {df_row}"

                        # assign calculated rows back to the df
                        for idx, ct_column in enumerate(
                            df_row[f"missing_{calc_type}_columns"]
                        ):
                            # create the output_col
                            ct_column = create_output_col(
                                suffix=CALC_TYPE_TO_COL_SPACE[calc_type],
                                col_str=str(ct_column),
                            )
                            current_and_lookback_results.loc[
                                qn_tstamp,
                                ct_column,
                            ] = vol_row[idx]

                except Exception as e:
                    logging.exception(
                        f"Error {e=}, while trying to calculate and populate Implied Volatilities for arbitrary lookbacks for {model=}, {chunk_start=}"
                    )
                    continue

            except Exception as ex:
                logging.exception(
                    f"Error {ex=}, while trying to populate arbitrary lookbacks for {model=}, {calc_type=}, {chunk_start=}"
                )
                continue

    return current_and_lookback_results


def _get_missing_calc_type_values(
    missing_ct_value_mask: pd.DataFrame,
    ct_values_missing_lookback_for_qn: list[float | int],
    calc_type: VolSurfaceCalcType,
    forwards: pd.Series,  # type: ignore
    model: Model,
    smile_ct_info: Optional[SmileCtInfo] = None,
) -> tuple[pd.Series, pd.Series]:  # type: ignore
    """
    Identify and return missing calc_type column names and converted strike values.

    :param missing_ct_value_mask: The mask indicating missing calc_type values
    :param ct_values_missing_lookback_for_qn: The list of ct values that are missing lookbacks
    :param calc_type: The calc type of the data
    :param smile_ct_info: A dataframe index corresponds to the missing_ct_mask df. This is needed as estimating
        the strikes for the calc type requires knowledge of the modelparameters and expiries.
    :param forwards: The forward values for each row.

    :return: Two Series where each element is a list of missing calc_type column names and missing strike values.
    """

    missing_columns = []
    missing_values = []
    delta_fallback_strikes = None

    # initial check to see whether the deltas that have been subscribed to contain unreasonable strikes.
    # If they do, then avoid calculating a lookback for them
    if calc_type == "smile":
        assert smile_ct_info is not None
        delta_fallback_strikes, available_ct_values = (
            _get_fallback_strikes_for_relevant_delta_columns(
                smile_ct_info=smile_ct_info,
                delta_columns=ct_values_missing_lookback_for_qn,
            )
        )

    for qn_timestamp_idx, row in missing_ct_value_mask.iterrows():
        if not isinstance(qn_timestamp_idx, tuple):
            raise TypeError(
                f"Expected qn_timestamp_idx to be a tuple, got {type(qn_timestamp_idx)}"
            )

        row_missing_columns: list[float] = []
        row_missing_values: list[float] = []
        forward = forwards.loc[qn_timestamp_idx]

        for ct_column, is_missing in zip(
            ct_values_missing_lookback_for_qn, row
        ):
            if is_missing:
                row_missing_columns.append(ct_column)

                if calc_type == "moneyness":
                    row_missing_values.append(forward * ct_column)
                elif calc_type == "strike":
                    row_missing_values.append(ct_column)
                elif calc_type == "smile":

                    assert smile_ct_info is not None
                    assert delta_fallback_strikes

                    params_info_row = smile_ct_info["params_df"].loc[
                        qn_timestamp_idx
                    ]
                    _, qn_tokens = utils_general.get_qfn_and_version(
                        qualified_name=qn_timestamp_idx[0]
                    )

                    params = extract_model_params(
                        mapping_object=smile_ct_info["params_df"].loc[
                            qn_timestamp_idx
                        ],
                        model=model,
                    )

                    default = (
                        row_missing_values[-1]
                        if row_missing_values
                        else delta_fallback_strikes[ct_column]
                    )  # use the last calculated strike as a fallback, if we aren't calculating the first in the history
                    strike, _ = estimate_strike_from_delta(
                        delta=ct_column,
                        params=params,  # type: ignore
                        spot=float(params_info_row["spot"]),
                        forward=forward,
                        expiry=float(params_info_row["expiry"]),
                        model=model,
                        r_d=get_domestic_rate(
                            load_domestic_rates(),
                            float(params_info_row["expiry"]) * 365,
                        ),
                        default=default,
                    )
                    row_missing_values.append(strike)

                else:
                    raise NotImplementedError(
                        f"Unsupported calc_type, {calc_type}"
                    )

        missing_columns.append(row_missing_columns)
        missing_values.append(row_missing_values)

    return pd.Series(
        missing_columns, index=missing_ct_value_mask.index
    ), pd.Series(missing_values, index=missing_ct_value_mask.index)


def _get_fallback_strikes_for_relevant_delta_columns(
    smile_ct_info: SmileCtInfo,
    delta_columns: list[float | int],
) -> tuple[dict[float | int, float], list[float | int]]:
    """
    Calculate fallback strikes for given delta columns and determine relevant columns based on moneyness criteria.

    This function processes the provided smile context information and delta columns to calculate fallback strike prices.
    It also filters out columns that do not meet the moneyness criteria, ensuring only relevant columns are included in the output.
    """

    delta_fallback_strikes = {}
    delta_columns_to_calculate = []
    assert smile_ct_info is not None

    _, qn_tokens = utils_general.get_qfn_and_version(
        qualified_name=str(smile_ct_info["earliest_current_result"].name[0])  # type: ignore
    )

    for delta_col in list(delta_columns):  # iterate through copy
        strike = utils_calc.strike_from_delta_vol(
            s=smile_ct_info["earliest_current_result"]["spot"],
            f=smile_ct_info["earliest_current_result"]["forward"],
            r_d=get_domestic_rate(
                load_domestic_rates(),
                smile_ct_info["earliest_current_result"]["expiry"] * 365,
            ),
            t=smile_ct_info["earliest_current_result"]["expiry"],
            vol=smile_ct_info["earliest_current_result"][delta_col],  # type: ignore
            delta=delta_col,
        )
        moneyness_strike = (
            strike / smile_ct_info["earliest_current_result"]["forward"]
        )
        if moneyness_strike < BAD_MONEYNESS_CUTOFF_RATIO:
            delta_columns_to_calculate.append(delta_col)
            delta_fallback_strikes[delta_col] = strike

    return delta_fallback_strikes, delta_columns_to_calculate


def _get_calc_type_and_params_df(
    calc_type: str,
    lookbacks_for_current_results: pd.DataFrame,
    scheduled_version: str = "",
) -> tuple[pd.DataFrame, pd.DataFrame]:
    """
    Strike calc_types often have less qualified_names as they are not calculated for constant tenors

    """
    calc_type_df = lookbacks_for_current_results[
        lookbacks_for_current_results.index.get_level_values(
            "qualified_name"
        ).str.contains(calc_type)
    ]
    if calc_type_df.empty:
        raise EmptyCalcTypeError(f"Calc type df is empty {calc_type}")

    model_idx = 4 if scheduled_version else 3
    calc_type_df["model"] = pd.Series(
        calc_type_df.index.get_level_values("qualified_name"),
        index=calc_type_df.index,
    ).str.split(".", expand=True)[model_idx]

    calc_type_df["qualified_name_prefix"] = get_qualified_name_prefixs_from_qns(
        pd.Series(
            calc_type_df.index.get_level_values("qualified_name"),
            index=calc_type_df.index,
        )
    )
    params_df = lookbacks_for_current_results[
        lookbacks_for_current_results.index.get_level_values(
            "qualified_name"
        ).str.contains("params")
        & lookbacks_for_current_results["qualified_name_prefix"].isin(
            calc_type_df["qualified_name_prefix"]
        )
    ]
    params_df.dropna(axis="columns")  # remove smile/money columns

    # ensure availability of columns
    params_df[SPIKED_PARAMS_COLUMN_NAME] = params_df.get(
        # Seems typing system for the default value is off
        [SPIKED_PARAMS_COLUMN_NAME],
        default=False,  # type: ignore
    )
    params_df[R2_SMOOTHCALIB_COLUMN_NAME] = params_df.get(
        # Seems typing system for the default value is off
        [R2_SMOOTHCALIB_COLUMN_NAME],
        default=np.nan,  # type: ignore
    )

    return calc_type_df, params_df


def calculate_vols(
    model: Model, df: pd.DataFrame, calc_type: str
) -> NDArrayFloat64:
    """
    Calculate strikes based on the model type.
    """

    if model == "spline":
        # spline implementation is not vectorized
        strikes = []
        for _, row in df.iterrows():
            spline_params = extract_model_params(
                mapping_object=row, model=model
            )
            row_strikes = model_vol(
                model_params=spline_params,
                forward=row["forward"],
                model=model,
                expiry=row["expiry"],
                strike=list(row[f"missing_{calc_type}_strikes"]),
            )
            strikes.append(row_strikes)
        return np.array(strikes, dtype=object)
    else:
        vectorized_model_params = extract_model_params(
            mapping_object=df, model=model
        )
        vols = model_vol(
            model_params=vectorized_model_params,
            model=model,
            forward=np.asarray(df["forward"]),
            expiry=np.asarray(df["expiry"]),
            strike=np.asarray(df[f"missing_{calc_type}_strikes"]),
        )
        assert isinstance(
            vols, np.ndarray
        ), "Returned vols must be a numpy array"
        return vols


def _get_missing_ct_columns_and_respective_strikes(
    qn_df: pd.DataFrame,
    calc_type_columns: list[float | int],
    calc_type: VolSurfaceCalcType,
    qualified_name: str,
    model: Model,
    params_columns: list[str],
    lookback_vol_calc_info: ArbitraryLookbackVolCalcDetails,
) -> tuple[Optional[tuple[pd.Series, pd.Series]], ArbitraryLookbackVolCalcDetails]:  # type: ignore
    """
    Function checks a qualified_names calc_type columns to see if any data is missing from the lookback
    e.g a new subscription. It returns a dataframe with all lookback rows that need any single calc_type
    value, e.g strike/moneyness with 2 new columns: the missing calc type columns (60000strike, 100money)
    and the dollar denominated strikes them imply (60_000, forward * 1.0 money)

    Note: The function is expected to modify qn_df inplace

    """

    # Check for data quality issues and filter data - recalibration spikes and low R2
    qn_df, num_spiked_rows, num_low_r2_rows, full_lookback_len = (
        _check_data_quality_issues_and_filter_df(qn_df=qn_df)
    )

    _from_lookback = qn_df[LOOKBACK_COLUMN_NAME] == True
    current_df = qn_df.loc[~_from_lookback]
    qn_lookback_df = qn_df.loc[_from_lookback]

    # retain only strikes/monies that have been calculated for this qn. Those that haven't
    # will have nan values
    all_available_ct_values_in_current_result = (
        current_df[calc_type_columns].dropna(axis=1, how="all").columns.tolist()
    )
    missing_ct_value_mask = qn_lookback_df[
        all_available_ct_values_in_current_result
    ].isna()

    if np.all(missing_ct_value_mask.values == False):
        # All calculated strikes contain their lookbacks
        return None, lookback_vol_calc_info

    _log_data_quality_issues(
        lookback_vol_calc_info=lookback_vol_calc_info,
        qualified_name=qualified_name,
        calc_type=calc_type,
        num_spiked_rows=num_spiked_rows,
        num_low_r2_rows=num_low_r2_rows,
        full_lookback_len=full_lookback_len,
    )

    _log_missing_calc_type_values(
        lookback_vol_calc_info=lookback_vol_calc_info,
        missing_ct_value_mask=missing_ct_value_mask,
        calc_type=calc_type,
        qualified_name=qualified_name,
    )

    smile_ct_info: Optional[SmileCtInfo] = None
    if calc_type == "smile":
        params_df = qn_lookback_df.loc[missing_ct_value_mask.any(axis=1)][
            ["expiry", "spot", *params_columns]
        ]

        smile_ct_info = {
            "params_df": params_df,
            "earliest_current_result": current_df.iloc[
                0
            ],  # we will use this as a fallback strike
        }

    return (
        _get_missing_calc_type_values(
            missing_ct_value_mask=missing_ct_value_mask,
            ct_values_missing_lookback_for_qn=all_available_ct_values_in_current_result,
            calc_type=calc_type,
            forwards=qn_lookback_df["forward"],
            smile_ct_info=smile_ct_info,
            model=model,
        ),
        lookback_vol_calc_info,
    )


def _log_missing_calc_type_values(
    lookback_vol_calc_info: ArbitraryLookbackVolCalcDetails,
    qualified_name: str,
    calc_type: VolSurfaceCalcType,
    missing_ct_value_mask: pd.DataFrame,
) -> ArbitraryLookbackVolCalcDetails:
    """
    Log information about missing calc_type values.
    """
    missing_ct_value_sum = missing_ct_value_mask.sum()
    missing_ct_value_sum_zero_filtered = missing_ct_value_sum[
        missing_ct_value_sum > 0
    ]
    missing_ct_value_dict = {
        float(key): f"{value} rows missing"  # type: ignore
        for key, value in missing_ct_value_sum_zero_filtered.items()
    }

    lookback_vol_calc_info["Calc Type Values with Missing Lookback Vols"][
        qualified_name.replace(f".{calc_type}", "")
    ] = missing_ct_value_dict

    return lookback_vol_calc_info


def _log_data_quality_issues(
    lookback_vol_calc_info: ArbitraryLookbackVolCalcDetails,
    qualified_name: str,
    calc_type: VolSurfaceCalcType,
    num_spiked_rows: int | None,
    num_low_r2_rows: int | None,
    full_lookback_len: int,
) -> ArbitraryLookbackVolCalcDetails:
    """
    Log data quality issues to the lookback volume calculation info.
    """
    qn_key = qualified_name.replace(f".{calc_type}", "")

    if num_spiked_rows:
        lookback_vol_calc_info["Params Containing Recalibration Spike"][
            qn_key
        ] = f"{num_spiked_rows} out of {full_lookback_len}"

    if num_low_r2_rows:
        lookback_vol_calc_info["Low R2 Smoothcalib Values"][
            qn_key
        ] = f"{num_low_r2_rows} out of {full_lookback_len}"

    return lookback_vol_calc_info


def _check_data_quality_issues_and_filter_df(
    qn_df: pd.DataFrame,
) -> tuple[pd.DataFrame, Optional[int], Optional[int], int]:
    """
    Check for data quality issues in the dataframe and return filtered data along with issue counts.

    Returns:
        - Filtered dataframe (excluding spiked rows)
        - Number of spiked rows (if any)
        - Number of low R2 rows (if any)
        - Total lookback length
    """
    full_lookback_len = qn_df[qn_df[LOOKBACK_COLUMN_NAME] == True].shape[0]

    # Check for spiked parameters
    num_spiked_rows = None
    if qn_df[SPIKED_PARAMS_COLUMN_NAME].any():
        num_spiked_rows = qn_df[SPIKED_PARAMS_COLUMN_NAME].sum()
        qn_df = qn_df[qn_df[SPIKED_PARAMS_COLUMN_NAME] != True]

    # Check for low R2 smoothcalib values
    num_low_r2_rows = None
    if R2_SMOOTHCALIB_COLUMN_NAME in qn_df.columns:
        low_r2_mask = (qn_df[R2_SMOOTHCALIB_COLUMN_NAME].notna()) & (
            qn_df[R2_SMOOTHCALIB_COLUMN_NAME] < CALIB_R2_THRESHOLD
        )
        if low_r2_mask.any():
            num_low_r2_rows = low_r2_mask.sum()
            qn_df = qn_df[~low_r2_mask]

    return qn_df, num_spiked_rows, num_low_r2_rows, full_lookback_len


def _drop_and_merge_on_params_df(
    calc_type_df: pd.DataFrame,
    params_df: pd.DataFrame,
    merge_columns: list[str],
    extra_cols: list[str],
    calc_type: str,
) -> pd.DataFrame:
    """
    We drop columns from the calc_type_df as we want to copy over the values from params_df onto calc_type_df.
     We do this to prevent the suffixing of columns with x and y if the columns are present in both
     dataframes. We MUST preserve the indices of the calc_type_df as they match the indices in the
     lookback_dataframe we are trying to fill values for

    """

    # remove below logging block when missing params issue is resolved
    if len(params_df) <= len(calc_type_df):
        common_qualfied_name_prefixes = set(
            params_df["qualified_name_prefix"]
        ).intersection(set(calc_type_df["qualified_name_prefix"]))
        for group in common_qualfied_name_prefixes:
            params_timestamps = set(
                params_df[
                    params_df["qualified_name_prefix"] == group
                ].index.get_level_values("timestamp")
            )
            calc_type_timestamps = set(
                calc_type_df[
                    calc_type_df["qualified_name_prefix"] == group
                ].index.get_level_values("timestamp")
            )
            missing_in_calc_type = params_timestamps - calc_type_timestamps
            missing_in_params = calc_type_timestamps - params_timestamps

            timestamp_differences = []
            if missing_in_calc_type or missing_in_params:
                record = {
                    "calc_type": calc_type,
                    "max_params_timestamp": max(params_timestamps),
                    f"max_{calc_type}_timestamp": max(calc_type_timestamps),
                    "qualified_name_prefix": group,
                    f"missing_in_{calc_type}": list(missing_in_calc_type),
                    "missing_in_params": list(missing_in_params),
                }
                timestamp_differences.append(record)
                logging.warning(f"missing timestamp records; {record}")

    params_df.reset_index(inplace=True)
    calc_type_df.reset_index(inplace=True)

    # drop these column to use the "extra_cols" from the params df
    calc_type_df = drop_available_columns_from_df(
        df=calc_type_df,
        columns=extra_cols,
    )
    params_df = drop_available_columns_from_df(
        df=params_df,
        columns=["qualified_name"],
    )
    merged_df = pd.merge(
        params_df[[*merge_columns, *extra_cols]],
        calc_type_df,
        on=merge_columns,
    )
    merged_df = set_qualified_name_timestamp_index(df=merged_df)

    return merged_df


def _rename_values(
    col_name: str, suffix: str, calc_type: VolSurfaceCalcType
) -> float | int | str:
    if col_name.endswith(suffix):
        return undo_create_output_col(
            input_col=col_name, suffix=CALC_TYPE_TO_COL_SPACE[calc_type]
        )
    return col_name
