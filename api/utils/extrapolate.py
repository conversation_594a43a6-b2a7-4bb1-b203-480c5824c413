from typing import Mapping

import utils_calc
from calc_helpers import get_calibration_delta_boundary


def get_strikes_and_vols_to_extrapolate(
    expiry: float,
    forward: float,
    spot: float,
    strikes_to_vol: Mapping[float, float],
    r_d: float,
    use_calibration_delta_boundary: bool = True,
) -> tuple[list[float], list[float], list[float]]:

    put_wing_delta = 0.0
    call_wing_delta = 0.0
    if use_calibration_delta_boundary:
        delta_ranges = get_calibration_delta_boundary(expiry)
        put_wing_delta = delta_ranges["put_wing_boundary"]
        call_wing_delta = delta_ranges["call_wing_boundary"]

    relevant_strikes = []
    relevant_vols = []
    relevant_deltas = []

    for strike, vol in strikes_to_vol.items():
        delta = utils_calc.option_delta(
            s=spot,
            f=forward,
            K=strike,
            t=expiry,
            r_d=r_d,
            vol=vol,
            phi=-1 if strike < forward else 1,
        )

        if delta < put_wing_delta or delta > call_wing_delta:
            relevant_deltas.append(delta)
            relevant_strikes.append(strike)
            relevant_vols.append(vol)

    return relevant_strikes, relevant_vols, relevant_deltas
