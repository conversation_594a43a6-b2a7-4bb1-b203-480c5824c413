from math import inf
from typing import Literal, Optional, cast

import pandas as pd
import utils_general
from constants import UNIX_HOUR_NS, UNIX_MINUTE_NS
from lambda_types import (
    ClosestTenorInfo,
    QnsList,
    SmoothingCalcType,
    TenorType,
)

from utils.common import (
    activate_tenor_types,
    modify_string,
    round_value,
)


def extract_listed_params_from_df(
    df: pd.DataFrame, calc_types: list[SmoothingCalcType]
) -> pd.DataFrame:
    listed_df = pd.DataFrame()

    listed_bundle_df = df[df["qualified_name"].str.contains("listed")]
    for calc_type in calc_types:
        # extract listed expiries from listed_qn bundle if they are present
        if (
            any(df["qualified_name"].str.contains("listed"))
            and calc_type in df.columns
        ):
            calc_type_listed_bundle = listed_bundle_df[
                ~pd.isna(listed_bundle_df[calc_type])
            ]

            parsed_params = calc_type_listed_bundle[calc_type].apply(
                lambda x: modify_string(x)
            )
            temp_df = pd.concat(
                [
                    pd.DataFrame(item).assign(timestamp=ts)
                    for item, ts in zip(
                        parsed_params, calc_type_listed_bundle["timestamp"]
                    )
                ],
                ignore_index=True,
            )

            # Concatenate temp_df with listed_df
            temp_df = activate_tenor_types(
                df=temp_df,
                initialise_tenor_types=True,
                tenor_types_to_toggle=[TenorType.LISTED_EXPIRY],
            )
            listed_df = pd.concat([listed_df, temp_df], ignore_index=True)

    return listed_df


def generate_lookback_timestamps(
    end_str: str, lookback_window_size: int, interval: str
) -> set[int]:
    """
    Generate list of all timestamps for filtering lookback data.
    Eg.: minute frequency, hourly data shifted to current minute

    Starts at the lookback end that is provided and works backwards in time,
    creating all the timestamps needed for the looback.
    """

    end_int_ns = utils_general.from_iso(end_str).timestamp() * 1e9
    # rounding is done as the live system will operates as sub minute frequencies and
    # the lowest frequency of data stored is minutely

    if interval == "hour":
        interval_delta_ns = 3600 * 1e9
        round_to_nearest = UNIX_HOUR_NS
    elif interval == "minute":
        interval_delta_ns = 60 * 1e9
        round_to_nearest = UNIX_MINUTE_NS
    else:
        raise ValueError(f"Unsupported interval: {interval}")

    end_int_ns_rounded = int(
        round_value(
            value=end_int_ns,
            round_to_nearest=round_to_nearest,
            rounding_strategy="floor",
        )
    )
    lookback_tstamps = {
        int(end_int_ns_rounded - x * interval_delta_ns)
        for x in range(1, lookback_window_size + 1)
    }

    return lookback_tstamps


def get_closest_tenor(
    tenor_days: float, tenors: list[int | float]
) -> float | int:
    return min(tenors, key=lambda x: abs(x - tenor_days))


def get_closest_listed_expiry(
    df: Optional[pd.DataFrame], tenor: float
) -> tuple[float, str]:
    if df is None or df.empty:
        return inf, ""

    absolute_differences = df["tenor_days"].sub(tenor).abs()
    closest_index = absolute_differences.idxmin()

    closest_row = df.loc[closest_index]
    return (
        cast(float, closest_row["tenor_days"]),
        cast(str, closest_row["qualified_name"]),
    )


def get_calc_type_qns_from_qualified_name_prefix(
    calc_types: list[SmoothingCalcType], qualified_name_prefixs: list[str]
) -> list[str]:
    return [
        f"{key}.{calc}" for key in qualified_name_prefixs for calc in calc_types
    ]


def determine_closest_tenor(
    target_tenor_days: float,
    listed_expiry_df: Optional[pd.DataFrame],
    constant_tenors: list[int | float],
    exchange: str,
    instrument: str,
    currency: str,
    model: str,
    calc_type: SmoothingCalcType,
    freq: str,
    scheduled_version: Optional[str],
) -> ClosestTenorInfo:
    """
    Determines the closest tenor to the target from constant and listed tenors.

    :param target_tenor_days: The target tenor to compare against, in DAYS.
    :param listed_expiry_df: DataFrame containing listed expiry dates.
    :param constant_tenors: List of constant tenors in days.
    :param exchange: The exchange on which the instrument is listed.
    :param instrument: The instrument used to construct the constant tenor qn
    :param currency: The currency used to construct the constant tenor qn
    :param model: The model used to construct the constant tenor qn
    :param calc_type: The calc_type of the qn we want to construct. This is used when the closest tenor is
        a constant tenor.
    :param freq: The frequency of the tenor.
    :param scheduled_version: Optional version of the schedule.

    :return: A Dictionary containing details about the closest standard/listed tenors:
        - closest_tenor: The closest tenor to the target.
        - is_constant_tenor: Boolean indicating if the closest tenor is a constant tenor.
        - closest_qualified_name: The qualified name constructed based on the closest tenor and other parameters.
    """
    closest_constant_tenor = None
    if constant_tenors:
        closest_constant_tenor = get_closest_tenor(
            tenor_days=target_tenor_days, tenors=constant_tenors
        )
    (
        closest_listed_expiry_tenor,
        closest_qualified_name,
    ) = get_closest_listed_expiry(listed_expiry_df, target_tenor_days)

    closest_tenor = (
        closest_constant_tenor
        if closest_constant_tenor
        and abs(target_tenor_days - closest_constant_tenor)
        < abs(target_tenor_days - closest_listed_expiry_tenor)
        else closest_listed_expiry_tenor
    )

    if closest_tenor == closest_constant_tenor:
        tenor = utils_general.convert_tenor_days_to_constant_maturity(
            closest_tenor
        )
        qualified_name_parts = (
            [scheduled_version] if scheduled_version else []
        ) + [
            exchange,
            instrument,
            currency,
            model,
            tenor,
            freq,
            calc_type,
        ]
        closest_qualified_name = ".".join(map(str, qualified_name_parts))

    return {
        "closest_tenor": closest_tenor,
        "is_constant_tenor": closest_tenor == closest_constant_tenor,
        "closest_qualified_name": closest_qualified_name,
    }


def filter_qualified_names(
    df: pd.DataFrame,
    threshold: float | int,
    filter_type: Literal["below_threshold", "above_eq_threshold"],
) -> pd.DataFrame:
    """
    Filters dataframe rows grouped by 'qualified_name' based on a threshold on the number of datapoints.
    :param df: DataFrame to filter
    :param threshold: the threshold number of datapoints
    :param filter_type: type of filtering ('min' for less than, 'max' for greater than or equal)
    """

    if filter_type == "below_threshold":
        return df.groupby("qualified_name").filter(lambda x: len(x) < threshold)
    elif filter_type == "above_eq_threshold":
        return df.groupby("qualified_name").filter(
            lambda x: len(x) >= threshold
        )
    else:
        raise ValueError(
            "filter_type must be either 'below_threshold' or 'above_eq_threshold'"
        )


def get_qualified_name_prefixs_from_qns(
    data: QnsList,
) -> QnsList:
    """
    Generates qualified_name_prefixs from qualified names by stripping out their calc_types
    If a list is passed in, then a list of unique qualified_name_prefixs are output
    """

    if isinstance(data, pd.Series):
        return cast(
            pd.Series,  # type: ignore
            data.str.rsplit(".", 1).str[0],  # type:ignore
        )
    elif isinstance(data, list):
        return list(
            {name.rsplit(".", 1)[0] if "." in name else name for name in data}
        )
    else:
        raise TypeError("Input must be a pandas Series or a list of strings.")


def extract_constant_tenors_from_maturities(
    df: pd.DataFrame,
) -> list[int | float]:

    # get all available constant tenors
    available_standard_constant_maturities = (
        df[df[TenorType.STANDARD_CONSTANT_MATURITY.value].eq(True)][
            "tenor_days"
        ]
        .unique()
        .tolist()
    )
    available_arbitrary_constant_maturities = (
        df[df[TenorType.ARBITRARY_CONSTANT_MATURITY.value].eq(True)][
            "tenor_days"
        ]
        .unique()
        .tolist()
    )
    return [
        *available_arbitrary_constant_maturities,
        *available_standard_constant_maturities,
    ]
