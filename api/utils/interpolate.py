import logging
from typing import cast

import numpy as np
import pandas as pd
from calc_helpers import (
    get_atm_deltas_from_boundaries,
    get_calibration_delta_boundary,
)
from constants import load_domestic_rates
from lambda_types import (
    ModelParamsBase,
    NDArrayFloat64,
    SingleParams,
    StrikesToInterpolateResult,
)
from utils_calc import DEFAULT_SURFACE_MONEYNESS as MONEYNESS
from utils_calc import Model, extract_model_params, get_domestic_rate

from .surface import estimate_strike_from_delta, validate_estimated_strikes


def get_delta_implied_strike_grid_for_expiry(
    params: ModelParamsBase,
    spot: float,
    forward: float,
    expiry: float,
    model: Model,
    exchange: str,
) -> list[float] | NDArrayFloat64:
    delta_ranges = get_calibration_delta_boundary(expiry)
    relevant_deltas = get_atm_deltas_from_boundaries(
        put_wing_delta=delta_ranges["put_wing_boundary"],
        call_wing_delta=delta_ranges["call_wing_boundary"],
    )

    strike_list_filtered, _ = estimate_valid_strike_list_from_deltas(
        params=params,
        spot=spot,
        forward=forward,
        expiry=expiry,
        model=model,
        exchange=exchange,
        deltas=relevant_deltas,
    )

    strike_list_filtered = sorted(set(strike_list_filtered))

    return strike_list_filtered


def get_strikes_to_interpolate(
    upper_expiry: SingleParams,
    lower_expiry: SingleParams,
    spot: float,
    model: Model,
    interpolated_forward: float,
    exchange: str,
) -> StrikesToInterpolateResult:
    """
    The strikes used to interpolate an expiry, will be based on the delta range of the
    upper-bounding (right-side) listed expiry. The strikes returned will be strikes
    that fall within the delta boundary of the upper expiry.
    """
    model_params = extract_model_params(upper_expiry, model)

    upper_bordering_relevant_strikes = get_delta_implied_strike_grid_for_expiry(
        params=model_params,
        spot=spot,
        forward=cast(float, upper_expiry["forward"]),
        expiry=cast(float, upper_expiry["expiry"]),
        model=model,
        exchange=exchange,
    )

    if len(upper_bordering_relevant_strikes) < 5:
        logging.warning(
            "Not enough delta estimated strikes to calibrate on. Falling back to standard moneyness grid"
        )
        upper_bordering_relevant_strikes = [
            cast(float, upper_expiry["forward"]) * money for money in MONEYNESS
        ]

    monies_to_interpolate = (
        np.array(upper_bordering_relevant_strikes) / upper_expiry["forward"]
    )

    interpolated_tenor_strikes = monies_to_interpolate * interpolated_forward
    lower_bordering_strikes = monies_to_interpolate * lower_expiry["forward"]
    upper_bordering_strikes = monies_to_interpolate * upper_expiry["forward"]

    return (
        cast(NDArrayFloat64, lower_bordering_strikes),
        cast(NDArrayFloat64, interpolated_tenor_strikes),
        cast(NDArrayFloat64, upper_bordering_strikes),
    )


def estimate_valid_strike_list_from_deltas(
    params: ModelParamsBase,
    spot: float,
    forward: float,
    expiry: float,
    model: Model,
    exchange: str,
    deltas: list[float],
) -> tuple[list[float], list[float]] | tuple[NDArrayFloat64, NDArrayFloat64]:
    strike_list = []
    for delta in deltas:
        ini, _ = estimate_strike_from_delta(
            delta,
            pd.Series(params),
            spot,
            forward,
            expiry,
            model,
            get_domestic_rate(load_domestic_rates(), expiry * 365),
            forward * MONEYNESS[(0 if delta < 0 else -1)],
        )
        strike_list.append(ini)

    moneyness_filtered_strikes_and_deltas = validate_estimated_strikes(
        forward=forward,
        strikes_list=strike_list,
        deltas=deltas,
    )

    if moneyness_filtered_strikes_and_deltas:
        strikes_filtered, deltas_filtered = (
            moneyness_filtered_strikes_and_deltas["strikes"],
            moneyness_filtered_strikes_and_deltas["deltas"],
        )
        removed_deltas = sorted(set(deltas) - set(deltas_filtered))

        logging.warning(
            f"Encountered strikes that are unreasonably large for {exchange=}, {model=}, {expiry=}, {spot=}, {forward=}, "
            f"{removed_deltas=} "
            f"Filtering out badly estimated deltas"
        )

        return strikes_filtered, deltas_filtered

    return strike_list, deltas
