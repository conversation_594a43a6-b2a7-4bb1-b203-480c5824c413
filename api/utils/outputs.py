import decimal
import logging
import math
from typing import Any, Optional

import numpy as np
import orjson
import pandas as pd
import utils_general
from constants import (
    COL_SPACE_TO_OUTPUT_SCALING_FACTOR,
    LOOKBACK_COLUMN_NAME,
    OUTPUT_COL_SCALING_FACTOR,
    SPIKED_PARAMS_COLUMN_NAME,
    TENOR_TYPES,
)
from lambda_types import (
    BaseCalcResult,
    DerivedCalcResult,
    ExchangeCurrencyToTimestampTenorExpiryArbFields,
    FinalCalcResult,
    ParamsCalcResult,
    TenorType,
    VolSurfaceCalcType,
)
from utils_calc import Model

from utils.common import (
    _expand_arb_lookup_df,
    _merge_with_arb_lookup_df,
    is_derived_calc_result,
    is_params_calc_result,
    set_qualified_name_timestamp_index,
)


def collect_final_results(
    calculation_results: list[ParamsCalcResult | DerivedCalcResult],
    vol_surface_calc_types: list[VolSurfaceCalcType],
) -> list[FinalCalcResult]:
    """
    Aggregates the results of the params surface and the Volsurfaces into a final results object.
    It uses a dictionary keyed by a tuple containing the keys of BaseCalcResult, to aggregate the
    params surfaces and volsurfaces of each key into a final result

    """
    BaseKey = tuple[Model, str, str, int]
    final_calc_dict: dict[BaseKey, FinalCalcResult] = {}

    def _get_base_result_key(
        result: BaseCalcResult,
    ) -> BaseKey:
        return (
            result["model"],
            result["exchange"],
            result["currency"],
            result["timestamp"],
        )

    for result in calculation_results:
        key = _get_base_result_key(result)
        if key not in final_calc_dict:
            final_calc_dict[key] = {
                "model": result["model"],
                "exchange": result["exchange"],
                "currency": result["currency"],
                "timestamp": result["timestamp"],
                "dfs": {
                    "params": pd.DataFrame(),
                    "smile": pd.DataFrame(),
                    "moneyness": pd.DataFrame(),
                },
            }

        final_dfs = final_calc_dict[key]["dfs"]

        if is_params_calc_result(result):
            final_dfs["params"] = result["dfs"]["params"]
        elif is_derived_calc_result(result):
            for ct in vol_surface_calc_types:
                final_dfs[ct] = result["dfs"][ct]

    final_calc_results = list(final_calc_dict.values())
    return final_calc_results


def group_listed_expiries(
    df: pd.DataFrame, estimate_params: bool
) -> pd.DataFrame:
    combined_df = pd.DataFrame(
        columns=[
            "qualified_name",
            "runtime",
            "timestamp",
            "isodate",
            LOOKBACK_COLUMN_NAME,
            SPIKED_PARAMS_COLUMN_NAME,
            "butterfly",
            "api_version",
            "moneyness",
            "params",
            "smile",
            "skew",
            "strike",
            "smoothed",
        ]
    )
    tenor_df = df[df[TenorType.LISTED_EXPIRY.value].eq(False)]
    listed = df[df[TenorType.LISTED_EXPIRY.value].eq(True)]
    columns_to_drop = [
        "runtime",
        "isodate",
        "api_version",
        "exchange",
        "ccy",
        "model",
        "freq",
        "calc_type",
        "timestamp",
        *[t.value for t in TENOR_TYPES],
    ]
    some_qn = str(df["qualified_name"].iloc[0])
    version = some_qn.split(".")[0] if some_qn.startswith("v-") else ""

    if LOOKBACK_COLUMN_NAME in listed:
        current = listed[~listed[LOOKBACK_COLUMN_NAME].eq(True)]
        columns_to_drop.append(LOOKBACK_COLUMN_NAME)
    else:
        current = listed

    for i, ts_df in current.groupby("timestamp"):
        assert isinstance(i, int)
        cols_to_drop = ["option", "date_time"]
        qn_token_cols = [
            "exchange",
            "option",
            "ccy",
            "model",
            "date_time",
            "freq",
            "calc_type",
        ]
        if version:
            qn_token_cols.insert(0, "version")
            cols_to_drop.insert(0, "version")
        version_prefix = version + "." if version else ""

        ts_df[qn_token_cols] = ts_df["qualified_name"].str.split(
            ".", expand=True
        )
        ts_df.drop(
            cols_to_drop,
            axis=1,
            inplace=True,
        )  # not needed

        # Group by the extracted columns
        for (exchange, calc_type, ccy, freq, model), g in ts_df.groupby(
            ["exchange", "calc_type", "ccy", "freq", "model"]
        ):

            row = g.iloc[0]
            expiry_qn_idx = 5 if version else 4
            g["expiry_str"] = g["qualified_name"].str.split(".", expand=True)[
                [expiry_qn_idx]
            ]

            if "smoothed" in g:
                g["smoothed"].fillna(value=False, inplace=True)
            if (
                calc_type != "strike"
            ):  # Strikes may vary across expiry so best to keep all and filter accordingly
                g.dropna(axis="columns", inplace=True)

            present_cols_to_drop = list(
                set(columns_to_drop).intersection(g.columns)
            )
            g.drop(present_cols_to_drop, axis=1, inplace=True)
            rowdict = g.to_dict(orient="records")
            if calc_type == "strike":  # Remove Nan strikes
                valid_rows: list[dict[Any, Any]] = []

                def is_valid(value: Any) -> bool:
                    """
                    Check if the value is not NaN and not None. Assumes non-numeric values are valid.
                    We consider them valid incase there are some boolean or string values we pass through with .strike.
                    """
                    # Check if value is numeric (int or float)
                    if isinstance(value, (int, float)):
                        return not math.isnan(value)
                    # Non-numeric values are considered valid
                    if not value:
                        return False
                    return True

                for r in rowdict:
                    # Some values are invalid as the dataframe contains strikes for all listed expiries.
                    # Where as some strikes may not propogate across all.
                    valid_row = {k: v for k, v in r.items() if is_valid(v)}
                    valid_rows.append(valid_row)
                rowdict = valid_rows
            record = {
                "qualified_name": f"{version_prefix}{exchange}.option.{ccy}.{model}.listed.{freq}.{calc_type}",
                "timestamp": i,
                "runtime": row["runtime"],
                "isodate": row["isodate"],
                "has_smoothed_element": any(row["smoothed"] for row in rowdict),
                calc_type: orjson.dumps(
                    rowdict, option=orjson.OPT_SERIALIZE_NUMPY
                ).decode("utf-8"),
                LOOKBACK_COLUMN_NAME: False,
            }
            if calc_type == "params" and estimate_params:
                record[f"has_{SPIKED_PARAMS_COLUMN_NAME}_element"] = any(
                    row[SPIKED_PARAMS_COLUMN_NAME] for row in rowdict
                )
            series = pd.Series(record)
            combined_df = combined_df.append(series, ignore_index=True)  # type: ignore
    return pd.concat(objs=[combined_df, tenor_df], copy=False, sort=False)


def drop_non_listed_strikes(df: pd.DataFrame) -> pd.DataFrame:
    return df[
        ~(
            (df["qualified_name"].str.endswith("strike"))
            & (df[TenorType.STANDARD_CONSTANT_MATURITY.value].eq(True))
        )
    ]


def create_output_col(
    suffix: str, col_str: str, scaling_factor: Optional[int] = None
) -> str:
    if scaling_factor is None:
        if suffix in COL_SPACE_TO_OUTPUT_SCALING_FACTOR:
            scaling_factor = COL_SPACE_TO_OUTPUT_SCALING_FACTOR[suffix]
        else:
            scaling_factor = OUTPUT_COL_SCALING_FACTOR

    decimal_scaling_factor = decimal.Decimal(str(scaling_factor))
    safe_decimal = float(decimal_scaling_factor * decimal.Decimal(col_str))

    return (
        f"{utils_general.decimal_to_string(value=safe_decimal, suffix=suffix)}"
    )


def undo_create_output_col(
    input_col: str, suffix: str, scaling_factor: Optional[int] = None
) -> float:
    if scaling_factor is None:
        if suffix in COL_SPACE_TO_OUTPUT_SCALING_FACTOR:
            scaling_factor = COL_SPACE_TO_OUTPUT_SCALING_FACTOR[suffix]
        else:
            scaling_factor = OUTPUT_COL_SCALING_FACTOR

    if not input_col.endswith(suffix):
        raise ValueError(f"Input string must end with '{suffix}'.")

    numeric_scaled, _ = utils_general.string_to_decimal(
        val=input_col, suffix_to_remove=suffix
    )
    original_value = float(numeric_scaled) / scaling_factor

    return original_value


def drop_available_columns_from_df(
    df: pd.DataFrame, columns: list[str]
) -> pd.DataFrame:

    df.drop(
        list(
            set(columns).intersection(df.columns)
        ),  # drop those that are available
        axis="columns",
        inplace=True,
    )
    return df


def handle_tenor_intersections(
    results: list[ParamsCalcResult | DerivedCalcResult],
    exchange_curr_timestamp_to_expiry_info: ExchangeCurrencyToTimestampTenorExpiryArbFields,
    vol_surface_calc_types: list[VolSurfaceCalcType],
    freq: str,
    include_listed_expiries: bool,
    version: str,
) -> list[ParamsCalcResult | DerivedCalcResult]:
    for calc_type_result in results:
        exchange: str = calc_type_result["exchange"]
        model: str = calc_type_result["model"]
        currency: str = calc_type_result["currency"]
        timestamp: int = calc_type_result["timestamp"]

        expiry_iv_info = exchange_curr_timestamp_to_expiry_info[exchange][
            currency
        ][timestamp]
        arb_lookup_df = (
            pd.DataFrame.from_dict(expiry_iv_info, orient="index")  # type: ignore
            .reset_index()
            .rename(columns={"index": "tenor_days"})
        )
        expanded_arb_lookup_df = _expand_arb_lookup_df(arb_lookup_df)

        if is_derived_calc_result(calc_type_result):
            for _calc_type in vol_surface_calc_types:
                calc_type_result["dfs"][_calc_type] = _merge_with_arb_lookup_df(
                    df=calc_type_result["dfs"][_calc_type],
                    arb_lookup_df=expanded_arb_lookup_df,
                    calc_type=_calc_type,
                    exchange=exchange,
                    currency=currency,
                    model=model,
                    freq=freq,
                    version=version,
                    timestamp=timestamp,
                )
                if (
                    _calc_type == "strike"
                    and include_listed_expiries
                    and freq != "live"
                ):
                    calc_type_result["dfs"][_calc_type] = (
                        drop_non_listed_strikes(
                            calc_type_result["dfs"][_calc_type]
                        )
                    )

        elif is_params_calc_result(calc_type_result):
            calc_type_result["dfs"]["params"] = _merge_with_arb_lookup_df(
                df=calc_type_result["dfs"]["params"],
                arb_lookup_df=expanded_arb_lookup_df,
                calc_type="params",
                exchange=exchange,
                currency=currency,
                model=model,
                freq=freq,
                version=version,
                timestamp=timestamp,
            )

    return results


def combine_new_lookback_with_previous_lookback(
    old_lookback: pd.DataFrame, new_lookback: pd.DataFrame
) -> pd.DataFrame:

    old_lookback_copy = old_lookback.copy()
    new_lookback_copy = new_lookback.copy()

    if not isinstance(old_lookback_copy.index, pd.MultiIndex):
        logging.warning(
            "Old lookback dataframe does not have a multi-level index, creating a multi-level index"
        )
        old_lookback_copy = set_qualified_name_timestamp_index(
            df=old_lookback_copy
        )

    if not isinstance(new_lookback_copy.index, pd.MultiIndex):
        logging.warning(
            "New lookback dataframe does not have a multi-level index, creating a multi-level index"
        )
        new_lookback_copy = set_qualified_name_timestamp_index(
            df=new_lookback_copy
        )

    # Combined_df will inherit new columns introduces by new_lookback
    # It will also inherit any non nan values from new_lookback, that were
    # nans in old_lookback
    combined_df = old_lookback_copy.combine_first(new_lookback_copy)

    return combined_df


def serialize_columns(
    df: pd.DataFrame, column_names: list[str]
) -> pd.DataFrame:
    """
    Serialize specified columns containing objects in a dataframe for database storage.

    This function handles columns which contain objects that need to be serialized
    using orjson with numpy support before storing to the database.

    Args:
        df: DataFrame potentially containing columns to serialize
        column_names: List of column names to serialize

    Returns:
        DataFrame with specified columns serialized (if present)
    """
    # Filter to only columns that actually exist in the dataframe

    existing_columns = [col for col in column_names if col in df.columns]

    if not existing_columns:
        return df

    df_copy = df.copy()

    def serialize_value(value: Any) -> Any:
        if value is None or value is pd.NA:
            return value
        if isinstance(value, (float, np.floating)) and np.isnan(value):
            return value

        return orjson.dumps(value, option=orjson.OPT_SERIALIZE_NUMPY).decode(
            "utf-8"
        )

    for col in existing_columns:
        mask = df_copy[col].notna()
        df_copy.loc[mask, col] = df_copy.loc[mask, col].apply(serialize_value)

    return df_copy
