AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: Block Scholes Volatility Smile Calc

Resources:
  volSmileCalcFunction:
    Type: AWS::Serverless::Function # More info about Function Resource: https://github.com/awslabs/serverless-application-model/blob/master/versions/2016-10-31.md#awsserverlessfunction
    Properties:
      PackageType: Image
      CodeUri: ./api
      Timeout: 200
      MemorySize:  3009
      Policies:
        - arn:aws:iam::273532302533:policy/allServicesDynamoDBPolicy
        - arn:aws:iam::273532302533:policy/LambdaFunctionsS3Policy
        - arn:aws:iam::273532302533:policy/CalcSSMReadPolicy
      Environment:
        Variables:
          LOG_LEVEL: INFO
          SSM_INDEX_CONFIG_PATH: /config/price_indices_generated
      Architectures:
        - arm64
    Metadata:
      Dockerfile: Dockerfile
      DockerContext: ./api
      DockerTag: latest

  volSmileCalcErrorMetricFilter:
    Type: AWS::Logs::MetricFilter
    DeletionPolicy: Retain
    Properties:
      LogGroupName: !Sub "/aws/lambda/${volSmileCalcFunction}"
      FilterName: "VolSmileCalc_Lambda_Errors"
      FilterPattern: "ERROR"
      MetricTransformations:
        - MetricValue: "1"
          MetricNamespace: "CalcLambdas"
          MetricName: "VolSmileCalc_Lambda_Errors"
          Unit: "Count"

Outputs:
  volSmileCalcFunction:
    Description: "volSmileCalc Lambda Function ARN"
    Value: !GetAtt volSmileCalcFunction.Arn
  volSmileCalcFunctionIamRole:
    Description: "Implicit IAM Role created for volSmileCalc function"
    Value: !GetAtt volSmileCalcFunctionRole.Arn
