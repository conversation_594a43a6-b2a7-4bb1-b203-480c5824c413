{"containerDefinitions": [{"name": "volSmileFlexStream-container", "image": "public.ecr.aws/docker/library/python:3.11", "cpu": 0, "command": ["dumb-init", "python", "-m", "run_stream_flex"], "essential": true, "environment": [{"name": "frequency_seconds", "value": "20"}, {"name": "tenor_mode", "value": "standard"}, {"name": "METRIC_S", "value": "60"}, {"name": "scheduled_version", "value": "v-00004"}, {"name": "smooth", "value": "True"}, {"name": "estimate_params", "value": "True"}, {"name": "MIN_KINESIS_FETCH_DELAY_S", "value": "1"}, {"name": "ECS_CLUSTER", "value": "Production"}, {"name": "ECS_SERVICE", "value": "volSmileFlexStream-service"}, {"name": "FIXED_TARGET_JOBS", "value": "[\"v2composite.BTC.SVI\", \"v2composite.ETH.SVI\"]"}], "mountPoints": [], "volumesFrom": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/volSmileFlexStream-containers-prod1a", "awslogs-region": "eu-west-2", "awslogs-stream-prefix": "ecs", "awslogs-datetime-format": "%Y-%m-%d %H:%M:%S"}}}], "family": "volSmileFlexStream-task-definition", "taskRoleArn": "arn:aws:iam::685767522279:role/volSmileStreamContainerRole", "executionRoleArn": "arn:aws:iam::685767522279:role/ecsTaskExecutionRole", "networkMode": "awsvpc", "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["FARGATE"], "cpu": "16384", "memory": "32768", "runtimePlatform": {"operatingSystemFamily": "LINUX", "cpuArchitecture": "ARM64"}}