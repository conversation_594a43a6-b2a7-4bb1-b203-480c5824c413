FROM python:3.11

# Set application working directory
WORKDIR /home

# Install requirements
COPY api/ ./
COPY stream/ ./

RUN python -m pip install -r requirements.txt

# Install dumb-init
RUN apt-get update && apt-get install -y dumb-init && apt-get clean

# Run application using dumb-init, taking care of properly handle and forward signals as they are received: https://github.com/Yelp/dumb-init
CMD ["dumb-init", "python", "run_stream.py"]
