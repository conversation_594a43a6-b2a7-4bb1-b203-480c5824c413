# volSmileCalc

Lambda to create volatility smiles

# Accepted input format and parameters

"calc":{
"version":"1.0.0",
"type":"volSmileCalc",
"args":{
"exchange": "deribit",
"currency":"BTC",
"price_frequency": "1h",
"model": "SABR",
"type": "delta",
"date_range": {
"start": "2022-02-01T00:00:00.000Z",
"end": "2022-03-11T23:00:00.000Z"
}
},
"output":{
"type":"csv",
"format": "timeseries",
"store": true,
"s3Bucket": "vol-surface-matrix",
}
}

# Runs in two ways:

1. Produce S3 Data files - Will run for the given date range and write preformated csv files to the S3 s3_bucket indicated.
   The files will be writen to the folder data/model/currency/type and have the format: model-type-currency-datetime.

   Use this ouptput format to write files to the vol-surface-s3_bucket.

   "output":{
   "type":"csv",
   "format": "timeseries",
   "store": true,
   "s3Bucket": "vol-surface-matrix",
   }
   }

2. Output JSON meant for updating associated timeseries
   Use this format to produce LATEST output JSON

   "calc":{
   "version":"1.0.0",
   "type":"volSmileCalc",
   "args":{
   "exchange": "deribit",
   "currency":"BTC",
   "price_frequency": "1h",
   "model": "SABR",
   "type": "delta",
   "Span": "LATEST"
   "date_range": {
   "start": "",
   "end": ""
   }
   },
   "output":{
   "type":"json",
   "format": "timeseries",
   }
   }

AWS ECR log in : aws ecr get-login-password --region eu-west-2 | docker login --username AWS \
--password-stdin 844157133169.dkr.ecr.eu-west-2.amazonaws.com
