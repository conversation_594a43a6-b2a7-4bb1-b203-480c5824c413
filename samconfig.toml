version = 0.1
[default]
[default.deploy]
[default.deploy.parameters]
stack_name = "volsmilecalc"
s3_bucket = "aws-sam-cli-managed-default-samclisourcebucket-y2905bqlhajs"
s3_prefix = "volsmilecalc"
region = "eu-west-2"
confirm_changeset = false
capabilities = "CAPABILITY_IAM"
image_repository= "844157133169.dkr.ecr.eu-west-2.amazonaws.com/vol_smile_calc"

[staging.deploy.parameters]
stack_name = "VolSmileCalcLambdaFunctionStack"
s3_bucket = "blockscholes-staging-lambda-deploys"
s3_prefix = "vol-smile-calc-function"
region = "eu-west-2"
confirm_changeset = false
capabilities = "CAPABILITY_IAM"
image_repository= "273532302533.dkr.ecr.eu-west-2.amazonaws.com/vol-smile-calc-function"

[prod.deploy.parameters]
stack_name = "VolSmileCalcLambdaFunctionStack"
s3_bucket = "blockscholes-production-lambda-deploys"
s3_prefix = "vol-smile-calc-function"
region = "eu-west-2"
confirm_changeset = false
capabilities = "CAPABILITY_IAM"
image_repository= "685767522279.dkr.ecr.eu-west-2.amazonaws.com/vol-smile-calc-function"

[prod1b.deploy.parameters]
stack_name = "VolSmileCalcLambdaFunctionStack"
s3_bucket = "blockscholes-production1b-lambda-deploys"
s3_prefix = "vol-smile-calc-function"
region = "eu-west-1"
confirm_changeset = false
capabilities = "CAPABILITY_IAM"
image_repository= "685767522279.dkr.ecr.eu-west-1.amazonaws.com/vol-smile-calc-function"
